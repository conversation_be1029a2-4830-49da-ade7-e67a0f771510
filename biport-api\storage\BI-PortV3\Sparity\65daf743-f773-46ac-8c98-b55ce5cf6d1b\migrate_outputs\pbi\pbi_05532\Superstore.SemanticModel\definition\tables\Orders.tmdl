table Orders
	lineageTag: c341a474-fe2e-468f-b67d-2f8a455d797e

	column 'Row ID'
		dataType: int64
		formatString: 0
		lineageTag: b31e94e8-7282-4cbd-b1f8-b650dd4ee70a
		summarizeBy: count
		sourceColumn: Row ID

		annotation SummarizationSetBy = Automatic

	column 'Order ID'
		dataType: string
		lineageTag: c196e425-2829-426f-a4c2-3adbe52fe059
		summarizeBy: none
		sourceColumn: Order ID

		annotation SummarizationSetBy = Automatic

	column 'Order Date'
		dataType: dateTime
		formatString: Long Date
		lineageTag: 77ecefc1-c28d-4c0e-ad95-7b83ec50301f
		summarizeBy: none
		sourceColumn: Order Date

		variation Variation
			isDefault
			relationship: d6c52428-f233-4866-864e-1f40285310ef
			defaultHierarchy: LocalDateTable_bda25c00-86aa-4cf8-97cd-4fb25207fc89.'Date Hierarchy'

		annotation SummarizationSetBy = Automatic

		annotation UnderlyingDateTimeDataType = Date

	column 'Ship Date'
		dataType: dateTime
		formatString: Long Date
		lineageTag: 99d1faa9-ef96-4597-844c-fd44da45c815
		summarizeBy: none
		sourceColumn: Ship Date

		variation Variation
			isDefault
			relationship: 53a28b60-84e1-480e-b7fb-c289e07b7814
			defaultHierarchy: LocalDateTable_f9e0793f-437c-464c-b232-81d708e89292.'Date Hierarchy'

		annotation SummarizationSetBy = Automatic

		annotation UnderlyingDateTimeDataType = Date

	column 'Ship Mode'
		dataType: string
		lineageTag: 51cd0c23-6124-4adc-91b5-f0a168e4017b
		summarizeBy: none
		sourceColumn: Ship Mode

		annotation SummarizationSetBy = Automatic

	column 'Customer ID'
		dataType: string
		lineageTag: b140f87c-fdc1-46e3-bfb3-52f83a479894
		summarizeBy: none
		sourceColumn: Customer ID

		annotation SummarizationSetBy = Automatic

	column 'Customer Name'
		dataType: string
		lineageTag: d2e720bc-6a3c-41e5-a311-217f68b8ee4a
		summarizeBy: none
		sourceColumn: Customer Name

		annotation SummarizationSetBy = Automatic

	column Segment
		dataType: string
		lineageTag: e256edc2-6e9a-40c1-90bd-39333ba10981
		summarizeBy: none
		sourceColumn: Segment

		annotation SummarizationSetBy = Automatic

	column Country/Region
		dataType: string
		lineageTag: 91414ac7-d0e2-46d3-977b-37793eec95c5
		summarizeBy: none
		sourceColumn: Country/Region

		annotation SummarizationSetBy = Automatic

	column City
		dataType: string
		lineageTag: 717288a6-49be-4981-ace9-f75a4e97b7a3
		summarizeBy: none
		sourceColumn: City

		annotation SummarizationSetBy = Automatic

	column State/Province
		dataType: string
		lineageTag: 7962e48a-ee9e-4a71-8749-6f8f828a0331
		summarizeBy: none
		sourceColumn: State/Province

		annotation SummarizationSetBy = Automatic

	column 'Postal Code'
		dataType: string
		lineageTag: 7e172514-1f11-41e8-abda-f5ccaf79c069
		summarizeBy: none
		sourceColumn: Postal Code

		annotation SummarizationSetBy = Automatic

	column Region
		dataType: string
		lineageTag: 6d97adde-518b-4d5b-a403-94a6d79b9879
		summarizeBy: none
		sourceColumn: Region

		annotation SummarizationSetBy = Automatic

	column 'Product ID'
		dataType: string
		lineageTag: d39c39b1-a5d9-4b2b-9657-bc9828877cc4
		summarizeBy: none
		sourceColumn: Product ID

		annotation SummarizationSetBy = Automatic

	column Category
		dataType: string
		lineageTag: b8ed580d-347f-45c9-a865-47ca5ec4ac4c
		summarizeBy: none
		sourceColumn: Category

		annotation SummarizationSetBy = Automatic

	column Sub-Category
		dataType: string
		lineageTag: 488b9c6d-b9e2-4ed6-a566-ed3de9617b75
		summarizeBy: none
		sourceColumn: Sub-Category

		annotation SummarizationSetBy = Automatic

	column 'Product Name'
		dataType: string
		lineageTag: 5971222c-91e4-464b-93e8-19e696e3d731
		summarizeBy: none
		sourceColumn: Product Name

		annotation SummarizationSetBy = Automatic

	column Sales
		dataType: double
		formatString: \$#,0;(\$#,0);\$#,0
		lineageTag: 582797af-7627-4f11-b1fa-379fa54c8b15
		summarizeBy: sum
		sourceColumn: Sales

		annotation SummarizationSetBy = Automatic

	column Quantity
		dataType: int64
		formatString: #,0
		lineageTag: 2fbc6e7b-c219-410b-aa30-81cdec8ad581
		summarizeBy: sum
		sourceColumn: Quantity

		annotation SummarizationSetBy = Automatic

	column Discount
		dataType: double
		formatString: 0.00%;-0.00%;0.00%
		lineageTag: 004bea3b-a7fa-476d-9b64-141ed8422623
		summarizeBy: sum
		sourceColumn: Discount

		annotation SummarizationSetBy = Automatic

	column Profit
		dataType: double
		formatString: \$#,0;(\$#,0);\$#,0
		lineageTag: 9d65874f-42ac-4153-aef2-7eb971efd92a
		summarizeBy: sum
		sourceColumn: Profit

		annotation SummarizationSetBy = Automatic

		annotation PBI_FormatHint = {"currencyCulture":"en-US"}

	column OrderProfitable = ```
			
			VAR TotalProfit = CALCULATE(SUM('Orders'[Profit]), ALLEXCEPT('Orders', 'Orders'[Order ID]))
			RETURN IF(TotalProfit > 0, 1, 0)
			
			```
		formatString: 0
		lineageTag: f886a1d1-8aac-45ad-8b19-3d44eb4629a5
		summarizeBy: sum

		annotation SummarizationSetBy = Automatic

	column 'Order Profitable?' = SWITCH(Orders[OrderProfitable],1,"Profitable",0,"Unprofitable")
		lineageTag: 0600856e-7ddf-4a32-9757-94291f8a3007
		summarizeBy: none

		annotation SummarizationSetBy = Automatic

	column 'Profit Ratio' = ```
			
			VAR Ratio = DIVIDE(SUM('Orders'[Profit]), SUM('Orders'[Sales]), 0)  
			RETURN Ratio
			
			```
		formatString: 0.0%;-0.0%;0.0%
		lineageTag: 8238c9be-1b84-4f1d-8c3e-0326b0a990fc
		summarizeBy: sum

		annotation SummarizationSetBy = Automatic

	column 'Days to Ship Actual' = ```
			DATEDIFF('Orders'[Order Date], 'Orders'[Ship Date], DAY)
			
			```
		formatString: 0
		lineageTag: f5e42ea0-b51d-4a12-88a0-e444be762d76
		summarizeBy: sum

		annotation SummarizationSetBy = Automatic

	column 'Days to Ship Scheduled' = ```
			
			SWITCH(
			    'Orders'[Ship Mode], 
			    "Same Day", 0, 
			    "First Class", 1, 
			    "Second Class", 3, 
			    "Standard Class", 6
			)
			
			```
		formatString: 0
		lineageTag: de2efcef-61f7-4cad-9b55-b335019ae412
		summarizeBy: sum

		annotation SummarizationSetBy = Automatic

	column 'Ship Status' = ```
			
			IF('Orders'[Days to Ship Actual] > 'Orders'[Days to Ship Scheduled], "Shipped Late", 
			    IF('Orders'[Days to Ship Actual] = 'Orders'[Days to Ship Scheduled], "Shipped On Time", 
			        "Shipped Early"
			    )
			)
			
			```
		lineageTag: 6a79b6ba-430a-4f86-beec-57da950c199a
		summarizeBy: none

		annotation SummarizationSetBy = Automatic

	column 'Profit per Order' = Sum([Profit])/DISTINCTCOUNT(Orders[Order ID])
		formatString: \$#,0.###############;(\$#,0.###############);\$#,0.###############
		lineageTag: 616fbdfd-c70b-4c37-87c2-f17dbc03271e
		summarizeBy: sum

		annotation SummarizationSetBy = Automatic

	column 'Sales per Customer' = ```
			
			DIVIDE(SUM('Orders'[Sales]), DISTINCTCOUNT('Orders'[Customer Name]), 0)
			
			```
		lineageTag: bc1549b8-306e-4065-99af-254a3501fe68
		summarizeBy: sum

		annotation SummarizationSetBy = Automatic

		annotation PBI_FormatHint = {"isGeneralNumber":true}

	partition Orders = m
		mode: import
		source =
				let
				    Source = Excel.Workbook(File.Contents("C:\Users\<USER>\Downloads\Sample - Superstoree.xlsx"), null, true),
				    Orders_Sheet = Source{[Item="Orders",Kind="Sheet"]}[Data],
				    #"Promoted Headers" = Table.PromoteHeaders(Orders_Sheet, [PromoteAllScalars=true]),
				    #"Changed Type" = Table.TransformColumnTypes(#"Promoted Headers",{{"Row ID", Int64.Type}, {"Order ID", type text}, {"Order Date", type date}, {"Ship Date", type date}, {"Ship Mode", type text}, {"Customer ID", type text}, {"Customer Name", type text}, {"Segment", type text}, {"Country/Region", type text}, {"City", type text}, {"State/Province", type text}, {"Postal Code", type text}, {"Region", type text}, {"Product ID", type text}, {"Category", type text}, {"Sub-Category", type text}, {"Product Name", type text}, {"Sales", type number}, {"Quantity", Int64.Type}, {"Discount", type number}, {"Profit", type number}})
				in
				    #"Changed Type"

	annotation PBI_ResultType = Table

