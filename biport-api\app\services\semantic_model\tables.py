import os
import re
import json
import uuid
import xml.etree.ElementTree as ET

def extract_table_columns(root, output_dir, template_content, unique_table_lineage_tags, relationships):
    """
    Extracts table and column information from metadata-record XML elements.
    If a column is a date/datetime type, creates a local date table file and appends
    a variation relationship.
    Returns a dictionary mapping table names to lists of column dictionaries and an updated variant_relationships.
    """
    table_columns = {}
    variant_relationships = {}

    for metadata_record in root.findall(".//metadata-record[@class='column']"):
        parent_name = metadata_record.findtext("parent-name")
        column_name = metadata_record.findtext("remote-name") or metadata_record.findtext("local-name")
        aggregation = metadata_record.findtext("aggregation", "none")

        if not parent_name or not column_name:
            continue

        parent_name = parent_name.replace("[", "").replace("]", "")
        if parent_name not in unique_table_lineage_tags:
            unique_table_lineage_tags[parent_name] = str(uuid.uuid4())
        table_columns.setdefault(parent_name, [])

        column_lineage_tag = str(uuid.uuid4())
        data_type = metadata_record.findtext("local-type", "none")

        if data_type == "integer":
            data_type = "int64"
            format_string = 0
            aggregation = "Sum"
        elif data_type in ("double", "real"):
            data_type = "decimal"
            format_string = "0.00"
            aggregation = "Sum"
        elif data_type in ("date", "datetime"):
            data_type = "datetime"
            format_string = "General Date"
            aggregation = "none"
        elif data_type == "string":
            format_string = "none"
        else:
            format_string = "none"
        
        column_name = column_name.strip()
        column_name = f"'{column_name}'" if " " in column_name else column_name

        column_data = {
            "name": column_name,
            "dataType": data_type,
            "formatString": format_string,
            "lineageTag": column_lineage_tag,
            "summarizeBy": aggregation,
            "sourceColumn": column_name,
            "changedProperty": "none",
            "annotation": "SummarizationSetBy = Automatic",
        }
        table_columns[parent_name].append(column_data)

        if data_type in ["datetime", "date"]:
            local_date_table_name = f"LocalDateTable_{column_lineage_tag}"
            local_date_file = os.path.join(output_dir, f"{local_date_table_name}.tmdl")

            # Update the template content for the date table file using regex substitutions
            updated_content = re.sub(
                r"(table.*?lineageTag:)[ ]*[a-f0-9-]+",
                f"\\1 {column_lineage_tag}",
                template_content,
                count=1
            )
            updated_content = re.sub(
                r"(table LocalDateTable_{_)[a-f0-9-]+",
                lambda match: f"{match.group(1)}{column_lineage_tag}",
                template_content
            )
            updated_content = re.sub(
                r"(table\s+)DateTableTemplate_[a-f0-9-]+",
                f"\\1{local_date_table_name}",
                template_content
            )

            def replace_column_lineage_tags(match):
                return f"lineageTag: {str(uuid.uuid4())}"
            updated_content = re.sub(r"lineageTag: [a-f0-9-]+", replace_column_lineage_tags, updated_content)
            updated_content = re.sub(
                r"source = .*?\n",
                f"source = Calendar(Date(Year(MIN('{parent_name}'[{column_name}])), 1, 1), Date(Year(MAX('{parent_name}'[{column_name}])), 12, 31))\n",
                updated_content
            )
            updated_content = re.sub(
                r"partition DateTableTemplate_[a-f0-9-]+ = calculated",
                f"partition {local_date_table_name} = calculated",
                updated_content
            )
            updated_content = re.sub(r"isPrivate", "showAsVariationsOnly", updated_content)

            with open(local_date_file, 'w') as local_date:
                local_date.write(updated_content)

            if column_lineage_tag not in variant_relationships:
                variant_relationships[column_lineage_tag] = str(uuid.uuid4())
            relationships.append({
                "id": variant_relationships[column_lineage_tag],
                "annotation": "joinOnDateBehavior: datePartOnly",
                "fromColumn": f"{parent_name}.{column_name}",
                "toColumn": f"{local_date_table_name}.Date",
            })

            column_data["variation"] = {
                "isDefault": True,
                "relationship": variant_relationships[column_lineage_tag],
                "defaultHierarchy": f"{local_date_table_name}.'Date Hierarchy'"
            }
    return table_columns, variant_relationships

def write_table_model_files(table_columns, unique_table_lineage_tags, output_dir, server_name, database_name):
    """
    Creates a separate .tmdl file for each table with its column definitions and partition info.
    """

    for table, columns in table_columns.items():
        file_path = os.path.join(output_dir, f"{table}.tmdl")
        with open(file_path, 'w') as models_tmdl:
            models_tmdl.write(f"table {table}\n")
            models_tmdl.write(f"\tlineageTag: {unique_table_lineage_tags[table]}\n")
            for column in columns:
                models_tmdl.write(f"\tcolumn {column['name']}\n")
                models_tmdl.write(f"\t\tdataType: {column['dataType']}\n")
                models_tmdl.write(f"\t\tformatString: {column['formatString']}\n")
                models_tmdl.write(f"\t\tlineageTag: {column['lineageTag']}\n")
                models_tmdl.write(f"\t\tsummarizeBy: {column['summarizeBy']}\n")
                models_tmdl.write(f"\t\tsourceColumn: {column['sourceColumn']}\n")
                models_tmdl.write(f"\t\tannotation {column['annotation']}\n\n")
                if "variation" in column:
                    variation = column["variation"]
                    models_tmdl.write(f"\t\tvariation Variation\n")
                    models_tmdl.write(f"\t\t\tisDefault\n")
                    models_tmdl.write(f"\t\t\trelationship: {variation['relationship']}\n")
                    models_tmdl.write(f"\t\t\tdefaultHierarchy: {variation['defaultHierarchy']}\n\n\n")
                    models_tmdl.write(f"\t\tchangedProperty = IsHidden\n\n")
                    models_tmdl.write(f"\t\tchangedProperty = DataType\n\n")
                    models_tmdl.write(f'\t\tannotation PBI_FormatHint = {{"isDateTimeCustom":true}}\n\n')
                    
            models_tmdl.write(f"\tpartition {table} = m\n")
            models_tmdl.write(f"\t\tmode: import\n")
            models_tmdl.write(f"\t\tsource =\n")
            models_tmdl.write(f"\t\t\tlet\n")

            # Writing M code for SQL-based data source
            models_tmdl.write(f"\t\t\t\tSource = Sql.Databases(\"{server_name}\"),\n")
            models_tmdl.write(f"\t\t\t\t{database_name} = Source{{[Name=\"{database_name}\"]}}[Data],\n")
            models_tmdl.write(f"\t\t\t\tdbo_{table} = {database_name}{{[Schema=\"dbo\",Item=\"{table}\"]}}[Data]\n")
            models_tmdl.write(f"\t\t\tin\n")
            models_tmdl.write(f"\t\t\t\tdbo_{table}\n\n")

            models_tmdl.write(f"\tannotation PBI_NavigationStepName = Navigation\n")
            models_tmdl.write(f"\tannotation PBI_ResultType = Table\n")

def ensure_unique_column_name(tables_dir, table_name, new_column_name):
    tmdl_file_path = os.path.join(tables_dir, f"{table_name}.tmdl")
    
    if os.path.exists(tmdl_file_path):
        with open(tmdl_file_path, 'r', encoding='utf-8') as tmdl_file:
            content = tmdl_file.read()
            existing_columns = re.findall(r'column\s+(\S+)', content)
            print(existing_columns,"existing_columns")
            print(new_column_name,"new_column_name")
            if new_column_name in existing_columns:
                suffix = 1
                updated_column_name = f"{new_column_name}_{suffix}"
                print(updated_column_name,"updated_column_name")
                while updated_column_name in existing_columns:
                    suffix += 1
                    updated_column_name = f"{new_column_name}_{suffix}"
                    print(updated_column_name,"updated_column_name while")
                print(f"Column name '{new_column_name}' already exists in '{table_name}'. Renamed to '{updated_column_name}'")
                return updated_column_name
            else:
                return new_column_name
    else:
        return new_column_name

def update_tmdl_files_from_json(tables_dir, json_data):
    """
    Updates the .tmdl files in the given tables directory based on the provided JSON data.
    Adds the new column definition just above the partition section in the required format.
    """
    aggregation = None
    for item in json_data:
        table_name = item['table_name']
        caption = item['caption']
        formula = item['formula']
        data_type = item['data_type']
        role = item['role']
        col_type = item['type']
        column_name = caption
        
        if data_type == "integer":
            data_type = "int64"
            format_string = 0
        elif data_type in ("double", "real"):
            data_type = "decimal"
            format_string = "0.00"
        elif data_type in ("date", "datetime"):
            data_type = "datetime"
            format_string = "General Date"
        elif data_type == "string":
            format_string = "none"
        else:
            format_string = "none"

        if role == 'measure' and col_type == 'quantitative':
            aggregation = 'Sum'
        elif role == 'dimension' and col_type == 'nominal':
            aggregation = 'None'

        
        
        # Path to the corresponding table .tmdl file
        updated_column_name = ensure_unique_column_name(tables_dir, table_name, column_name)

        column = {
            'name': updated_column_name,
            'formula':formula,
            'dataType': data_type,
            'formatString': format_string,
            'lineageTag': str(uuid.uuid4()),
            'summarizeBy': aggregation,
            'annotation': 'SummarizationSetBy = Automatic\n'
        }
        
        tmdl_file_path = os.path.join(tables_dir, f"{table_name}.tmdl")
        
        # Check if the .tmdl file exists
        if os.path.exists(tmdl_file_path):
            with open(tmdl_file_path, 'r') as tmdl_file:
                lines = tmdl_file.readlines()

            partition_index = next((i for i, line in enumerate(lines) if line.strip().startswith("partition")), None)
            
            # If partition string is found, insert the new column definition just above it
            if partition_index is not None:
                # Separate the lines before and after the partition line
                lines_before_partition = lines[:partition_index]
                lines_after_partition = lines[partition_index:]


                updated_lines = lines_before_partition


                updated_lines.append(f"\tcolumn {updated_column_name} = {column['formula']}\n")
                updated_lines.append(f"\t\tdataType: {column['dataType']}\n")
                updated_lines.append(f"\t\tformatString: {column['formatString']}\n")
                updated_lines.append(f"\t\tlineageTag: {column['lineageTag']}\n")
                updated_lines.append(f"\t\tsummarizeBy: {column['summarizeBy']}\n")
                updated_lines.append(f"\t\tisDataTypeInferred\n")
                updated_lines.append(f"\t\tannotation {column['annotation']}\n\n")

                updated_lines += lines_after_partition

                # Write the updated content back to the .tmdl file
                with open(tmdl_file_path, 'w') as tmdl_file:
                    tmdl_file.writelines(updated_lines)
            else:
                # If no partition string is found, just append the column definition to the end of the file
                with open(tmdl_file_path, 'a') as tmdl_file:
                    tmdl_file.write(f"\tcolumn {column['name']}\n")
                    tmdl_file.write(f"\t\tdataType: {column['dataType']}\n")
                    tmdl_file.write(f"\t\tformatString: {column['formatString']}\n")
                    tmdl_file.write(f"\t\tlineageTag: {column['lineageTag']}\n")
                    tmdl_file.write(f"\t\tsummarizeBy: {column['summarizeBy']}\n")
                    tmdl_file.write(f"\t\tsourceColumn: {column['sourceColumn']}\n")
                    tmdl_file.write(f"\t\tannotation {column['annotation']}\n\n")
        else:
            print(f"Table file {table_name}.tmdl not found in {tables_dir}. Skipping update for this table.")

def create_parameters_model(tables_dir,datasource):
    """
    Create the Parameters.tmdl file in the 'tables' directory.
    """
    parameters_table = datasource.get('name')
    lineage_tag = str(uuid.uuid4())
    columns = datasource.findall(".//column[@name]")
    tmdl_file_path = os.path.join(tables_dir, f"{parameters_table}.tmdl")
    with open(tmdl_file_path, 'w') as models_tmdl:
        models_tmdl.write(f"table {parameters_table}\n")
        models_tmdl.write(f"\tlineageTag: {lineage_tag}\n\n")
       
        for column in columns:
            column_name = column.get('name').strip('[]')
            column_name = f"'{column_name}'"
            calculation = column.find('calculation')
            models_tmdl.write(f"\tmeasure {column_name} = {calculation.get('formula')}\n")
            models_tmdl.write(f"\t\tlineageTag: {str(uuid.uuid4())}\n\n")
            models_tmdl.write(f"\t\tannotation PBI_FormatHint = {'isGeneralNumber:true'}\n\n")
 
        models_tmdl.write(f"\tpartition {parameters_table} = m\n")
        models_tmdl.write(f"\t\tmode: import\n")
        models_tmdl.write(f"\t\tsource =\n")
        models_tmdl.write(f"\t\t\tlet\n")
        models_tmdl.write('\t\t\t\tSource = Table.FromRows(Json.Document(Binary.Decompress(Binary.FromText("i44FAA==", BinaryEncoding.Base64), Compression.Deflate)), let _t = ((type nullable text) meta [Serialized.Text = true]) in type table [Column1 = _t]),\n')
        models_tmdl.write('\t\t\t\t#"Changed Type" = Table.TransformColumnTypes(Source,{{"Column1", type text}}),\n')
        models_tmdl.write('\t\t\t\t#"Removed Columns" = Table.RemoveColumns(#"Changed Type",{"Column1"})\n')
        models_tmdl.write(f"\t\t\tin\n")
        models_tmdl.write('\t\t\t\t\t#"Removed Columns"\n\n')
        models_tmdl.write(f"\tannotation PBI_NavigationStepName = Navigation\n\n")
        models_tmdl.write(f"\tannotation PBI_ResultType = Table\n")
        
        
