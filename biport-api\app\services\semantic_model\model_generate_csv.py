import os
import re
import shutil
import time
import xml.etree.ElementTree as ET

from app.core.config import PathConfig, logger, S3Config
from app.services.semantic_model.process_calculations_csv import get_calculations_data_csv
from app.services.semantic_model.relationships_csv import extract_relationships_csv, write_relationships_file_csv
from app.services.semantic_model.tables_csv import (
    update_tmdl_files_from_json_csv,
    create_parameters_model_csv,
    extract_table_columns_csv,
    write_table_model_files_csv
)

s3_config = S3Config()
bucket_name = s3_config.bucket_name


async def semantic_model_generation_csv(organization_name, user_email, twb_files_path, local_dir, process_id, extracted_path):
    if not twb_files_path:
        raise ValueError("No TWB files provided for semantic model generation.")

    logger.info(f"Starting semantic model generation for {len(twb_files_path)} TWB file(s).")

    download_links = []
    for idx, twb_file in enumerate(twb_files_path, start=1):
        try:
            logger.info(f"Processing file {idx}/{len(twb_files_path)}: {os.path.basename(twb_file)}")

            download_link = await process_single_file_csv(
                twb_file, local_dir, organization_name, user_email, process_id, extracted_path
            )

            if download_link:
                logger.info(f"Successfully processed {os.path.basename(twb_file)}. Download link: {download_link}")
                download_links.append(download_link)
            else:
                logger.info(f"WARNING: No download link generated for {os.path.basename(twb_file)}.")

        except Exception as e:
            logger.info(f"ERROR processing {os.path.basename(twb_file)}: {str(e)}")
            continue

    if not download_links:
        raise Exception("No semantic models were generated successfully.")

    logger.info("Semantic model generation completed for all files.")

    return download_links


async def process_single_file_csv(twb_file, local_dir, organization_name, user_email, process_id, extracted_path):
    twb_filename = os.path.basename(twb_file)
    start_time = time.time()

    logger.info(f"Started processing file: {twb_filename}")
    logger.info(f"File timestamp: {start_time}")

    try:
        semantic_model_path = extracted_path
        if not os.path.exists(semantic_model_path):
            raise FileNotFoundError(f"Semantic model path does not exist: {semantic_model_path}")
        logger.info(f"Semantic model base path: {semantic_model_path}")

        short_name = os.path.splitext(twb_filename)[0][:2]
        powerbi_output_dir = os.path.join(local_dir, f'{short_name}_model_v2')
        os.makedirs(powerbi_output_dir, exist_ok=True)
        logger.info(f"Created output directory: {powerbi_output_dir}")

        definition_folder_path = copy_powerbi_structure_csv(source_path=semantic_model_path, dest_path=powerbi_output_dir)
        logger.info(f"Copied PowerBI structure. Definition folder path: {definition_folder_path}")

        tables_output_dir = os.path.join(definition_folder_path, 'tables')
        relationships_file = os.path.join(definition_folder_path, 'relationships.tmdl')
        models_file = os.path.join(definition_folder_path, 'models.tmdl')
        template_file_path = PathConfig.dateTable_template_path

        logger.info(f"Output paths: tables={tables_output_dir}, relationships={relationships_file}, models={models_file}")

        json_data = get_calculations_data_csv(twb_file)
        logger.info("Extracted calculations data from TWB file.")

        extract_models_and_columns_with_lineage_csv(
            twb_file, tables_output_dir, models_file, relationships_file, template_file_path, json_data
        )
        logger.info("Extracted models and columns with lineage successfully.")

        zip_path = shutil.make_archive(powerbi_output_dir, 'zip', powerbi_output_dir)
        zip_filename = os.path.basename(zip_path)
        unique_folder = f'{organization_name}/{user_email}'
        s3_key = f'{unique_folder}/{process_id}/semantic_model/{zip_filename}'

        await s3_config.upload_to_s3(zip_path, s3_key)
        presigned_url = await s3_config.generate_presigned_url(s3_key)

        download_link = {
            "file": zip_filename,
            "download_url": presigned_url
        }

        logger.info(f"Uploaded semantic model package. Download link: {download_link}")

        end_time = time.time()
        logger.info(f"Completed processing {twb_filename} in {end_time - start_time:.2f} seconds.")
        return download_link

    except Exception as e:
        logger.error(f"Error processing {twb_filename}: {str(e)}", exc_info=True)
        raise

def copy_powerbi_structure_csv(source_path, dest_path):
    logger.info("dest_path_at_beginning " + dest_path)
    start_time = time.time()
    logger.info(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(start_time))}] - Starting copy from {source_path} to {dest_path}")

    if not os.path.exists(source_path):
        raise FileNotFoundError(f"Source path does not exist: {source_path}")

    os.makedirs(dest_path, exist_ok=True)

    file_count = 0
    for root, dirs, files in os.walk(source_path):
        if '__MACOSX' in dirs:
            dirs.remove('__MACOSX')

        for filename in files:
            if filename.endswith('.twb') or filename == 'models.tmdl':
                continue

            src_path = os.path.join(root, filename)
            relative_path = os.path.relpath(src_path, source_path)
            dest_file_path = os.path.join(dest_path, relative_path)

            os.makedirs(os.path.dirname(dest_file_path), exist_ok=True)
            shutil.copy2(src_path, dest_file_path)
            print(f"Copied file: {src_path} -> {dest_file_path}")
            file_count += 1

    print(f"Total files copied (excluding .twb): {file_count}")

    semantic_model_path = None
    for root, dirs, _ in os.walk(dest_path):
        for dir_name in dirs:
            if '.SemanticModel' in dir_name:
                semantic_model_path = os.path.join(root, dir_name)
                print(f"Found SemanticModel folder at: {semantic_model_path}")
                break
        if semantic_model_path:
            break

    if not semantic_model_path:
        error_msg = f"No '.SemanticModel' folder found inside destination path: {dest_path}"
        print(f"ERROR - {error_msg}")
        logger.error(error_msg)
        raise Exception(error_msg)

    definition_folder_path = os.path.join(semantic_model_path, 'definition')
    print(f"Definition folder path set to: {definition_folder_path}")

    end_time = time.time()
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(end_time))}] - Completed copying structure. Time taken: {end_time - start_time:.2f} seconds.")

    return definition_folder_path

def extract_models_and_columns_with_lineage_csv(
    xml_file, output_dir, models_file, relationships_file, template_file, json_file
):
    logger.info(f"Starting extraction for file: {xml_file}")

    tree = ET.parse(xml_file)
    root = tree.getroot()

    unique_table_lineage_tags = {}
    relationships = []

    logger.info(f"Copying template file: {template_file}")
    template_content = copy_template_file_csv(template_file, output_dir)

    logger.info(f"Extracting connection info from XML: {xml_file}")
    server_name = get_connection_info_csv(root)

    logger.info(f"Extracting database name from XML: {xml_file}")
    database_name = get_database_name_csv(root)

    logger.info("Extracting relationships from XML")
    rels, variant_relationships = extract_relationships_csv(root, unique_table_lineage_tags)
    relationships.extend(rels)

    logger.info("Extracting table columns and writing local date tables")
    table_columns, variant_relationships = extract_table_columns_csv(
        root, output_dir, template_content, unique_table_lineage_tags, relationships
    )

    logger.info(f"Writing relationships to file: {relationships_file}")
    write_relationships_file_csv(relationships, relationships_file)

    logger.info("Writing individual table model files")
    write_table_model_files_csv(table_columns, unique_table_lineage_tags, output_dir, server_name, database_name)

    for datasource in root.findall(".//datasource"):
        if datasource.get('name') == 'Parameters':
            logger.info("Creating parameters model")
            create_parameters_model_csv(output_dir, datasource)
            break

    logger.info(f"Updating TMDL files from JSON: {json_file}")
    update_tmdl_files_from_json_csv(output_dir, json_file)

    logger.info(f"Models written successfully to: {models_file}")
    logger.info(f"Relationships written successfully to: {relationships_file}")
    print(f"Relationships written to: {relationships_file}")

def copy_template_file_csv(template_file, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    template_filename = os.path.basename(template_file)
    destination_template_path = os.path.join(output_dir, template_filename)
    shutil.copy(template_file, destination_template_path)

    with open(template_file, 'r') as tf:
        template_content = tf.read()

    with open(destination_template_path, 'w') as tf:
        tf.write(template_content)
    return template_content

def get_connection_info_csv(root):
    server_name = None
    for named_connection in root.findall(".//named-connection"):
        server_name = named_connection.get("caption")

        if server_name:
            break

    return server_name

def get_database_name_csv(root):
    database_name = None
    for datasource in root.findall(".//datasource"):
        caption = datasource.get("caption")
        if caption:
            match = re.search(r"\\((.*?)\\)", caption)
            if match:
                database_name = match.group(1)
                break
    return database_name