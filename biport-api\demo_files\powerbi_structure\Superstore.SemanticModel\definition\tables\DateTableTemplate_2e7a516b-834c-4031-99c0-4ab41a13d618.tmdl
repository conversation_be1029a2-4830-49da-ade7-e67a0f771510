table DateTableTemplate_2e7a516b-834c-4031-99c0-4ab41a13d618
	isHidden
	isPrivate
	lineageTag: 0c348168-f26a-4317-b972-083c6c25292a

	column Date
		dataType: dateTime
		isHidden
		lineageTag: a44551f4-8bdc-4f8a-b17b-17737d0d050c
		dataCategory: PaddedDateTableDates
		summarizeBy: none
		isNameInferred
		sourceColumn: [Date]

		annotation SummarizationSetBy = User

	column Year = YEAR([Date])
		dataType: int64
		isHidden
		lineageTag: f332028e-49ae-4c29-8cb6-1038385ea4dd
		dataCategory: Years
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Year

	column MonthNo = MONTH([Date])
		dataType: int64
		isHidden
		lineageTag: 16dca8a5-3a9a-438f-bb0e-b79e26c89e48
		dataCategory: MonthOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = MonthNumber

	column Month = FORMAT([Date], "MMMM")
		dataType: string
		isHidden
		lineageTag: 622845f6-cad1-48e8-9f2d-3a22a7bcefcc
		dataCategory: Months
		summarizeBy: none
		sortByColumn: MonthNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Month

	column QuarterNo = INT(([MonthNo] + 2) / 3)
		dataType: int64
		isHidden
		lineageTag: 9b125b74-dd2a-489c-a28a-0af130b15240
		dataCategory: QuarterOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = QuarterNumber

	column Quarter = "Qtr " & [QuarterNo]
		dataType: string
		isHidden
		lineageTag: 82802c40-bea2-4bd4-9651-1780c4ae7e60
		dataCategory: Quarters
		summarizeBy: none
		sortByColumn: QuarterNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Quarter

	column Day = DAY([Date])
		dataType: int64
		isHidden
		lineageTag: df13465c-ee53-4b61-8cc3-9b9028d44590
		dataCategory: DayOfMonth
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Day

	hierarchy 'Date Hierarchy'
		lineageTag: e084403e-d1c2-4a63-a302-ca14b9b123e8

		level Year
			lineageTag: f824cd6c-99e2-45d7-97ae-558fee715988
			column: Year

		level Quarter
			lineageTag: 3e716bc0-4c7b-4029-97ac-1c9c18914ddb
			column: Quarter

		level Month
			lineageTag: e37d869f-2f1f-463c-b576-471b2e8146f0
			column: Month

		level Day
			lineageTag: 9e9579e8-3119-4df4-b7ca-65994e87cac0
			column: Day

		annotation TemplateId = DateHierarchy

	partition DateTableTemplate_2e7a516b-834c-4031-99c0-4ab41a13d618 = calculated
		mode: import
		source = Calendar(Date(2015,1,1), Date(2015,1,1))

	annotation __PBI_TemplateDateTable = true

	annotation DefaultItem = DateHierarchy

