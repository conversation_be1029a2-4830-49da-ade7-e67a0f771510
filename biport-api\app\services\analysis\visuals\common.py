import re
from typing import List, Dict, Any
from xml.etree.ElementTree import Element
from app.core.enums import WorkSheet as WS, GeneralKeys as GS
from app.core.regex_enums import Regex as RE
from app.core import logger

def get_quantitative_columns(worksheet: Element) -> List[Dict[str, Any]]:
    columns = worksheet.findall(WS.DS_COLS.value)
    column_instances = worksheet.findall(WS.DS_COL_INSTANCES.value)
 
    if not columns or not column_instances:
        logger.warning("Columns or column instances are empty or None.")
        return []
 
    date_types = {'date', 'datetime'}
    quantitative_columns = []
 
    # Build a map of column name -> column attributes
    column_map = {
        col.attrib.get(WS.NAME.value): col.attrib
        for col in columns
        if col.attrib.get(WS.NAME.value)
    }
 
    for ci in column_instances:
        column_name = ci.attrib.get(WS.COLUMN.value)
        if not column_name or column_name not in column_map:
            continue
 
        column = column_map[column_name]
        datatype = column.get(WS.DATATYPE.value)
        instance_type = ci.attrib.get(WS.TYPE.value)
 
        if instance_type == GS.QUANTATIVE.value and datatype not in date_types:
            combined_details = {
                WS.COLUMN.value: column,
                GS.COLUMN_INSTANCE.value: ci.attrib
            }
            quantitative_columns.append(combined_details)
 
    return quantitative_columns

def get_rows_cols_details(worksheet: Element):
    columns = worksheet.findall(WS.DS_COLS.value)

    rows_elem = worksheet.find(WS.ROWS.value)
    rows_text = rows_elem.text if rows_elem is not None else RE.EMPTY.value
    cols_elem = worksheet.find(WS.COLS.value)
    cols_text = cols_elem.text if cols_elem is not None else RE.EMPTY.value

    rows = extract_column_names(rows_text) if rows_text else []
    cols = extract_column_names(cols_text) if cols_text else []

    column_instances = worksheet.findall(WS.DS_COL_INSTANCES.value)
    column_map = {}

    for instance in column_instances:
        column_name = instance.get(WS.NAME.value)
        if column_name:
            column_map[column_name] = {
                WS.COLUMN.value: instance.get(WS.COLUMN.value),
                WS.NAME.value: column_name,
                WS.TYPE.value: instance.get(WS.TYPE.value),
                WS.DATATYPE.value: None,
                WS.ROLE.value: None
            }

    column_lookup = {col.get(WS.NAME.value): col for col in columns}

    for instance_name, instance_details in column_map.items():
        column_name = instance_details[WS.COLUMN.value]
        if column_name in column_lookup:
            col = column_lookup[column_name]
            instance_details[WS.DATATYPE.value] = col.get(WS.DATATYPE.value)
            instance_details[WS.ROLE.value] = col.get(WS.ROLE.value)

    def analyze_columns(column_list):
        column_details = []
        for col_name in column_list:
            if col_name in column_map:
                column_details.append(column_map[col_name])
            elif col_name == "[:Measure Names]":
                column_details.append({
                    "column": "[:Measure Names]",
                    "name": "Measure Names",
                    "type": "nominal",
                    "datatype": "string",
                    "role": "dimension"
                })
            elif col_name == "[Multiple Values]":
                column_details.append({
                    "column": "[Multiple Values]",
                    "name": "Measure Values",
                    "type": "quantitative",
                    "datatype": "string",
                    "role": "measure"
                })
        return column_details

    rows_details = analyze_columns(rows)
    cols_details = analyze_columns(cols)

    quantitative_in_rows = [
        col for col in rows_details if col.get(WS.TYPE.value) == GS.QUANTATIVE.value
    ]
    quantitative_in_cols = [
        col for col in cols_details if col.get(WS.TYPE.value) == GS.QUANTATIVE.value
    ]

    if not quantitative_in_rows and not quantitative_in_cols:
        logger.warning("No quantitative data found in rows or columns.")

    if "[:Measure Names]" in rows or "[:Measure Names]" in cols:
        logger.info("Found 'Measure Names' in rows or columns.")

    if any(col.get(WS.TYPE.value) == "nominal" for col in rows_details) and \
       any(col.get(WS.TYPE.value) == "quantitative" for col in rows_details):
        logger.info("Found mixed data types in rows.")

    if any(col.get(WS.TYPE.value) == "nominal" for col in cols_details) and \
       any(col.get(WS.TYPE.value) == "quantitative" for col in cols_details):
        logger.info("Found mixed data types in columns.")

    return (
        quantitative_in_rows,
        quantitative_in_cols,
        rows,
        cols,
        columns,
        rows_details,
        cols_details
    )

def extract_column_names(expression: str):
    """
    Extracts column names from an expression like:
    ([none:Region:nk] * ([sum:Discount:qk] + [sum:Profit:qk]))
    Returns a list of column names with brackets included.
    """
    return re.findall(RE.LIST_IN_LIST_FOLLOWED.value, expression)

def check_common_structure(worksheet: Element, require_quantitative: bool = True) -> List[Dict[str, Any]]:
    """
    Common structure logic for both table and polygon:
    - Checks for quantitative columns
    - Validates no quantitative fields in layout (rows/cols)
    - Handles 'Multiple Values'
    Parameters:
        - require_quantitative: if True, fail if no quantitative columns exist (used in Table)
    Returns:
        - status: bool
        - pane: first <pane> element
        - rows_details, cols_details
        - quantitative_cols
    """
    quantitative_cols = get_quantitative_columns(worksheet)
    logger.debug(f"Quantitative columns: {quantitative_cols}")

    # Check if quantitative columns are required and missing
    if require_quantitative and not quantitative_cols:
        logger.debug("No quantitative columns found, but required.")
        return False, None, [], [], []

    (
        quantitative_in_rows,
        quantitative_in_cols,
        rows,
        cols,
        columns,
        rows_details,
        cols_details
    ) = get_rows_cols_details(worksheet)

    if any("Multiple Values" in str(col) for col in cols):
        cols_details.append({"name": "Multiple Values", "datatype": "quantitative"})
        quantitative_in_cols.append("Quantitative Data")

    if any("Multiple Values" in str(row) for row in rows):
        rows_details.append({"name": "Multiple Values", "datatype": "quantitative"})
        quantitative_in_rows.append("Quantitative Data")

    total_quantitative = quantitative_in_rows + quantitative_in_cols
    if total_quantitative:
        logger.debug("Quantitative data found in layout — not a text/polygon structure.")
        return False, None, rows_details, cols_details, quantitative_cols

    panes = worksheet.findall(WS.PANES.value)
    pane = panes[0] if panes else None
    if not pane:
        logger.debug("No <pane> found in worksheet.")
        return False, None, rows_details, cols_details, quantitative_cols

    return True, pane, rows_details, cols_details, quantitative_cols
