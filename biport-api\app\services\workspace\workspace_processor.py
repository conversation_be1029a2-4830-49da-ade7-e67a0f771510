from app.core import ServiceResponse
from app.models.users import User
from app.services.workspace import WorkspaceService
from app.core.logger_setup import logger
from uuid import UUID


class WorkspaceProcessor:
    """
    Processor class for workspace-related operations.

    Handles validation, business logic, and coordination between API endpoints
    and service layer for workspace functionality including report management
    and status updates.
    """

    @staticmethod
    def process_get_reports_by_user_role(user: User) -> ServiceResponse:
        """
        Process request to fetch reports based on the current user's role.

        Validates user permissions and delegates to service layer for data retrieval.
        Role-based access control determines which reports the user can see:
        - Admin: sees all reports in their organization
        - Manager: sees reports assigned to them or their subordinates
        - Dev<PERSON>per: sees only reports assigned to them

        Args:
            user: User object with role and organization information

        Returns:
            ServiceResponse: Contains list of reports or error information

        Raises:
            Exception: Service layer errors or validation failures
        """
        # Get role name safely
        role_name = getattr(user, 'role_name', None)
        logger.info(f"[WorkspaceProcessor] Processing report fetch for user ID: {user.id} (Role: {role_name})")

        try:
            response = WorkspaceService.execute(WorkspaceService().get_reports_by_user_role, user)
            logger.info(f"[WorkspaceProcessor] Successfully fetched reports. Status: {response.status_code}")
            return response
        except Exception as e:
            logger.error(f"[WorkspaceProcessor] Error occurred while fetching reports: {e}", exc_info=True)
            return ServiceResponse.failure("Error processing report fetch by user role", 500)

    @staticmethod
    def process_update_report_status(report_id: UUID, status_update, user: User) -> ServiceResponse:
        """
        Process request to update report status flags (unit_tested, uat_tested, deployed).

        Validates the request parameters and business rules before delegating to the service layer.
        Ensures at least one status field is provided for update and logs the operation details.

        Args:
            report_id: UUID of the report to update
            status_update: ReportStatusUpdateRequest containing the fields to update
            user: User object with role and organization information

        Returns:
            ServiceResponse: Contains success status, message, and updated field details

        Raises:
            Exception: Validation errors or service layer failures
        """
        logger.info(f"[WorkspaceProcessor] Processing report status update for report ID: {report_id}")

        try:
            # Validate that at least one field is being updated
            if (status_update.unit_tested is None and
                status_update.uat_tested is None and
                status_update.deployed is None):
                logger.warning("[WorkspaceProcessor] No status fields provided for update")
                return ServiceResponse.failure("At least one status field must be provided", 400)

            # Log what fields are being updated
            update_fields = []
            if status_update.unit_tested is not None:
                update_fields.append(f"unit_tested={status_update.unit_tested}")
            if status_update.uat_tested is not None:
                update_fields.append(f"uat_tested={status_update.uat_tested}")
            if status_update.deployed is not None:
                update_fields.append(f"deployed={status_update.deployed}")

            logger.info(f"[WorkspaceProcessor] Updating fields: {', '.join(update_fields)}")

            # Delegate to service layer
            response = WorkspaceService.execute(
                WorkspaceService().update_report_status,
                report_id,
                status_update,
                user
            )

            logger.info(f"[WorkspaceProcessor] Report status update completed. Status: {response.status_code}")

            return response

        except Exception as e:
            logger.error(f"[WorkspaceProcessor] Error occurred while updating report status: {e}", exc_info=True)
            return ServiceResponse.failure("Error processing report status update", 500)