import uuid,json
from ..core import get_tables_data,get_calc_filter_column,get_level

def get_pods(bound_filter, fil_name, col_name, tab_name):
    param_id = str(uuid.uuid4()).replace("-", "")[:20]
    if fil_name == "yr": return {"name": param_id,"boundFilter": bound_filter,"fieldExpr": {"HierarchyLevel": {"Expression": {"Hierarchy": {"Expression": {"PropertyVariationSource": {"Expression": {"SourceRef": {"Entity": tab_name}},"Name": "Variation","Property": col_name}},"Hierarchy": "Date Hierarchy"}},"Level": (fil_name)}}}
    elif fil_name == "sum": return {"name": param_id,"boundFilter": bound_filter,"fieldExpr": {"Column": {"Expression": {"SourceRef": {"Entity": tab_name}},"Property": col_name}},"asAggregation": True}
    else: return {"name": param_id,"boundFilter": bound_filter,"fieldExpr": {"Column": {"Expression": {"SourceRef": {"Entity": tab_name}},"Property": col_name}}}

def get_filters(bound_filter, fil_name, col_name, tab_name):
    if fil_name == "yr": return {"name": bound_filter,"expression": {"HierarchyLevel": {"Expression": {"Hierarchy": {"Expression": {"PropertyVariationSource": {"Expression": {"SourceRef": {"Entity": tab_name}},"Name": "Variation","Property": col_name}},"Hierarchy": "Date Hierarchy"}},"Level": get_level(fil_name)}},"type": "Categorical","howCreated": 5}
    else: return {"name": bound_filter,"expression": {"Column": {"Expression": {"SourceRef": {"Entity": tab_name}},"Property": col_name}},"type": "Categorical","howCreated": 5}

def get_drill_through_from_root(root, section_list, dashboards_worksheets, report_name_id_in_pbi):
    pods_list = []
    dashboard_filters = {}
    dashboards_dependency_names = {}
    target_dashboards = []

    try:
        for action in root.findall(".//action"):
            for param in action.findall(".//param"):
                if param.attrib.get("name") == "target":
                    target = param.attrib.get("value")
                    if target and target not in target_dashboards:
                        target_dashboards.append(target)

        for worksheet in root.findall(".//worksheet"):
            worksheet_name = worksheet.attrib.get("name", "")
            dashboard_name = next((d for d, w in dashboards_worksheets.items() if worksheet_name in w), None)
            if not dashboard_name or dashboard_name not in target_dashboards:
                continue
            dependencies = worksheet.findall(".//column-instance")
            if not dependencies:
                continue
            datasource_columns = []
            for col in worksheet.findall(".//datasource-dependencies/column"):
                datasource_columns.append(col.attrib)

            for column in dependencies:
                query = column.attrib.get("name")
                split_values = list(get_calc_filter_column(query, datasource_columns))
                dashboards_dependency_names.setdefault(dashboard_name, []).append(split_values)

        for dashboard_name, col_details in dashboards_dependency_names.items():
            pod = {
                "boundSection": report_name_id_in_pbi.get(dashboard_name),
                "config": "{}",
                "name": str(uuid.uuid4()).replace("-", "")[:20],
                "type": 1
            }
            filters = []
            parameters = []
            for col in col_details:
                bound_filter = str(uuid.uuid4()).replace("-", "")[:20]
                filters.append(get_filters(bound_filter, *col))
                parameters.append(get_pods(bound_filter, *col))
            dashboard_filters[dashboard_name] = json.dumps(filters, separators=(',', ':'))
            pod["parameters"] = json.dumps(parameters, separators=(',', ':'))
            pods_list.append(pod)

            for visual in section_list:
                if visual.get("displayName") == dashboard_name:
                    visual["filters"] = dashboard_filters[dashboard_name]

    except Exception as e:
        print(f"Error during drill-through extraction: {e}")

    return pods_list, section_list
