<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20242.24.0807.0327                               -->
<workbook include-phone-layouts='false' original-version='18.1' source-build='2024.2.2 (20242.24.0807.0327)' source-platform='win' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <IncludePhoneLayoutsOptOut />
    <MapboxVectorStylesAndLayers />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelRelationshipPerfOptions.true...ObjectModelRelationshipPerfOptions />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <WindowsPersistSimpleIdentifiers />
    <WorksheetBackgroundTransparency />
  </document-format-change-manifest>
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Parameter 1' datatype='integer' name='[Parameter 1]' param-domain-type='list' role='measure' type='quantitative' value='10'>
        <calculation class='tableau' formula='10' />
        <members />
      </column>
      <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
        <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        <members>
          <member value='&quot;PROFIT&quot;' />
          <member value='&quot;SALES&quot;' />
          <member value='&quot;COST&quot;' />
        </members>
      </column>
    </datasource>
    <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' inline='true' name='federated.1nl426t13auwkc10rmst40m1iuwe' version='18.1'>
      <connection class='federated'>
        <named-connections>
          <named-connection caption='SPARITY-SRIKRIS\SQL_SERVER_1' name='sqlserver.0k11eaw1owt4cc1b7523912w0z0e'>
            <connection authentication='sspi' class='sqlserver' dbname='AdventureWorksDW2022' minimum-driver-version='SQL Server Native Client 10.0' odbc-native-protocol='yes' one-time-sql='' server='localhost' />
          </named-connection>
        </named-connections>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <cols>
          <map key='[ArabicDescription]' value='[DimProduct].[ArabicDescription]' />
          <map key='[CalendarQuarter]' value='[DimDate].[CalendarQuarter]' />
          <map key='[CalendarSemester]' value='[DimDate].[CalendarSemester]' />
          <map key='[CalendarYear]' value='[DimDate].[CalendarYear]' />
          <map key='[CarrierTrackingNumber]' value='[FactInternetSales].[CarrierTrackingNumber]' />
          <map key='[ChineseDescription]' value='[DimProduct].[ChineseDescription]' />
          <map key='[Class]' value='[DimProduct].[Class]' />
          <map key='[Color]' value='[DimProduct].[Color]' />
          <map key='[CurrencyKey]' value='[FactInternetSales].[CurrencyKey]' />
          <map key='[CustomerKey]' value='[FactInternetSales].[CustomerKey]' />
          <map key='[CustomerPONumber]' value='[FactInternetSales].[CustomerPONumber]' />
          <map key='[DateKey]' value='[DimDate].[DateKey]' />
          <map key='[DayNumberOfMonth]' value='[DimDate].[DayNumberOfMonth]' />
          <map key='[DayNumberOfWeek]' value='[DimDate].[DayNumberOfWeek]' />
          <map key='[DayNumberOfYear]' value='[DimDate].[DayNumberOfYear]' />
          <map key='[DaysToManufacture]' value='[DimProduct].[DaysToManufacture]' />
          <map key='[DealerPrice]' value='[DimProduct].[DealerPrice]' />
          <map key='[DiscountAmount]' value='[FactInternetSales].[DiscountAmount]' />
          <map key='[DueDateKey]' value='[FactInternetSales].[DueDateKey]' />
          <map key='[DueDate]' value='[FactInternetSales].[DueDate]' />
          <map key='[EndDate]' value='[DimProduct].[EndDate]' />
          <map key='[EnglishDayNameOfWeek]' value='[DimDate].[EnglishDayNameOfWeek]' />
          <map key='[EnglishDescription]' value='[DimProduct].[EnglishDescription]' />
          <map key='[EnglishMonthName]' value='[DimDate].[EnglishMonthName]' />
          <map key='[EnglishProductCategoryName]' value='[DimProductCategory].[EnglishProductCategoryName]' />
          <map key='[EnglishProductName]' value='[DimProduct].[EnglishProductName]' />
          <map key='[EnglishProductSubcategoryName]' value='[DimProductSubcategory].[EnglishProductSubcategoryName]' />
          <map key='[ExtendedAmount]' value='[FactInternetSales].[ExtendedAmount]' />
          <map key='[FinishedGoodsFlag]' value='[DimProduct].[FinishedGoodsFlag]' />
          <map key='[FiscalQuarter]' value='[DimDate].[FiscalQuarter]' />
          <map key='[FiscalSemester]' value='[DimDate].[FiscalSemester]' />
          <map key='[FiscalYear]' value='[DimDate].[FiscalYear]' />
          <map key='[Freight]' value='[FactInternetSales].[Freight]' />
          <map key='[FrenchDayNameOfWeek]' value='[DimDate].[FrenchDayNameOfWeek]' />
          <map key='[FrenchDescription]' value='[DimProduct].[FrenchDescription]' />
          <map key='[FrenchMonthName]' value='[DimDate].[FrenchMonthName]' />
          <map key='[FrenchProductCategoryName]' value='[DimProductCategory].[FrenchProductCategoryName]' />
          <map key='[FrenchProductName]' value='[DimProduct].[FrenchProductName]' />
          <map key='[FrenchProductSubcategoryName]' value='[DimProductSubcategory].[FrenchProductSubcategoryName]' />
          <map key='[FullDateAlternateKey]' value='[DimDate].[FullDateAlternateKey]' />
          <map key='[GermanDescription]' value='[DimProduct].[GermanDescription]' />
          <map key='[HebrewDescription]' value='[DimProduct].[HebrewDescription]' />
          <map key='[JapaneseDescription]' value='[DimProduct].[JapaneseDescription]' />
          <map key='[LargePhoto]' value='[DimProduct].[LargePhoto]' />
          <map key='[ListPrice]' value='[DimProduct].[ListPrice]' />
          <map key='[ModelName]' value='[DimProduct].[ModelName]' />
          <map key='[MonthNumberOfYear]' value='[DimDate].[MonthNumberOfYear]' />
          <map key='[OrderDateKey]' value='[FactInternetSales].[OrderDateKey]' />
          <map key='[OrderDate]' value='[FactInternetSales].[OrderDate]' />
          <map key='[OrderQuantity]' value='[FactInternetSales].[OrderQuantity]' />
          <map key='[ProductAlternateKey]' value='[DimProduct].[ProductAlternateKey]' />
          <map key='[ProductCategoryAlternateKey]' value='[DimProductCategory].[ProductCategoryAlternateKey]' />
          <map key='[ProductCategoryKey (DimProductSubcategory)]' value='[DimProductSubcategory].[ProductCategoryKey]' />
          <map key='[ProductCategoryKey]' value='[DimProductCategory].[ProductCategoryKey]' />
          <map key='[ProductKey (DimProduct)]' value='[DimProduct].[ProductKey]' />
          <map key='[ProductKey]' value='[FactInternetSales].[ProductKey]' />
          <map key='[ProductLine]' value='[DimProduct].[ProductLine]' />
          <map key='[ProductStandardCost]' value='[FactInternetSales].[ProductStandardCost]' />
          <map key='[ProductSubcategoryAlternateKey]' value='[DimProductSubcategory].[ProductSubcategoryAlternateKey]' />
          <map key='[ProductSubcategoryKey (DimProduct)]' value='[DimProduct].[ProductSubcategoryKey]' />
          <map key='[ProductSubcategoryKey]' value='[DimProductSubcategory].[ProductSubcategoryKey]' />
          <map key='[PromotionKey]' value='[FactInternetSales].[PromotionKey]' />
          <map key='[ReorderPoint]' value='[DimProduct].[ReorderPoint]' />
          <map key='[RevisionNumber]' value='[FactInternetSales].[RevisionNumber]' />
          <map key='[SafetyStockLevel]' value='[DimProduct].[SafetyStockLevel]' />
          <map key='[SalesAmount]' value='[FactInternetSales].[SalesAmount]' />
          <map key='[SalesOrderLineNumber]' value='[FactInternetSales].[SalesOrderLineNumber]' />
          <map key='[SalesOrderNumber]' value='[FactInternetSales].[SalesOrderNumber]' />
          <map key='[SalesTerritoryAlternateKey]' value='[DimSalesTerritory].[SalesTerritoryAlternateKey]' />
          <map key='[SalesTerritoryCountry]' value='[DimSalesTerritory].[SalesTerritoryCountry]' />
          <map key='[SalesTerritoryGroup]' value='[DimSalesTerritory].[SalesTerritoryGroup]' />
          <map key='[SalesTerritoryImage]' value='[DimSalesTerritory].[SalesTerritoryImage]' />
          <map key='[SalesTerritoryKey (DimSalesTerritory)]' value='[DimSalesTerritory].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryKey]' value='[FactInternetSales].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryRegion]' value='[DimSalesTerritory].[SalesTerritoryRegion]' />
          <map key='[ShipDateKey]' value='[FactInternetSales].[ShipDateKey]' />
          <map key='[ShipDate]' value='[FactInternetSales].[ShipDate]' />
          <map key='[SizeRange]' value='[DimProduct].[SizeRange]' />
          <map key='[SizeUnitMeasureCode]' value='[DimProduct].[SizeUnitMeasureCode]' />
          <map key='[Size]' value='[DimProduct].[Size]' />
          <map key='[SpanishDayNameOfWeek]' value='[DimDate].[SpanishDayNameOfWeek]' />
          <map key='[SpanishMonthName]' value='[DimDate].[SpanishMonthName]' />
          <map key='[SpanishProductCategoryName]' value='[DimProductCategory].[SpanishProductCategoryName]' />
          <map key='[SpanishProductName]' value='[DimProduct].[SpanishProductName]' />
          <map key='[SpanishProductSubcategoryName]' value='[DimProductSubcategory].[SpanishProductSubcategoryName]' />
          <map key='[StandardCost]' value='[DimProduct].[StandardCost]' />
          <map key='[StartDate]' value='[DimProduct].[StartDate]' />
          <map key='[Status]' value='[DimProduct].[Status]' />
          <map key='[Style]' value='[DimProduct].[Style]' />
          <map key='[TaxAmt]' value='[FactInternetSales].[TaxAmt]' />
          <map key='[ThaiDescription]' value='[DimProduct].[ThaiDescription]' />
          <map key='[TotalProductCost]' value='[FactInternetSales].[TotalProductCost]' />
          <map key='[TurkishDescription]' value='[DimProduct].[TurkishDescription]' />
          <map key='[UnitPriceDiscountPct]' value='[FactInternetSales].[UnitPriceDiscountPct]' />
          <map key='[UnitPrice]' value='[FactInternetSales].[UnitPrice]' />
          <map key='[WeekNumberOfYear]' value='[DimDate].[WeekNumberOfYear]' />
          <map key='[WeightUnitMeasureCode]' value='[DimProduct].[WeightUnitMeasureCode]' />
          <map key='[Weight]' value='[DimProduct].[Weight]' />
        </cols>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>1</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryAlternateKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryAlternateKey</remote-alias>
            <ordinal>2</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>EnglishProductCategoryName</remote-alias>
            <ordinal>3</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>SpanishProductCategoryName</remote-alias>
            <ordinal>4</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>FrenchProductCategoryName</remote-alias>
            <ordinal>5</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>7</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryAlternateKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryAlternateKey</remote-alias>
            <ordinal>8</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>EnglishProductSubcategoryName</remote-alias>
            <ordinal>9</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>SpanishProductSubcategoryName</remote-alias>
            <ordinal>10</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>FrenchProductSubcategoryName</remote-alias>
            <ordinal>11</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey (DimProductSubcategory)]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>12</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>14</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductAlternateKey</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductAlternateKey]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductAlternateKey</remote-alias>
            <ordinal>15</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>16</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeightUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[WeightUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>WeightUnitMeasureCode</remote-alias>
            <ordinal>17</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeUnitMeasureCode</remote-alias>
            <ordinal>18</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishProductName</remote-alias>
            <ordinal>19</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SpanishProductName</remote-alias>
            <ordinal>20</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchProductName</remote-alias>
            <ordinal>21</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[StandardCost]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StandardCost</remote-alias>
            <ordinal>22</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FinishedGoodsFlag</remote-name>
            <remote-type>11</remote-type>
            <local-name>[FinishedGoodsFlag]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FinishedGoodsFlag</remote-alias>
            <ordinal>23</ordinal>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Color</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Color]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Color</remote-alias>
            <ordinal>24</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>15</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SafetyStockLevel</remote-name>
            <remote-type>2</remote-type>
            <local-name>[SafetyStockLevel]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SafetyStockLevel</remote-alias>
            <ordinal>25</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ReorderPoint</remote-name>
            <remote-type>2</remote-type>
            <local-name>[ReorderPoint]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ReorderPoint</remote-alias>
            <ordinal>26</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ListPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ListPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ListPrice</remote-alias>
            <ordinal>27</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Size</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Size]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Size</remote-alias>
            <ordinal>28</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeRange</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeRange]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeRange</remote-alias>
            <ordinal>29</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Weight</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Weight]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Weight</remote-alias>
            <ordinal>30</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DaysToManufacture</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DaysToManufacture]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DaysToManufacture</remote-alias>
            <ordinal>31</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductLine</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductLine]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductLine</remote-alias>
            <ordinal>32</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DealerPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DealerPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DealerPrice</remote-alias>
            <ordinal>33</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Class</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Class]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Class</remote-alias>
            <ordinal>34</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Style</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Style]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Style</remote-alias>
            <ordinal>35</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ModelName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ModelName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ModelName</remote-alias>
            <ordinal>36</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LargePhoto</remote-name>
            <remote-type>128</remote-type>
            <local-name>[LargePhoto]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>LargePhoto</remote-alias>
            <ordinal>37</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishDescription</remote-alias>
            <ordinal>38</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchDescription</remote-alias>
            <ordinal>39</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ChineseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ChineseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ChineseDescription</remote-alias>
            <ordinal>40</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ArabicDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ArabicDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ArabicDescription</remote-alias>
            <ordinal>41</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HebrewDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[HebrewDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>HebrewDescription</remote-alias>
            <ordinal>42</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ThaiDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ThaiDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ThaiDescription</remote-alias>
            <ordinal>43</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>GermanDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[GermanDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>GermanDescription</remote-alias>
            <ordinal>44</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>JapaneseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[JapaneseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>JapaneseDescription</remote-alias>
            <ordinal>45</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TurkishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[TurkishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>TurkishDescription</remote-alias>
            <ordinal>46</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StartDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[StartDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StartDate</remote-alias>
            <ordinal>47</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EndDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[EndDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EndDate</remote-alias>
            <ordinal>48</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Status</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Status]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Status</remote-alias>
            <ordinal>49</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>7</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>51</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[OrderDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDateKey</remote-alias>
            <ordinal>52</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DueDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDateKey</remote-alias>
            <ordinal>53</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ShipDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDateKey</remote-alias>
            <ordinal>54</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CustomerKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerKey</remote-alias>
            <ordinal>55</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PromotionKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[PromotionKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>PromotionKey</remote-alias>
            <ordinal>56</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CurrencyKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CurrencyKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CurrencyKey</remote-alias>
            <ordinal>57</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>58</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesOrderNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderNumber</remote-alias>
            <ordinal>59</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>20</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderLineNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[SalesOrderLineNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderLineNumber</remote-alias>
            <ordinal>60</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RevisionNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[RevisionNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>RevisionNumber</remote-alias>
            <ordinal>61</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderQuantity</remote-name>
            <remote-type>2</remote-type>
            <local-name>[OrderQuantity]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderQuantity</remote-alias>
            <ordinal>62</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[UnitPrice]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPrice</remote-alias>
            <ordinal>63</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ExtendedAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ExtendedAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ExtendedAmount</remote-alias>
            <ordinal>64</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPriceDiscountPct</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UnitPriceDiscountPct]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPriceDiscountPct</remote-alias>
            <ordinal>65</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DiscountAmount</remote-name>
            <remote-type>5</remote-type>
            <local-name>[DiscountAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DiscountAmount</remote-alias>
            <ordinal>66</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductStandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ProductStandardCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductStandardCost</remote-alias>
            <ordinal>67</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TotalProductCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TotalProductCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TotalProductCost</remote-alias>
            <ordinal>68</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SalesAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesAmount</remote-alias>
            <ordinal>69</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TaxAmt</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TaxAmt]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TaxAmt</remote-alias>
            <ordinal>70</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Freight</remote-name>
            <remote-type>131</remote-type>
            <local-name>[Freight]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>Freight</remote-alias>
            <ordinal>71</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CarrierTrackingNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CarrierTrackingNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CarrierTrackingNumber</remote-alias>
            <ordinal>72</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerPONumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CustomerPONumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerPONumber</remote-alias>
            <ordinal>73</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[OrderDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDate</remote-alias>
            <ordinal>74</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DueDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDate</remote-alias>
            <ordinal>75</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[ShipDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDate</remote-alias>
            <ordinal>76</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DateKey</remote-alias>
            <ordinal>78</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FullDateAlternateKey</remote-name>
            <remote-type>7</remote-type>
            <local-name>[FullDateAlternateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FullDateAlternateKey</remote-alias>
            <ordinal>79</ordinal>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
              <attribute datatype='boolean' name='TypeIsDateTime2orDate'>true</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfWeek</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfWeek</remote-alias>
            <ordinal>80</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishDayNameOfWeek</remote-alias>
            <ordinal>81</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishDayNameOfWeek</remote-alias>
            <ordinal>82</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchDayNameOfWeek</remote-alias>
            <ordinal>83</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfMonth</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfMonth]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfMonth</remote-alias>
            <ordinal>84</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[DayNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfYear</remote-alias>
            <ordinal>85</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeekNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[WeekNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>WeekNumberOfYear</remote-alias>
            <ordinal>86</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishMonthName</remote-alias>
            <ordinal>87</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishMonthName</remote-alias>
            <ordinal>88</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchMonthName</remote-alias>
            <ordinal>89</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MonthNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[MonthNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>MonthNumberOfYear</remote-alias>
            <ordinal>90</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarQuarter</remote-alias>
            <ordinal>91</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[CalendarYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarYear</remote-alias>
            <ordinal>92</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarSemester</remote-alias>
            <ordinal>93</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalQuarter</remote-alias>
            <ordinal>94</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[FiscalYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalYear</remote-alias>
            <ordinal>95</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalSemester</remote-alias>
            <ordinal>96</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey (DimSalesTerritory)]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>98</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryAlternateKey]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryAlternateKey</remote-alias>
            <ordinal>99</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryRegion</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryRegion]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryRegion</remote-alias>
            <ordinal>100</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryCountry</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryCountry]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryCountry</remote-alias>
            <ordinal>101</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryGroup</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryGroup]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryGroup</remote-alias>
            <ordinal>102</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryImage</remote-name>
            <remote-type>128</remote-type>
            <local-name>[SalesTerritoryImage]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryImage</remote-alias>
            <ordinal>103</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
        </metadata-records>
      </connection>
      <aliases enabled='yes' />
      <column caption='Arabic Description' datatype='string' name='[ArabicDescription]' role='dimension' type='nominal' />
      <column caption='CLEAR FILTERS' datatype='string' name='[Calculation_295830217743659008]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Clear All&apos;' />
      </column>
      <column aggregation='Sum' caption='Quarter' datatype='integer' name='[Calculation_447545216406446083]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='QUARTER([OrderDate])' />
      </column>
      <column aggregation='Sum' caption='Month' datatype='integer' name='[Calculation_447545216414334984]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='MONTH([OrderDate])' />
      </column>
      <column caption='MonthName' datatype='string' name='[Calculation_447545216416518153]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='MonthShortName' datatype='string' name='[Calculation_447545216416817162]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='LEFT(DATENAME(&apos;month&apos;,[OrderDate]),3)' />
      </column>
      <column aggregation='Sum' caption='Weekday' datatype='integer' name='[Calculation_447545216417103883]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekdayName' datatype='string' name='[Calculation_447545216417656844]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekEnd/WeekDay' datatype='string' name='[Calculation_447545216417820685]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='IF [Calculation_447545216417103883]=1 or [Calculation_447545216417103883]=7 THEN &quot;WeekEnd&quot; &#13;&#10;ELSE &quot;WeekDay&quot;&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='FinacialMonth' datatype='integer' name='[Calculation_447545216418512910]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='IF MONTH([OrderDate])-3&gt;0&#13;&#10;THEN MONTH([OrderDate])-3&#13;&#10;ELSE MONTH([OrderDate])+12-3&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='Finacial Quarter' datatype='integer' name='[Calculation_447545216418717711]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;quarter&apos;,DATEADD(&apos;month&apos;,-3,[OrderDate]))' />
      </column>
      <column caption='YearMonth' datatype='string' name='[Calculation_447545216419405840]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;year&apos;,[OrderDate])+&quot;-&quot;+DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='Month,Day,Year' datatype='integer' name='[Calculation_447545216421498898]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='(DATEPART(&apos;year&apos;, [OrderDate])*10000 + DATEPART(&apos;month&apos;, [OrderDate])*100 + DATEPART(&apos;day&apos;, [OrderDate]))' />
      </column>
      <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
      </column>
      <column caption='DM' datatype='real' name='[Calculation_447545216423968789]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='IF [Parameters].[Parameter 2]==&quot;PROFIT&quot; THEN [Calculation_447545216423890964]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;SALES&quot; THEN [SalesAmount]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;COST&quot; THEN [TotalProductCost]&#13;&#10;ELSE 0&#13;&#10;END' />
      </column>
      <column caption='OrderDate' datatype='date' name='[Calculation_474848296898699264]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATE(LEFT(STR([OrderDateKey]),4)+&quot;-&quot;+MID(STR([OrderDateKey]),5,2)+&quot;-&quot;+RIGHT(STR([OrderDateKey]),2))' />
      </column>
      <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='YEAR([OrderDate])' />
      </column>
      <column caption='Reset Filters' datatype='string' name='[Calculation_778278323468275715]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Reset Filters&apos;' />
      </column>
      <column caption='Calendar Quarter' datatype='integer' name='[CalendarQuarter]' role='dimension' type='quantitative' />
      <column caption='Calendar Semester' datatype='integer' name='[CalendarSemester]' role='measure' type='quantitative' />
      <column caption='Calendar Year' datatype='integer' name='[CalendarYear]' role='dimension' type='quantitative' />
      <column caption='Carrier Tracking Number' datatype='string' name='[CarrierTrackingNumber]' role='dimension' type='nominal' />
      <column caption='Chinese Description' datatype='string' name='[ChineseDescription]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Currency Key' datatype='integer' name='[CurrencyKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Customer Key' datatype='integer' name='[CustomerKey]' role='dimension' type='ordinal' />
      <column caption='Customer PO Number' datatype='string' name='[CustomerPONumber]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Date Key' datatype='integer' name='[DateKey]' role='dimension' type='ordinal' />
      <column caption='Day Number Of Month' datatype='integer' name='[DayNumberOfMonth]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Week' datatype='integer' name='[DayNumberOfWeek]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Year' datatype='integer' name='[DayNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Days To Manufacture' datatype='integer' name='[DaysToManufacture]' role='measure' type='quantitative' />
      <column caption='Dealer Price' datatype='real' name='[DealerPrice]' role='measure' type='quantitative' />
      <column caption='Discount Amount' datatype='real' name='[DiscountAmount]' role='measure' type='quantitative' />
      <column aggregation='Sum' caption='Due Date Key' datatype='integer' name='[DueDateKey]' role='dimension' type='ordinal' />
      <column caption='Due Date' datatype='datetime' name='[DueDate]' role='dimension' type='ordinal' />
      <column caption='End Date' datatype='datetime' name='[EndDate]' role='dimension' type='ordinal' />
      <column caption='English Day Name Of Week' datatype='string' name='[EnglishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='English Description' datatype='string' name='[EnglishDescription]' role='dimension' type='nominal' />
      <column caption='English Month Name' datatype='string' name='[EnglishMonthName]' role='dimension' type='nominal' />
      <column caption='English Product Category Name' datatype='string' name='[EnglishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='English Product Name' datatype='string' name='[EnglishProductName]' role='dimension' type='nominal' />
      <column caption='English Product Subcategory Name' datatype='string' name='[EnglishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Extended Amount' datatype='real' name='[ExtendedAmount]' role='measure' type='quantitative' />
      <column caption='Finished Goods Flag' datatype='boolean' name='[FinishedGoodsFlag]' role='dimension' type='nominal' />
      <column caption='Fiscal Quarter' datatype='integer' name='[FiscalQuarter]' role='dimension' type='quantitative' />
      <column caption='Fiscal Semester' datatype='integer' name='[FiscalSemester]' role='measure' type='quantitative' />
      <column caption='Fiscal Year' datatype='integer' name='[FiscalYear]' role='dimension' type='quantitative' />
      <column caption='French Day Name Of Week' datatype='string' name='[FrenchDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='French Description' datatype='string' name='[FrenchDescription]' role='dimension' type='nominal' />
      <column caption='French Month Name' datatype='string' name='[FrenchMonthName]' role='dimension' type='nominal' />
      <column caption='French Product Category Name' datatype='string' name='[FrenchProductCategoryName]' role='dimension' type='nominal' />
      <column caption='French Product Name' datatype='string' name='[FrenchProductName]' role='dimension' type='nominal' />
      <column caption='French Product Subcategory Name' datatype='string' name='[FrenchProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Full Date Alternate Key' datatype='date' name='[FullDateAlternateKey]' role='dimension' type='ordinal' />
      <column caption='German Description' datatype='string' name='[GermanDescription]' role='dimension' type='nominal' />
      <column caption='Hebrew Description' datatype='string' name='[HebrewDescription]' role='dimension' type='nominal' />
      <column caption='Japanese Description' datatype='string' name='[JapaneseDescription]' role='dimension' type='nominal' />
      <column caption='Large Photo' datatype='string' name='[LargePhoto]' role='dimension' type='nominal' />
      <column caption='List Price' datatype='real' name='[ListPrice]' role='measure' type='quantitative' />
      <column caption='Model Name' datatype='string' name='[ModelName]' role='dimension' type='nominal' />
      <column caption='Month Number Of Year' datatype='integer' name='[MonthNumberOfYear]' role='dimension' type='quantitative' />
      <column aggregation='Sum' caption='Order Date Key' datatype='integer' name='[OrderDateKey]' role='dimension' type='ordinal' />
      <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
      <column caption='Order Quantity' datatype='integer' name='[OrderQuantity]' role='measure' type='quantitative' />
      <column caption='Product Alternate Key' datatype='string' name='[ProductAlternateKey]' role='dimension' type='nominal' />
      <column caption='Product Category Alternate Key' datatype='integer' name='[ProductCategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductCategoryKey (DimProductSubcategory)]' role='dimension' type='ordinal' />
      <column caption='Product Category Key' datatype='integer' name='[ProductCategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductKey (DimProduct)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Product Key' datatype='integer' name='[ProductKey]' role='dimension' type='ordinal' />
      <column caption='Product Line' datatype='string' name='[ProductLine]' role='dimension' type='nominal' />
      <column caption='Product Standard Cost' datatype='real' name='[ProductStandardCost]' role='measure' type='quantitative' />
      <column caption='Product Subcategory Alternate Key' datatype='integer' name='[ProductSubcategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductSubcategoryKey (DimProduct)]' role='dimension' type='ordinal' />
      <column caption='Product Subcategory Key' datatype='integer' name='[ProductSubcategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Promotion Key' datatype='integer' name='[PromotionKey]' role='dimension' type='ordinal' />
      <column caption='Reorder Point' datatype='integer' name='[ReorderPoint]' role='measure' type='quantitative' />
      <column caption='Revision Number' datatype='integer' name='[RevisionNumber]' role='dimension' type='ordinal' />
      <column caption='Safety Stock Level' datatype='integer' name='[SafetyStockLevel]' role='measure' type='quantitative' />
      <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
      <column caption='Sales Order Line Number' datatype='integer' name='[SalesOrderLineNumber]' role='dimension' type='ordinal' />
      <column caption='Sales Order Number' datatype='string' name='[SalesOrderNumber]' role='dimension' type='nominal' />
      <column caption='Sales Territory Alternate Key' datatype='integer' name='[SalesTerritoryAlternateKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Country' datatype='string' name='[SalesTerritoryCountry]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
      <column caption='Sales Territory Group' datatype='string' name='[SalesTerritoryGroup]' role='dimension' type='nominal' />
      <column caption='Sales Territory Image' datatype='string' name='[SalesTerritoryImage]' role='dimension' type='nominal' />
      <column aggregation='Sum' datatype='integer' name='[SalesTerritoryKey (DimSalesTerritory)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Sales Territory Key' datatype='integer' name='[SalesTerritoryKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Region' datatype='string' name='[SalesTerritoryRegion]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Ship Date Key' datatype='integer' name='[ShipDateKey]' role='dimension' type='ordinal' />
      <column caption='Ship Date' datatype='datetime' name='[ShipDate]' role='dimension' type='ordinal' />
      <column caption='Size Range' datatype='string' name='[SizeRange]' role='dimension' type='nominal' />
      <column caption='Size Unit Measure Code' datatype='string' name='[SizeUnitMeasureCode]' role='dimension' type='nominal' />
      <column caption='Spanish Day Name Of Week' datatype='string' name='[SpanishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='Spanish Month Name' datatype='string' name='[SpanishMonthName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Category Name' datatype='string' name='[SpanishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Name' datatype='string' name='[SpanishProductName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Subcategory Name' datatype='string' name='[SpanishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Standard Cost' datatype='real' name='[StandardCost]' role='measure' type='quantitative' />
      <column caption='Start Date' datatype='datetime' name='[StartDate]' role='dimension' type='ordinal' />
      <column caption='Tax Amt' datatype='real' name='[TaxAmt]' role='measure' type='quantitative' />
      <column caption='Thai Description' datatype='string' name='[ThaiDescription]' role='dimension' type='nominal' />
      <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
      <column caption='Turkish Description' datatype='string' name='[TurkishDescription]' role='dimension' type='nominal' />
      <column caption='Unit Price Discount Pct' datatype='real' name='[UnitPriceDiscountPct]' role='measure' type='quantitative' />
      <column caption='Unit Price' datatype='real' name='[UnitPrice]' role='measure' type='quantitative' />
      <column caption='Week Number Of Year' datatype='integer' name='[WeekNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Weight Unit Measure Code' datatype='string' name='[WeightUnitMeasureCode]' role='dimension' type='nominal' />
      <_.fcp.ObjectModelTableType.true...column caption='DimDate' datatype='table' name='[__tableau_internal_object_id__].[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProductCategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProductSubcategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProduct' datatype='table' name='[__tableau_internal_object_id__].[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimSalesTerritory' datatype='table' name='[__tableau_internal_object_id__].[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='Sales' datatype='table' name='[__tableau_internal_object_id__].[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]' role='measure' type='quantitative' />
      <column-instance column='[SalesTerritoryCountry]' derivation='Attribute' name='[attr:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesTerritoryCountry]' derivation='Max' name='[max:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_447545216406446083]' derivation='None' name='[none:Calculation_447545216406446083:ok]' pivot='key' type='ordinal' />
      <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
      <column-instance column='[SalesTerritoryCountry]' derivation='None' name='[none:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[Calculation_474848296898699264]' derivation='Quarter' name='[qr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:2]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[EnglishMonthName]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:3]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[SalesTerritoryCountry]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:4]' pivot='key' type='quantitative'>
        <table-calc ordering-type='CellInPane' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
      <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_474848296898699264]' derivation='Year-Trunc' name='[tyr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <group caption='Action (CLEAR FILTERS)' hidden='true' name='[Action (CLEAR FILTERS)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_295830217743659008]' />
        </groupfilter>
      </group>
      <group caption='Action (Clear All)' hidden='true' name='[Action (Clear All)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323462979586]' />
        </groupfilter>
      </group>
      <group caption='Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))' hidden='true' name='[Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
          <groupfilter function='level-members' level='[qr:Calculation_474848296898699264:ok]' />
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset Filters)' hidden='true' name='[Action (Reset Filters)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323468275715]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset)' hidden='true' name='[Action (Reset)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323455827969]' />
        </groupfilter>
      </group>
      <group caption='Action (Sales Territory Country)' hidden='true' name='[Action (Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (YEAR(OrderDate))' hidden='true' name='[Action (YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Year)' hidden='true' name='[Action (Year)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Month,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,Month,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115184209922]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,MonthName,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,MonthName,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Quarter)' hidden='true' name='[Action (Year,Quarter)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
        </groupfilter>
      </group>
      <drill-paths>
        <drill-path name='Sales Territory Region, Sales Territory Country'>
          <field>[SalesTerritoryRegion]</field>
          <field>[SalesTerritoryCountry]</field>
        </drill-path>
      </drill-paths>
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' rowDisplayCount='300' show-aliased-fields='true' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[none:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[max:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[attr:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_447545216406446083:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>1</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>4</bucket>
            </map>
            <map to='#e15759'>
              <bucket>3</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[:Measure Names]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409763848:qk]&quot;</bucket>
            </map>
            <map to='#9c755f'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216406446083:ok]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407900165:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407973894:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407998471:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418045966:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[usr:Calculation_447545216407670788:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:2]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:3]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:4]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912410996746:qk]&quot;</bucket>
            </map>
            <map to='#ff5500'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409743366:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912411025420:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418058256:qk]&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_561261115181088768:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>2010</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>2014</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>2013</bucket>
            </map>
            <map to='#e15759'>
              <bucket>2012</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2011</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;India&quot;' />
      </semantic-values>
      <field-sort-info field-sort-order-type='custom-order'>
        <field-sort-custom-order field='DateKey' />
        <field-sort-custom-order field='FullDateAlternateKey' />
        <field-sort-custom-order field='DayNumberOfWeek' />
        <field-sort-custom-order field='EnglishDayNameOfWeek' />
        <field-sort-custom-order field='SpanishDayNameOfWeek' />
        <field-sort-custom-order field='FrenchDayNameOfWeek' />
        <field-sort-custom-order field='DayNumberOfMonth' />
        <field-sort-custom-order field='DayNumberOfYear' />
        <field-sort-custom-order field='WeekNumberOfYear' />
        <field-sort-custom-order field='EnglishMonthName' />
        <field-sort-custom-order field='SpanishMonthName' />
        <field-sort-custom-order field='FrenchMonthName' />
        <field-sort-custom-order field='MonthNumberOfYear' />
        <field-sort-custom-order field='CalendarQuarter' />
        <field-sort-custom-order field='CalendarYear' />
        <field-sort-custom-order field='CalendarSemester' />
        <field-sort-custom-order field='FiscalQuarter' />
        <field-sort-custom-order field='FiscalYear' />
        <field-sort-custom-order field='FiscalSemester' />
      </field-sort-info>
      <datasource-dependencies datasource='Parameters'>
        <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
          <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='DimDate' id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
            </properties>
          </object>
          <object caption='DimProductCategory' id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProductSubcategory' id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProduct' id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
            </properties>
          </object>
          <object caption='DimSalesTerritory' id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
            </properties>
          </object>
          <object caption='Sales' id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[OrderDateKey]' />
              <expression op='[DateKey]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductCategoryKey]' />
              <expression op='[ProductCategoryKey (DimProductSubcategory)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A' unique-key='true' />
            <second-end-point object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductSubcategoryKey]' />
              <expression op='[ProductSubcategoryKey (DimProduct)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' unique-key='true' />
            <second-end-point object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SalesTerritoryKey]' />
              <expression op='[SalesTerritoryKey (DimSalesTerritory)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductKey (DimProduct)]' />
              <expression op='[ProductKey]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' unique-key='true' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
          </relationship>
        </relationships>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <mapsources>
    <mapsource name='Tableau' />
  </mapsources>
  <worksheets>
    <worksheet name='Country wise Category'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#4e79a7'>Country wise Subcategory</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <mapsources>
            <mapsource name='Tableau' />
          </mapsources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='English Product Subcategory Name' datatype='string' name='[EnglishProductSubcategoryName]' role='dimension' type='nominal' />
            <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column-instance column='[OrderDate]' derivation='Month' name='[mn:OrderDate:ok]' pivot='key' type='ordinal' />
            <column-instance column='[EnglishProductSubcategoryName]' derivation='None' name='[none:EnglishProductSubcategoryName:nk]' pivot='key' type='nominal' />
            <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='mark'>
            <encoding attr='size-bar' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' field-type='quantitative' max-size='1' min-size='0.005' type='centersize' />
          </style-rule>
          <style-rule element='map'>
            <format attr='washout' value='0.0' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Square' />
            <encodings>
              <color column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' />
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='has-stroke' value='false' />
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-line-first' value='true' />
                <format attr='mark-labels-line-last' value='true' />
                <format attr='mark-labels-range-min' value='true' />
                <format attr='mark-labels-range-max' value='true' />
                <format attr='mark-labels-mode' value='all' />
                <format attr='mark-labels-range-scope' value='pane' />
                <format attr='mark-labels-range-field' value='' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:EnglishProductSubcategoryName:nk]</rows>
        <cols>[federated.1nl426t13auwkc10rmst40m1iuwe].[mn:OrderDate:ok]</cols>
      </table>
      <simple-id uuid='{FBA62C8A-85C9-4E21-9A6B-2FDF81235A85}' />
    </worksheet>
    <worksheet name='Year wise Production cost'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#4e79a7'>Year wise Productioncost</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
              <calculation class='tableau' formula='YEAR([OrderDate])' />
            </column>
            <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
            <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
            <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
            <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style />
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Bar' />
            <encodings>
              <tooltip column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]' />
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</rows>
        <cols>[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]</cols>
      </table>
      <simple-id uuid='{BE630D19-559C-4DCB-8C0F-DD4C4A6186EA}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='AdventureWorks'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#4e79a7' fontname='Tableau Medium' fontsize='20' italic='true'>Adventure Works Sales Dashboard</run>
          </formatted-text>
        </title>
      </layout-options>
      <style />
      <size sizing-mode='automatic' />
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='98120' id='150' param='horz' type-v2='layout-flow' w='99034' x='483' y='940'>
            <zone h='98120' id='115' param='vert' type-v2='layout-flow' w='99034' x='483' y='940'>
              <zone fixed-size='63' h='8343' id='116' is-fixed='true' type-v2='title' w='99034' x='483' y='940'>
                <zone-style>
                  <format attr='border-color' value='#000000' />
                  <format attr='border-style' value='solid' />
                  <format attr='border-width' value='1' />
                  <format attr='margin' value='4' />
                </zone-style>
              </zone>
            </zone>
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
          </zone-style>
        </zone>
        <zone h='81904' id='147' name='Year wise Production cost' w='48521' x='1026' y='13631'>
          <layout-cache cell-count-h='5' non-cell-size-h='133' type-h='cell' type-w='fixed' />
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
            <format attr='background-color' value='#dfedeb' />
          </zone-style>
        </zone>
        <zone h='82256' id='148' name='Country wise Category' w='46047' x='50392' y='13631'>
          <layout-cache fixed-size-w='401' type-h='fixed' type-w='fixed' />
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
            <format attr='background-color' value='#dfedeb' />
          </zone-style>
        </zone>
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <layout-options>
            <title>
              <formatted-text>
                <run bold='true' fontalignment='1' fontcolor='#4e79a7' fontname='Tableau Medium' fontsize='20' italic='true'>Adventure Works Sales Dashboard</run>
              </formatted-text>
            </title>
          </layout-options>
          <size maxheight='700' minheight='700' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='170' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='84000' id='169' param='vert' type-v2='layout-flow' w='84000' x='8000' y='8000'>
                <zone fixed-size='63' h='8343' id='116' type-v2='title' w='99034' x='483' y='940'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='81904' id='147' is-fixed='true' name='Year wise Production cost' w='48521' x='1026' y='13631'>
                  <layout-cache cell-count-h='5' non-cell-size-h='133' type-h='cell' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#dfedeb' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='82256' id='148' is-fixed='true' name='Country wise Category' w='46047' x='50392' y='13631'>
                  <layout-cache fixed-size-w='401' type-h='fixed' type-w='fixed' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#dfedeb' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{5CA7A8AA-0B19-4C3D-BDD1-8F8F88AD34F6}' />
    </dashboard>
  </dashboards>
  <windows saved-dpi-scale-factor='1.25' source-height='37'>
    <window class='worksheet' name='Year wise Production cost'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:ProductSubcategoryKey (DimProduct):ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{C7F402EE-DCD6-4F97-8C69-574EEEC04E1F}' />
    </window>
    <window class='worksheet' maximized='true' name='Country wise Category'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:EnglishProductCategoryName:nk]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:EnglishProductSubcategoryName:nk]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:ProductSubcategoryKey (DimProduct):ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[yr:OrderDate:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{4A47DAF1-BB62-437E-8259-B71B137B39DF}' />
    </window>
    <window class='dashboard' name='AdventureWorks'>
      <viewpoints>
        <viewpoint name='Country wise Category' />
        <viewpoint name='Year wise Production cost'>
          <zoom type='entire-view' />
        </viewpoint>
      </viewpoints>
      <active id='-1' />
      <simple-id uuid='{DF6D76A4-4DF0-4E63-AF57-74BF98439136}' />
    </window>
  </windows>
  <thumbnails>
    <thumbnail height='192' name='AdventureWorks' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAXp0lEQVR4nO3da2xbZ57f8S/vV1GkRFFXW7LkSHLk2zhOYjuOkXUyyXqS7CySnW2nO4sJ
      tsC+mKJFgRYoCmw7KbAFCrToq862xRRFG0x2tzOLTLCTdiaZycTJJE4cx0ksWZZkWRJFSSRF
      UryT4uVc+sK2JpObFVKy4pz/BxAgyf6f8/DR+fE8PHzOQ5Ou6zpCGJR5pxsgxE6y3vxG13U0
      TdvJtghx220EQFEUfvjDH3LgwIGdbI8Qt5X1oz8cPHiQkydP7lRbhLjt5DWAMDQJgDA0CYDY
      WrqOoqhod8jVdQmA+MJ0TeXvX5vgR69Oo3zs35R6lX/2n37GQrbW8PbPf3iN8aVsc40E/t8b
      E8wly+i6xs/OTnBxIY2u67x2fprZRAmQAIgvStc59+4U566u8uK7Czd/SbVaZfxanPcmwpgD
      QVz1MuFkEdBZjqdZWiuhaRpzS0muRrPouk61UmF8PkU6V2R8Pkld05mPxPnfPx9nejlNIl9l
      LpJgrVijVqkyPp9E03Wm5uLk1mtcXUqj6TrpTIEPZuOs1z9yGV+v88KvJikqJq5MzvOTN2dZ
      yVVZja7y/JuLdAVcKHXld68CCXErhVye589F+N7j+/n3L81gBWKxJH/5/DsEAi0sLCV5/LH7
      +fVblym1dPGnx7r4D8+f4598+yT/8X+9TUYxEV5J86/+/AzrS2H+y6/nGenycmkuyfe/d4Zf
      vHKJnG5lamaZg7v9/Ofnz/EX3/sGs1PX+NnVMgN+C3/51+8w2uvF5AvywNIKL1xYQa1VGRwd
      4l8+uR8ApVhiTbHS5bfyX19a5OhQO91tHv72l+f5w0cOUc1k+P7fvCNnALF5uq7x3EsXOHV8
      DItax2m3omsq/+PFCzz52P18/zvH8DrsnNjXTaWm0Opx8MIvL3Hf/WNMfThN2uzmj06N0uuz
      o6gqb04s88wfHuefPnGAVr+PwZCX3nY3Dz8wxvefOUU9vYYnFKLXZ+PN8SVOHuzn4pUlnB4n
      33zkKH/+QDc/fHWOp0/v5/hwiLry2zPAWqaExefl2vhV7hobolxcp5JNM7du59H9If77T8/z
      2CP3SgDEZulcvjzHazNpfvryBf7N31zEabNSzGSYiNe5fzTE9NVlHB0d9PvtVGoK+Uya9xMq
      37qvj3NTcfra3UQSeY7dO8Jup8rUmsLRwTYuTC6xb7gPt17n7ZkU9+/rRtd13pqIcGz/LkrZ
      LBOrde7b28ab40v86ZP3cXC3n/enlunoaCWdKeD0+fjmfQMbrV3Nlmj3WHnpgzjfPNpHrKDy
      q7en+Ee/fwi1mOPSSoV79gZlCCQ2p1ou899+PsWz3/sG+7u9vPn2BC9MFVE1HV2t8u9++Cql
      XJ5HTt+LCZ31qsJv3lvgX//ZI7isZtpaHFy4HCHsc3Bw/xAz15bpH+wlYIe3Lq/w+48/SLVS
      obBe5wf/5xz/9pkHmFnKUYpf5u23VfoHe7CUsswWzPzzAT8ArV4X0dgCv1FqmO0unjx190Z7
      E5kS8ViKb3zrFI5aiZVUAVfHIMf2+FEqZdpdOn/xg19gujkbtF6vc/78eXknWHyq9XKFRLFO
      f6gFgEKxTLaisavdw+paAavDwXp5nUCgBY/dTCyZp6qb6Q+1YOL68bWULOByOenyu1jLFjHZ
      7LS5bSyu5ugN+bFZIJUuUlJ0dnW0kMkWqZus2HQF3WrHY9VJFhV2dXgxAeg6sVSeiqrTE/Th
      sP52QJPNlUgVq+zuDmBWFcKJAu1+LwGPHYBSucJqriIBEMYmrwGEoUkAhKFtvAg2m81EIhHO
      nj27g80R4vYyyS2RwshkCCQMTQIgDE0CIAxNAiAMTQIgDE0CIAxNAiAMTQIgDO0T06GTySS1
      WuP3cwpxJ/lEAF5//XUOHjy4E20R4rb7RACCwSDDw8M70RYhbjt5DSAMTQJwg67r5EpVADRN
      pbheb3qbi8tJctVbr7hdKFVIZkuUax9fZeczthtdo6purg1zkSSluqz6/VkkABt0fvrLD4kW
      alz88BofLOeb3qKm62xmru2PXnqPi9fi/M+/f4948dbBuzARpvw5Wcll0rz4ziIAuqazVdN9
      X379Q1bXt2hjXxJyU/wNJpOZJ08M8ePXpzBrGv/4iTbeeHeacKZKZ2eQY/1e/u97EfKlKk9/
      /fD1dW/WKxw7dpB9QRs/+vVVnrq3lx+8Os+/+IO7+btzS/Q6atgCQd547RIl1czwUC/OaoEL
      4SxOj5t/8OBdmACz1cbpo0P41AqLySKv/GaBbL7C46f28fr7C4CJR0/eTSy8xOVYiehqgYdq
      VX787iJ/fGIPf/vGHGcOd/N3b80R8Pvwmcucn8oS9DtZX0vj72zj4vtXieSqeFp8PDzcynNv
      LNDmgO49uxhwKrw2lWCgr4Pdbp03r62hY+ZPHh7hr38xjsNl5/59fbw1FWdx/TJ/8MAIXS22
      nf6TbQk5A3xEe0c7nmqBw4cGsWo1zl5ZpTfYwvuTi9QxYbOYqJRKzCbLxJM5HjxxgH0hNyaL
      DbVaYTK8xu42F5MLCdqCftbXqygaVGoKJouFoM/JKxfD9HW0EJ5bIX3jarNar/LjX00wnVYZ
      6/YSWc3z7cePMj8b4cFjYzx9bBevf7jEews5vvvYIUa6WkDXKaxf30ChXOPcpTC/d+Junjq5
      l3uGexgb6uXkaCfl9RpqrcKVZI3vfP0Q5XSaXEWhsyvIt07uZTGe47XxFf7ksUM8fLCXs5Mx
      vvPoQQ50WBlfLlKpqTgdTtrbfBzuD/D4g/u/Mgc/SAB+l8lE0O+l3ecAs5WAy4bP6+Lkgd18
      OLlIR2+IwZAHXddpafGyp8ODyXS9dKTDxVtLJX5vpI0XzkU4uKdtY7Njd/VyZKCVn59fIOhz
      Ync6OHmoH8eN3rfYHPzxIwf4szMHcVtNdIYCdHjtdLV5mA4nuLKQIhRswaIpLKzmSOTWwQTF
      YoX5aJpyXaO7zc3EfJJwLIvZaiVXKJEuXn9Ng9WGuV5hMZElV9NxWG402nT9K+ixcnkxzWIi
      T5vLwvRKltl4kc4WO8e+NkjIXuc3V1O4HFaWElkqX6HXFJZnn3322Y/+IhwOMzAwsDOt+RJw
      u+y0+dw4bBb27W5jOZmnq6OVA0Mh4okce3Z30NPupdPvpr3VzY1DiXa/hxavk6G+NtxOB3d1
      t+B2OQj43GjVCpF0lYfvGeBrd3UST+bx+Lz0trkxm8DncdDu92w8G/ncDtp9LkJBP8p6Gd3p
      5fhIByN9fuaiOUb3dNId9BF0mVnXLYz0BRjb24O2XiZX0xnoaaPFolFUTPR3+mjzeRjbHWBm
      KcOpI0MEPHZ8XicBrxOfx8l9d/cRi6cx2x0c399HZDnF3qE+9nR4WEvnqVicnNrXxZ7edpZW
      UrS3+3BavxrPnZ+4JfLs2bM89NBDO9QcIW6vr0aMhWiQBEAYmgRAGJoEQBiaBEAYmgRAGJoE
      QBjatgdA05p713An63Vdp5mVIzVNa7q+GXdy392uvt/2ADS79Oid/kdsxp0cgGbrb9eThwyB
      hKFJAIShbXsAphZT270LIRq27QFQmxxHCrGdZAgkDE0CIAxNAiAMTQIgDE0CIAxNAiAMTQIg
      DK3hhbEyyTixRIqOjg7iqwm6+vpJLl9j112HSC1fo4qdkb17Nv7/O++8w8rKCj6fD6fTyfHj
      x4lGo3zwwQccPnyYcDhMLBZj7969zM/P8/TTT2OxWLbkQQrxWRoOQGt7kFQ6TTqZY3hslJWF
      GB1trdTVGrrJjYc6H13kr62tDZvNhtfrZWJiglKphM/nw+PxEI/H6evrY3BwEFVVURQFs1lO
      TmL7NXyUXb18iTp2WlvtXJ26isluIxqNsZrMUCmlSZfWN9KlaRrvvfceqqpSrVZxOBwkEgmK
      xeKNRaZayOVy9PX1cf78+aZnAgqxWQ2vC6SqCpqmY7Fa0VQVi8WCoiiYTGbMZtAxYTGbuXQt
      xqG93Z+oz+fztLS0YLq5tNpnUBQFq7XxJUybqb85pbbRs5GqqpjN5ls+xs+yk4+92Xpd19E0
      reFhrKqqmEymhvteURQsFsst+77h3rFYrNx8bOYbnWSzbX7NSJ/P1+iuhdgyMtAWhiYBEIYm
      ARCGJgEQhiYBEIYmARCGJgEQhrbtAdi/J7TduxCiYXIGEIYmARCGJgEQhiYBEIYmARCGJgEQ
      htb4ZPFNujAd5YU3phuu19Ex0dh8+q2ob8bH9/31o4N8/d6hHWmL+HTbHoBcqcLM0tp27+aO
      8LW7una6CeJjZAgkDE0CIAxNAiAMTQIgDE0CIAxNAiAMTQIgDK3h9wHSyTix1QSBQBuptQyh
      7j6Sy7PsHr2HYuwqNWeIPT3tW9nWrwRd1zl79iw9PT0MDw9z/vx50uk0g4ODzMzMcPjwYS5e
      vMhTTz0FwOTkJOl0GqvVis1mw+/3Mz09zfHjx2lvl/5tVsNngFZ/AKvVRim/zsiBfZTzBbpD
      QVRNp6u7G01TtrKdXxnRaJRarUY2m8VkMrF79258Ph8dHR1ks1k6Oztpa2sDYH19nUgkQqlU
      YmZmhlgsRldXFw6HA5fLtcOP5Kuh8bVBpy6j6GZ8rU6mJ65gdTlYWl4mFo+zsrRELBpFkQ+I
      /ASXy4XH46FYLBKLxXjppZew2+2srKzQ2trK7Owsk5OThMNhMpkMoVCIQqFAZ2cnmqbx8ssv
      YzKZZO3ULdLw2qCb9fK7s/zVixe3bHt3sn94eoxvP3IAgFqtRrFY3Hi2/7hsNovVasXr9Ta8
      P1kbdBvXBhXNsdvtn3nwA/j9fhRFhpHbTa4CCUOTAAhDkwAIQ5MACEOTAAhDkwAIQ5MACEPb
      9vcBToztYv+ezobrVU3D0sRHpjZTr+k6Jmj4Q+40TfudN3Ja3I6GtiO2z7YHwO200eJxNlxv
      5E+JFNtPhkDC0CQAwtAkAMLQJADC0CQAwtC2/SrQmxMRfvTKRMP1OjS1smcz9TdvlPi0+tP3
      DPLth/c3uGXxZbHtAajUFBLZ8nbv5rYrlKs73QSxBWQIJAxNAiAMTQIgDE0CIAxNAiAMTQIg
      DE0CIAyt4fcBMqk4sUSazs4QsVic7l0DpJbn6N27n2w8TH5dZ3R071a29UunXC5z/vx5LBYL
      p06dIhaL8eqrr/Lggw8yPj7OmTNneO655zhz5gyhUIipqSnm5ubo7u7m4sWLjI2NkU6nOXLk
      CL29vTv9cAyp4TOA1+enxeMkGV9j774Rsqk0fp+HmqLT3tGFrbEFwe4obrcbq9W6sVThysoK
      vb299Pf309LSwvLyMv39/WiahqqqlMtlvF4v+/fv58CBA+i6TqVSafh+A9G8hnt+fnaG4rpC
      sMPH1clpXF438XiC1dU4k+PjmC22rWznl1I+n6der+Pz+Zienubo0aP09vYyNzdHMpnE5/Mx
      OjqKpmlEIhGOHDlCT08PKysr3HvvvQSDQQKBAE5n4zcMieY0PAQaGTu08X0w1A1AT2fw+i8G
      +5pr1R3C5/Nx+vRpNE3bWKx2eHgYgKGhoY07wlRV3Vin8ua/A4yOjjI6OrojbRfXydqgW+BW
      Q5hmbukU20sGn8LQJADC0CQAwtAkAMLQJADC0CQAwtC2/fpci9vBUE+g4Xpd15taWa2Z+s+7
      JzjY6m64TeLLY9sDcP++Xk7s391w/Z28NKL48pO/rDA0CYAwNAmAMDQJgDA0CYAwtG2/CpQr
      VUhk1xuu11QVs6Xxu2uaqdc1DUymxj8hRr3xCTENXsXdycfebL2u6+i63vAVNE3TMG2i74f7
      2jGbG79Mvu0BuDAd5a9evLjduxEG9eNn/wiHvfHDWIZAwtAkAMLQJADC0CQAwtAkAMLQJADC
      0CQAwtAaDsDNNzo+70uI2yGRSDA3N7ex0t7Zs2dZW1vj0qVLZLNZUqkUU1NTwPXjNhaLUavV
      OHfuXONvhGVTqyxHo7hcLZSq6/gDnWSis+weO0Y5FSGWyHHk3q9t2YMU4rOMj48D0NfXx+Tk
      JPV6HbPZTD6f59y5cySTSe655x4AYrEY7777Lo8++iiDg4ONnwHcbjdWhxer2cLo/n2o1Sp9
      vT0AeD1uMJt+e0uVENuoo6OD+fl5NE3j8OHDPPDAA7zyyisUi0WcTieBQIB4PI6u6/T09OD3
      +3E6nczPzzd+BlhaWsJssuMLeLl6eZpQdx+xyFXsNTsOrYLL6ZDjX9wWNpuNo0ePMjs7SzAY
      ZHJyktOnT3PlyhV6e3up1WpUq1UuXbpEIBAgl8sxOztLpVJpPAB7R8c2vm9rDwHQGby/+Ucj
      xBd09913/87PPT09KIrC6dOnP3UyXX9/PwAjIyNyFUgYmwRAGJoEQBiaBEAYmgRAGJoEQBia
      BEAY2rbfExxsdXN0pKfh+p1cG/TzVwfd7n3f6fU6OmBqtO/Qb1R+fn0zN8TDbQjA4b1d3DPS
      +Gfg3slrg978kLxGD6KdfOzN1uu6jqZpWBpcVUJVVUwm07avyypDIGFoEgBhaBIAYWgSAGFo
      EgBhaBIAYWgSAGFoEgBhaBIAYWgSAGFoEgBhaBIAYWgSAGFoEgBhaNs+HfqmQqGApmm4XC5K
      pRKBQACAcrmMy+VCVVVqtRq6rlOr1fD7/U3NZRdiMxoOQDoZJxpfpbU1QCabJdjVR2p5lt37
      jmKvZZmYi3L/kQMb/39xcZG5uTm8Xi+KonDixAlMJhM/+clP+O53v8uFCxfIZrMMDAwwMzPD
      E0880dRcdiE2o+EhkK/Vj9Vmp1quMXLgbiqFIj2dHWiqwuzCEhZUVO23iyMqisLw8DBOp5M9
      e/awvr6O1+tlaGiItbU1ZmZmWF5eRtM0BgcHG76RQogvouEAXJuZQtMttPpdTE9cwe52srwS
      Jb6aoK+vF8tHhi+aphEOhykUCvj9fiKRCNlslng8TjQaJZ/P88wzz3Dy5Enm5+cpFouyvLq4
      LUz6x460s2fP8tBDD23ZDlRV/dRn82q1it1uv+U4X26JlFsiG6EoChaL5ZZ9v2ODbIfDsVO7
      FmKDXAYVhiYBEIYmARCGJgEQhiYBEIYmARCGJgEQhiYBEIYmARCGJgEQhrbtUyF0XUdV1Tuy
      /uY0qUYn5t2cS9Sor0LfNVN/86vR+s30/7YHwGQyNT21eafqNU3DZDI1PJntZm0zN/bcqX13
      8+BtdDJbs30PbGoi4rYH4E79A0Ljs0C3qv5O7rutOHibsdm235bXALHlReKpDLGVJeqqTmw5
      QqX+2adGXddYXIyg6RCJRNDRCS+E2ezJsFYpMXv1KrlCieWVFXRNIby4tOn2plajXJsPk1tb
      JVOsUM6lWE0XNl2PrhJeXCKTWKFQUcmtxUnn1zddvhyZZyGyQjK2xHpdI7W6QqFc29yudY1I
      eJ50rtRQ36VWo0xfuUwqtUYslUWrFlmMJjfd9uxagtlrCxRzaySzJaqlLNFEZtP1a4kYC5EV
      8mtxsqUqpWySRKa4uX0no2SKNfLpJPOLy+QzKbKFMuVcmkQ6+6k12z8dWilTrNugsgbVAqrN
      QzS8gDPQidP26SlV6zWKhTw6sLQwT3urk3AsQ/+egU3t0myx0Rn0k0ytEYsu0+IwEU1mGejf
      tal6t8dDPJEig0JZq+DW81Qt7XS2tWyqfiWySKZQwYKdWnEFcy1J3WmjzefaRLXOWmqNQOcu
      isUcZTVBMRGhtc9Hi9t+y+p8coV4ushASzvxpTCtrV4WllbZtWeAzTwnBjt7KJXKmGtFlnMK
      SsFEqaYCHZuohmQyhVKvkUyp5FULZUuVsu6iJxTYXH0mh8Osk6hZUaji1HJUrSFCAe8tay1q
      mWi+gqWUxWsxEcmouCmArqLqEGrzf6LmNpwBTGiagqZp2L0BctE53P7P70yr3YnLcf2P3d0V
      4IOpFbrab90BN1VKOZIFhf7eEJ3tXqYW0wR9zi/QZAsOq5maomOlimqyf4GPetPIZHOkEzGq
      igW0MibrF9i3rrN3dIxcJoXZ7qZaSGBz+75A2010dveSSSXoCLUzNTVHqGNzBx+Arlap6Tbs
      ZhNuCxQ1E9YvMJKpaxpet5NaTcVuUqli3VTwbgr6W4gnM1jMFixUUc2b7/sW7/UnKE3XUDQN
      M2BCw2S2fOY2/j/QEoGMfS3P8QAAAABJRU5ErkJggg==
    </thumbnail>
    <thumbnail height='192' name='Country wise Category' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAgAElEQVR4nOy9x48k2Zng+TPXWntoLVOrEix2UXU32WyB6Z3B7gJ72D9kj30dYA972kMD
      OzurGo2ZaUWyq8hismTqyNBautZau5u5me3BM4NVFZlV4Z6VZLHDf0ACmZFh3xNmn71n71OC
      qqoqffpcUDS/7w706fP7pK8AfS40fQXo8wfP0e4GDx8/RVI+v5tvcbAfPPO71UyY33z8GUeh
      BAC6300X+/R5Pci1FJvBEn983c3du58itapMTU2QzsXJZRTymTCK0QGtAuidTFkrWEavsvv0
      Y/TCG/0VoM8fOKqKoNGi1WloNyuIRh/1+DFX3vwOVLOs7Bwjt9tEokneevPWs4sEtBoNoWAI
      oX8K1OcPnd2NZbIViTduzBPJSYy5YGXnGIfDR6ucxugaQofE1StXqKQCPNyJMD4xjb6V7ytA
      n4vNH+QWSFUUZEV5LbLldptms3kqX1VVZFl+LW0piozUbqOqCqIofWWfXvU1JYkiL3rXtdtt
      ZFlGecUGFLlNW5ZRFQVRar+SrJchy68+D19G+zd/8zd/882KfP2kj5fZiEiIxTgYrFQKaeKp
      AnqNChoNlWqdZDSAhIFWvUq5kENUdTTqVUxmM8JXyH7863/hJCfi9bqJBk8wGPT84z//Ix7/
      KJLYoFqp0my2EBsN2nKTo0AUh91MPpsjnS9ht5ooVeqYTcavHUd88xP+891d5lxt/te//a/c
      vjxJOJHDpFPJ5SsgSIQiKR589hEarR6Hy41W81W9fwlyg//tP/5HRq+9RauQJJWvYBBkcvky
      6+srZKKHiOZh3FZD97Kfsf7Rv/LRfhpbK8J/+qeHLIy5SBVqKJKI2ailUGliNvUuH+Cz93+G
      4BrAbNBSKpUp5TLkSg1MOpmTSAqPx/WV9/ZF/MGeAslinVA6zP3VfQxKHZfeyJHbzNUr19jc
      C2FT6xzcW8GhbXDt7e9x+HQdo0HPn//FT79Gskqr1WL/yScc5mo4j2PojBZWH35KoV7h1p3v
      crS7jRWYuLnA9pNPSeaucbyXYHHcTCziwD64gNtpP8coBFwWA3uREjPjHpLxKJ89fMTUyDAV
      0zhUI/zgRz9GEiUa6SA7Ji83Jzxdz1V8b5mxK7fZ2Niknoww4jax06ijcc0hl4sMuM1dyzyD
      xoBJp5KoyIw4TcSjYR4+WeXywhyC3oB37g4e56s3s/P4M7TfeZtHD9eQpDZmJJqtIsgKWuf/
      zKzP1F23X71Lv1ua1QKru2E8ZpF0VcFuNmJzOLGaTdhtZnZ29pClKpFkFafdgtU1wNXFRTza
      Cvaxha99Q+iMZu68+Rbjo4NYrC6uXJpBrJUQBSM2h5vrl+eR6iVS+TLHh0d4/H4EVBauXOPd
      H36fpSdbXJ4ZPNdYBK2WufEB4sUWVoOe4+NjfD4vgkbH9Vs3GXBYOApEMVtt2G3WnudsN1rm
      nTdv0yrGaJQLpKsiTpuNG7euY9Lp0Gp1aHpZWT6HVqtjzGej3NZi0Gk4Pgnh87i48sbbPF0/
      5NLIqz396ViQbFNgzG9je3MHNFqcTicmowGPx8Po/HX8Vm3Xci/IR7DMvY8+4fa7P8Rq6H6S
      zkshHeYwVuPt25dfWxuvRpvPPn3M93/w7u+sxWwiSDDT4s0bi7+zNrvhVAGq1epr+9h7FfaK
      KUTl9XxU9elz+g1gs9l+n/14Ke1GDlG+AItUn98Lf3DfAH36fJOcUYBkLEw4mkBqNajW6lSq
      9TMXFXJpQpEYsnL2zVwuV15PT8+JqqgkwhEqxRLQOUOPB8Ooikq1VCabTNGWJJKR2AvPxc9D
      s94gEYqcXp8MR0lGY4gtkVgwhKIoZBJJWo1mz+NQFIVSPv9sTAqxYBix2aJcKFIuFJFlmUQ4
      0vMYxFaLWCB0er3YEmk2GiiKQjwUQRJF8ukM9Wqt5/7HAqHT69tS+3TOc6k0jVodsdUiHU/0
      PIZGrU70JIjybOveliQatRqKLBMNBGlLEsVcnmqp/FIZZz6C//7v/j+m/GYEzzQCkMrVuHV5
      GqQadcwszozz8//6X5i9PEm6ZsNvbGDxjePQiSRLEvs7G7z9xh0MekjkGly/usirHDDcT57Q
      lF9uJPoybUkiFY1xsnPA9/7yJ6w/fILT46ZcKFLKF5i5vEA2mcZkNmOxWZmYn+26T9GTIMVc
      Du/gAAOjI3zy8/e59vYbyO02lWKJerWGoii0RYm3/+QHXctXVZX9tU2SkSg/+uu/pJjNUSmV
      iYc6igxgspixOR3I7TaLN6933UY8FKZWqaLRaJi5vMj6wyfoDXoEQQAEBsdGON7ZQ5EV3vnx
      j7qW36w3yKXSBA+OePenf8rag8eYLGYURaFWrqDIChqtBoPRyNjMFN7Bga7byKcz5DNZ2pLE
      4s3r7K1u0KjVmFyYo1GrEQuGQe0o4zt/+kMEzdkNz5mf1CtFkoUGFq1Ivlxn69FvyLdl3vvX
      37C2soIMtMUGT1d2mBjzkkrn+ejDu3zyYIX56XHysUM2T5Ic7+1RKBb5XW/fdXo9lVIZjbYz
      NIfbRT6dIRmJIrZaHG3tgqoydWmeeq23t5vJYqbVbDEwMoIgCEwtzLH9dBWr3U4pX8DpcTMx
      N4PR3N2Z9HOqpTI7y6tkEkkkUcLucpIIhZleXMDpdeP2+2g2GsxeuUSz0eipDavdTiGdZXJ+
      FkEQWLh5DUEQyCZTIMDW0gqjU5OYrZae5JssZrLJFEZjxyAoCALTlxZIR+OMz85gspgxGI1M
      zM9S7XHXYLHbiB4HsDkcCILA3LXLaHU67C4n0UCIiblZ/CND2Jz2l1q6z1iCjw/3sdvt2F1u
      VK2BwZFRqoUiFhNMzCwyMjRANpPn+tUZEqkMqVwNp92O32mg3ASDyYRWajAyMUY0FGJ8ZgGj
      rvclIFIt0FbP7/bQrNeJHAdQVdBoNFhsVjKJFFffvEO5UEBv0OP2+dhf32Tm0kJPN/jxbz7F
      aDKiNxpo1OokI1E0Gg2pSAytTs/A6DC7K+tYbDYGR0e6lm8wGrl06zogoKoK0ZMgzVodm9NB
      NpGiUaszPDnO1pNlJudnsTkcXbfx9ON76I0GDEYT9VqN461dEqEI05cXySZTjM9MEzo4Qm80
      MDwx1rX8cqFIOhZ/tqKARqthb3WDK3dusbe2gcliRqfXEzo4Yv76FfSG7q3EsUCIVrOJTqdH
      brcJHhwROTqhXCwiCAJOj5voSRBVURidnjzty+f51tsBut0CfZ5iLo/L273l9LyUCwVsTiea
      Fyyt3xSvewzVcqWjzD08gOdBVVTKxSJOj/u1yAdo1OsICJgs3Vu0/00rQJ8+X0f/GLTPhaav
      AH0uNH0F6HOh6StAnwtNXwH6XGjO2AE+vvs+gUAYo0EgliqSzBYZ8H3xCOtwe4294yBOu4Vy
      Q8FqfvERWmBvC53Ni1HXu551aweQRJG1+48oZvP4hgcpF4osf/oAp8fN3uoGqWgci83K9vIq
      wxPjPfUpsHfAwfoW3sEBdHodTz76lFq5gndwgKcf38M/MkQ+kyUWCPZk4VQUhdV7D8kkkgyM
      jtCWJJ58+CkjUxNsPn5KMhLFYDSyeu8RngE/BuPXR599GVVVeXT3Y0anJwE42tqh1WxSKZXZ
      XVnHZDEjiSJHW7sMjA53Lb9Rq7H24DGNag3PgJ/oSZCdlTUcbhdbSys4XE4KuRx7q+v4hgfR
      6rqPzYoFQ+wur2O12zBZzGw9WSYWCOLyeVn57AGj05Psr2+RTaTwDg280A5wptVwKMKw302+
      PECtLhGJxKnVqsiVFFXFyE/++Pvs7e5y5Tt/TDm0xr9sNvn337/EztYmk3NzHIbTmAxmrFQ5
      ODxmIV+lqcDb33kbq/71Lzh6gwHf0CCpWBzoPKzf/fGP2Hj8lKtv3ubpJ/fR6rSn/iO9MDE/
      S7PeoFapojfoKeUKePw+YoEglWIJsdkichRAUXprQ6PRMDg2SvjoGFSVdruN3mhAbsunPk6B
      vQO+8+MfsfVkmZvffbvrNqLHAUr5PKqiIGi1uP1+0rEYc9eukI7GqJTK5JLpnsdgtlpx+bxU
      nvnh5FJpDEYj+XQGi81Ks9Fg8/FTXF4PitxbfLd/aIhUJEa1VMbp9VDKF1BREVsttDotjVqN
      4+1dJhfmXirjzBNptrsY8TvIZLIUKg0Ce+tozAaWnmxhNppQgB/92V+S2n5ARjQwPTNH8GiP
      eilFIJrCMThBOZcgka8zMz6I3e2hko5Qqv9uzvLlttxxFag3UFUVQRBoNjp/X73/iFt/9DZW
      u/2Fb4PzcrC+hdvvxTvoRxAEfvBXPyUdTxAPRijm82wvr9Ko1QgfHqP0ELyvKAp2t5O2JCEr
      CmaLBa1Wiyy30RsMGExGJEmiWW+80L/lPMRCYUr5AulYAkEQsNg7EWeKLDM4Pkpw/5B6tUpg
      77CnOBFJlBgYGab2zM2h1WgyODZCvVY7tb6brVamLy0QC4R6GoOsdPpayhdQVfWZ5d+GVqdD
      0GgQhI5Pk9hsvfSFd8YQtvzoM0RMXFmcolRvo8gqUquOKlapY+HG1QWC+1tkyiK3bl5hfW2b
      AY+VQrmCd2AYnclOs5wHqUam1GR8dIhQOMrNN97C0kM0VreGMLHVYvvpKi6vh0atzvSlBXZW
      1li4cY3d5TXMNgtun4/Q4REL16/iH+l+ed9aWqHVaODyenB6vSTCEewuJ1MLc+TTWRxuJzq9
      nnQ8wUAP8mVZZuvJMhabjbYkMjg2yu7qBiOT44itFqqqMj47zf7aJtfefqOnLRBAJpEEOg6E
      6ViCZr3B2MwUyWiMhetXsbucPY+hXq2xt7qOf2SYaqmMb3iQyHGA2SuX2F1Zx2y1MDE3Q+jw
      mKtv3kFv0HfdRiIcJR4KMzI5gSSKtBpNJFHENzTA4dYuk/OzSKKI3JaZvXrp4rlC9OnzdfRP
      gfpcaPoK0OdC01eAPheavgL0udD0FaDPhaavAH0uNGdcIf7z3/7vRE/2EXzTiMU0Wr2OX77/
      K1x2Ex9++gR9LcJ+xYquVUDQmynnUrQUDSaDnkwyRlMWaLea5HI5zFYb6XiQ9379GVevzHP3
      vZ+TzFVxu2wkUlnsDjvpRAxREZBbNYq1FhpVJJUrYbdbEejdFeJwc4fJ+VlUVeVn/9ffYXM6
      WProM0IHR5itFu7/6i5zV3vL4Ha8vcfG4yWsDjtWu43A3gFPP7mH3mBg49ESkiiytbSCqqq4
      fN6u5T93hdhZWWdqcY6j7V321zaoliqsP3xCvVojGY1ysntAvVrDN9S9u0Xo8JitJ8sIGg0m
      s5n7v7pLcO8AQRBYvf+IaqnM/vomUkvCM+DvWj7A6r2HxE5CjExNEA2E2HqyTKNe52Rnn6Pt
      XVKRGKloDO9Ax6WkW9YePCZ6EsTmcmAwGvn4Z+9RKZWplErsLK/RajbZfLJM9CTI+NzM+Vwh
      FFWl0WqT3HmKzjtIZn2TYqmIyailVKqhHTOQOl5nNRbFNzhGM3vM2z/6K1x2M3tb62wEcwzZ
      9cyO2NncOURrtQMqtKtUZQs/fucNIkfbfHz3I8bm52lbxijH7lHK5mhrLUwMOrANzDA8PEAv
      SQz1BgNWu/00HcfBxha+oUGkloiiKmjouBn4hs6Xv/NFeAf9HGxsYbZYaNTqpKJxjCYTHr+P
      3ZV1qqUyYqtFIhxhcmGua6uzRqPB6fVQzOY716oqeoOBVCyOf3iIXDKFZ8CP2Gr2ZEAC8A74
      2Vlexeb4bRLfSqnM8OQ4+UyWwbERYoEg8VD4pUakryKTSCK3ZeRnrhTeAR+7K2vMuBcxGI20
      Gk10eh3RQIhapdJTAgHv4ACR4wDFbB6704l3aJB8KkOr0eR7f/ET7r3/aybmZqhVqiiKglZ7
      9ol64RbIYTNTb6tkk3H0ZgcOk5ZaW4u2XachttEZTbg9Pm7fWGR+eprHS6uoSp2TcA6nzYTO
      YMLncqCioZjLIMkK6GyY1AoPHq+wvbvHyMgwkixQzKZoiQp2h5M333qLxYUFtpYeUe/RVadZ
      b1AtV5AkiWI2h8fvQ1EU8pksWq2WdrtNLBDicGvn1BLaLaloHLvLSS6Vplap4hsaoJQvkIzE
      cLhd6HQ6UOk5l31bkihkciiqQj6dwTc8hPIssFtRFNrtNo1aHYvNRr1S7XkMHr+fdDxBs9HA
      M+DHNzhAq9HEbLHg9nk74+gRk9mMxWGjUixRyOZIxRI43C5igRBLH98DoWPxfpX6C8/dG8RW
      q+MOIcvIchur3cb9X97FPzJE+PCYYjb30rjtM5bg574rgiCc+tL8FhWe5Vc+/T9Vhee/8/m/
      P7/i2e89l/d5mWKtyPL6FqrRyR+9cf30clUFQSMg0L0l+PNtJMKRL3p8/rb7X+hXtzxvI5dM
      4/J50BsMp8mdvjxnryL/82M4ey++mTGU8gWMJhNmi/kL9+75/ep1DJ9vIxWNMTQ+dtrfz8v9
      JsZQr9aQ220cbtcX/u8896HvCtHnQtM/BepzoekrQJ8LTV8B+lxo+grQ50LTV4A+F5ozCpBL
      Rni6sskfalEiVVWJHJ/QqHXqGpRyeZY/e0ClVOJ4Z49oIIiqqhxv7yGJYk9t1Gs1IscBoHNs
      vLW0QmDvAICjrV0kUWRnea2TabnHMSRCv61xAJAIRSjlC+yvb7G/vkW5UGTlswc06mfrN5yH
      VqNJ6ODotL291Q0ON3dQVZWT3X3EVouDjS2SkVjPYwjsHXxhjo93OnMe2DsgfHhMLBBi+dP7
      PddRkESJk939TnuKwvrDJyx99NnpPa9Vqpzs7ncS5L7ksPNLCiDxyb0VblxdJBsL8tGHHxEN
      HXH3k3usbh2w8uQeG7tHfJvPTRVZJhWNUy4WATBZLIzPTrPzdLWTyTkco5DJsru6TqvZ28RX
      S+UvxLFOLcwRPQmSTabZW9sgEY5itloI7h/2NghVJZNIks9kgc6N3n66QiGTZWxmklwyxc7y
      GmMzUxysb/XURKVU+sIYxmenScXiFHN5dpZXSccSKIp6qujdIomdYiHNZw93MZtjd2WdXCrN
      zsoagkbD0dYOVrsdvbG3xLyVUol4MAyAoNFw45230On1HO3sMT4z1XEbqVRJRV9eDOVLCiCD
      RofBaCCTSFDPx9ndOcDgmSR8tIPUalEolvg2a4BWp/tCSnKtTksyHEWj1WIym9Ab9GwtrSCJ
      Iulooqc2BkaGT10QNBoN+XQGQRDYXlqm1WySCEUwW63o9L25KQgaDUMTY6clXbefrnQqrgRD
      lAslFFXBYDQQD0d6yogM4BsaPHU/EASBUr4AqsrmoyXabZnoSQCz1dKzq4XBaMQ/NHT67+dz
      Hj46YWJulngozA/+6qeYrRaiJ8Ge2vD4fV9Ib1/M5nB4XJitFhLhKHqDAaPZ1Ml8/RIF+JKt
      28S1hWEePFzGbtXj9I1gNagsb63w1q3raJt5xFZPff2dIbZahI9OMFnMlPNFLDYrrWaT4clx
      sokURpOJ7//ln5GKxnD7fD21cbyzRyGT42R3H5vDTjqewOqwc+uPvkMyHMXl97L5+Cn+4aGv
      F/YCZFnmZGcfFZVmo8HNd96iVqk+KwkUwGQ24x0cIBWLMzzefe5+6DjDFTI5AnsHmMxmkpEY
      ZquF2+++QzaZwuZ0srOyhvNz1tVuqFerJCJRJEnCZDbz7p//mFQ0jsvrZmtpFYfLxeHWLvl0
      mlvvvtNTG/FgmEImR3D/EL3BgKIoTC3Ok4rGiJ4EGZueIpNIojcaXpo942stwc1Smnzbwoj3
      91NF8lUswWKr1XPGhPPK1xsMr5Ri5TxtvM4xSKKIVqd7bTUOVFWlLUo9b3POQ1tqI2iEFzq7
      fR19V4g+F5r+MWifC01fAfpcaPoK0OdC01eAPheavgL0udCciXlbuvcRba0DDXUu3fkuTmPv
      YXHfBnaW16hXq1y+fbNjXU1n0Gi1lItF3v7RD3o2JH2ecqHI4w8/4cqdW4QOj7HYbchSm1wq
      zU/++7/uOYPzc462dslnMkwvzrO3tonRbMLl9ZCMxHjj+3+E9XNxvb3QliQ+e+/XjM9OMX1p
      kX/9u//Cn/2P/57P3vuAd3/6p1hsr34EHguECB4csfisIPfmk2WGx8fIJJJcefM2Hn9vNpnn
      qKrKymcPsDoc2Jx24sEw05cW2F/bxGKzcvt73z1PUHyNaEbkP/yHN/j0lz/neOsxT1d2mL3+
      BmL8iJRkwCiVUBFYmJlkdXefqbEp9o7DzAw6qRld/MVP/hj7t0hptDotUkukVChSyhcQNBoa
      tRo2u4NEOML0pYVXkt/xK9rF5fUwOj1JqVBgamEerVbL4db2Kz/80LE2y7JMo944dd9o1Oq8
      86c/4nhnj8t3br6SfFmWMRgNVMsVDja28Ph96PV6xmenkXvM3f9lVFVBo9FQr1TJptLY7HYq
      pTIDo8ME9g5eWQFajQbRkyBDE2PEAkFsTgflQpFWs/mV9oEv3R0TglwhGU/RBsrFAhOLt5jz
      aMjJVjSyiNHmYnzYSzabZebKW8xP+pi+fId3vvddzPUkiXxvzlmvC6fHzfDUBOHDYxq1OoHd
      faYvLdKo1XD3kLLky0iihCRJHG/vdrJBNFuYrRYONreYv3b1GxgBWJ12xmemiRydnPZZbLYI
      7B9+IQ62Z1SYvrRwms0iFgyRisaplSvUK5VXl0/HJ2tqcY5YMIzYanG8s8fYzBSVYqlni/nn
      0en1eAcH0Gg0WO12RqcmCR8eMzg2Srv9ctfOM4awVr1CLF3E73FgsliIhcN4h0YpZlJYHE60
      goBOC0aTmWg4jG9wGEUVMOsUQvEM09NT6DTfnGX0VQ1h9WqNbDLF+Ow00HlzNhsN5Hb7G5n4
      37ZTRW8woqoKeoOBRq32jWwdoOO5mYxEGZudJh1LYLZasNisZBJJRqcmX1m+qqrEg2EcHhd2
      p5N6tYZGqyF6EsRkMTM2PfXKbUiiSCwQYmx2Gp1OR73aSVVSzhcZnhz/RqzpxWwORVFwej3E
      ToKMzUx3aje4nThcL35R9C3BfS40/VOgPheavgL0udD0FaDPhaavAH0uNH0F6HOhOaMA4ZMD
      9o9fXre1UCh8myMiURSF4MERxWzu9GfFbA5JFElGYpQLxdPU3/Varac26tUqwf3D0zjTRLgT
      wC7LMoG9AyqlEvtrmz3H03YC+wPkUmkAxJbYqeTeaCK2WtQq1U5w+ef60C3lQpGT3f1Octp2
      m8PNbY6391AUhWIuD0Bw/7DncEVZljne2aP6rFB2MhJlf22TcqFIcP+QYi5P5DjA/tpmz4H9
      APl0BvnZOX8hmyMeDJ+2LbZEsskUpWfjeRFnFODx0iojQ35S0QArGzvEQiesr6+SzFfZ3Vjl
      v/3Tv5JPRVla2SAdC7C5c8jx4R5r2wc9D+KbRJFlrDYbm0+WUVUVSZT45Be/JHR4TDqeYOPR
      Evvrm52C04beopSKuTzNRpNkOEohkyWfzrD+8AlrDx4jNltotTqGJ8dPA7a7RVVVjCYT209X
      URQFsdnE4Xaz8WiJw80djrZ22Hz8FINBz87yak9tyO02iiwT2DtEo9EwPDFOKhYnm0ix9NGn
      tNttjrZ2sdp7s2W0RRGH28X6oyUAXF4vPIs9tthsbD5ewjc0QL1WQ2r1lp2j1Wjy4T//glql
      iqqq1MplYsEQyXAEu9PJymf3Ce4fsru28dKC5WcUoFLMchwI8atffkAksM/yk6f4fA7u3v2A
      YFHF57Lywa9+STZ2wtNHD8hJRuLhAIVi6VuxMuj0+lN/H4Cljz9FURQKmRz1SpV8OsPs1cs0
      Gw0CPWZt0On0NGo1BsdGsNg7KcDzmSzlQhGEjt9LJpFk4ea1nuRrNBqq5fLp293yrAiH1WFn
      /voVBEHA5fOSCMeoFMs9taE3GIieBLHabQgaDXK7zcDIEAOjw3gG/Gg0Gi7dvsHG46c9yTea
      zSRCEYwm47N/m6iWyozNTJHPdO6PyWJBVRTsLmdPbdz75a/RaLWkop3ULW2pfVon4GRvn5nL
      i7j9Pqw220tXyjMVYsrVBu+8/QblQha7bxS7QcvQ2AjVJoilNIqgZ3LEh87mx2vVMrZ4BzMN
      QpEki5fmv/GPim4rxDTrdSLHAQRNJ/X21Tdv4/Z78Q0NUq9W8Qz6UWSZbCLJyOQEVnv3jmRP
      P72P3qBHp9fRbsvUShX8w0P4BgfIJFKMTI5Tq1QYm57qycLZlp7nu+lc22o0KReLnSIZ0Rjx
      UJjRqUkKmQyX79zsyaEvfHyCJIroDHoUWSYTTzJ37QrJcJS99U0sVivJaAy9wcjo1ETX8suF
      IqlYHK1O14nZBQwmEwajkchRAEGjQW/ouC9YbNau5QPMXF7E6rDh8fso5vLsrqxhNJtJx+Ko
      qord5eqswgKMTE5cvErx5ULxm/GVeQmVUgmb3f6NOLy9jNc9hnq1isFo6qlE0XlQFZVqpYzd
      2dtb/jw0Gw00ggaDqfvkAf+mFaBPn6+jfwza50LTV4A+F5q+AvS50PQVoM+Fpq8AfS40Z+wA
      Rztr7B0GMFhNHOyGGB7urUr4N0W3doC2JLF6/zH5VBr/yDAAq/cfYbZaO6nLQxHEZovDzW2c
      bndPR2cnu/vsb2zhGfCh0+vZeLSETq8jFY1zvL2LzeVk6aPPkGW5p7BLRVFYu/+IVCTO4Pgo
      +XSWjUdLCAKkY3EqpTIajYb1R0+wOR2YzN3bASJHJ+yurONwuzCYjOyvb5KOJdAb9Gw8XsZi
      s7L5+CnVcqWnSvTNeuNZxfnO9fl0lq2lZZwedydRQaVGNpnmaGubwbGRnvJ6JsIRdp6uYbZa
      MFstbDxaIhGOIGgEtp6soNVp2V/bJJNIMjg28kI7wJdWgCabe3GG/U60WplwIMxvPvgl733w
      IZ9+8gmh3WXuPXrKhx/eZXl9nV/87D3CiWzXHX+daHU6rr51m9qzAtKFTJZSroDYbHH1zds0
      6g2K+QKKLCP0GLo5tTCHzeGgWW8A4Pb7qFeqlPIFZFlGaok06vWe/XQEQeDqW68aR4YAACAA
      SURBVHdOZZTyeTQaDcVsnoHREUq5PJuPnyIIAmKrt3Tdo9OTeAcHqJUrz14IO4it1qnlNxGO
      Mjg2QibeWwp5g8nI5ds3qZU7lurNJ09RVZVMPEksEKLVbJLPZBienOjZZWRwdISh8VGq5TKK
      LCOJEqgq8UAIrU5LLpmmVq6cpn5/EZov/7MtNfGZFR7vRFBaVSqyCU2rjNUId58GEDPHVGQd
      NosVu01PMBztqfOvC0kUWX/whDd/+D0EQSAeDNOo14gFgqx89pA3f/AuM5cWWLhxjeD+UU9t
      7K5uMDg2gtvvQxCEU0vs7JVLzFxeJB4K8/2/+DOSPc6NoiisfPaAN3/4LhqNhlK+wNz1K1Qr
      ldO2TBYzV+7cInLUm8Pd0fYuVoe9U4dAIzA4NoJGo0Gr03L5zg1azSaVUplqubeg+Eatzv76
      5mnqc7PVwqVbN0iEo4zPTlMtV5icn2N3ZR3T53L8d0Po8BhBEJiYm0Wj1eIZ8BELhqlXa8xf
      v0ommcLhcaPT6176MvqS+c/An/7obXaCcb577TLVkVFUqUSdaeZGPRhdaS5NDbK8toXL5aJe
      cjEw8epB2d8kcrvzZj/c2kaRFa6+dYepSwuoisL+2ib7G5sMjY8RC4a5+satntoQNALhw2PK
      +QJOj6fzEAqdhzIRibF44yoHG1vMXbvck3xVUdBotOyvb6HRaFi8cY39jS0Wblxjf22TZr3B
      1Tdvc7i1w+U7N3obgyAQD4VpNRpYbFZGJidoNZss3LjG3voGizevc7S9y9t/8oOe5LclCUVR
      ONndR2qJXH/7DQ42tnnj+39E5DiAd3AAud1m+vICA8+2qr2QTiRRn41Hbre59e53cHk8HG5u
      8+YP3iUeCuP0eHqvD/D7pm8J7vM66Z8C9bnQ9BWgz4Xm25PD8CWoKijf6k3axeDfwi140Zlf
      fwXoc2F4kRL3FaDPheaMAiiKgqqqX4ihVBTlW78EqqqKIssv/fvz+OC2JH3h/7vh+dxAJ+j7
      +d/bkkRbaqMqCu12+3T+ejlge94vRZZPg707Rh4RWZY7f9qdtiRRfGms69eNQXl2/fM+KorS
      kfu5+eplDJ3rO9c9Pwp9Lv/53Mvt9qns5/PVDZ05kM/cR0VWTu9LW5JQn7Uhf8W9PuMK8c//
      9C/Mjpj41cMQDn2Llqrn7vvv4x/wkkymqFfLCHoLpUyMelvDx7/8F/ROP/V8CkkwYDF9s+Uw
      w+d0hcjEE9x7/y5z166wt7rB3uo6kwtzFLM5Pvynnz/LFb/B0kef4vJ6ePjBh8xePf85fVuS
      uPsPP8Pt96GqCv/wt/8n1966gwr85h9/hkarIbB/SGD3AL3RwCc/e4+RqUmMJtO528inM3z4
      z79g9tplHv36QySxjWfATy6dYW9lnchxgHKhyM7yGtVSiUQw3JUrhCLLfPKL9zEYTRxubJNP
      Z3B6PWi1Wh7/5mOa9Tour4d//D/+b0anJnnwq99gtlq7itmNBUIsf3rvmSvFMslQhLGZKRLh
      SCcrRL7AzvIamXiCVDROYGcPl8/XlTvH5qMldlfWsNpt3P/lr5m7dgVVVXnwq7uED45RFaUT
      Q223kY7FefLhJ8xduwKc/Q448xFcyMR4sgzNksB/+8UqLo8Xuw7uffghwyMOcI2x/HidaDaF
      xenFrVNwWnS8/4uHvPP9P8Hr7C2+81UZGB3BPzJEKV8gl0qfvgncfh8jU5PoDQamFucxmk0M
      jY8RD7w89cuL0On1LD4Lcl/97CH+kWGa9QZGixnf8BDRQBCn241Wq6GYzbF46/qLv7q+As+A
      n+HJcZr1Ovl0lrbUMRT5hgbJxBJMXpqnUih2AthHRwgdHJGOxXF63OeSr9FquXT7JoqsMDA6
      TOT4hGImh9PrIZ/O0Kw3KOULnfjpWo3Ld27SbUjz2MwUqWgMo8lEs14/Ded0+7zsPl3FaDJx
      5Y1bpCKdQPbr33mLYjZ37jEA+IaH0Op0z+55x4imKAourxeL3cbBxha+oUGOd/aolSvo9J24
      Z80L/I3ObIF8QxO8/eYtjCYrLrudWzevo9VqMRgsuF1OHG43RoMRl8PB7ZvX8bhsRFNFpsf9
      rKxvdTdb3yCxYIjj7V3KhQKT87OoikI+nSEd6zioxQIhAnsHzFy5RCwQ5Gh7tys/l1ajwd7a
      JkdbO8w9y8zQbDROU3Kjgm94EEVR8Q0Nsb++yeHGdlfL+/O+5tNZbA47Wp2WQjqL3G6fFsx+
      8KvfAAJis9X11qEtSeyurHdyDDWbp1uFVrOJzelAq9UysTCH0WxCaonsrqxzsNndGIL7h6fz
      bbFZabc7lXJS0Tg2lxOdXs/20xVazSaKIrP+4DGegfM7XCqyzINf3UVVFeLBMMfbuyTCEcr5
      AuV8gehxgLlrV2k1mxiMRmauXPrKLfy33hJ8L3FCo0dLcCoaY3Bs9Bvu0W/JJlO4vB50ev1r
      a+N1j6GUz2M0mTBZvtofp9eHRFUUMokkA6MjPUr4emqVKoosn2ur9uUF7d+0AvT55vhWPyRd
      8GUF6B+D9rnQ9BWgz4XhRd/z33pXCEGAb7DkWJ8+X6C/AvS50PQVoM+F5owl+PjoGKdVSyhR
      wf05o1YsdILe6kSv/e1+RJGa1ESFZOQQvc2H/gVxzbsbyxyehHH7BzHoPvcLssRRKIbH/dVH
      V90GxcuyzO7yGrVKBZfXgyRKbD5ewmyzcrKzT6NWo9losrf624DwbkmEIxxt7uIbGkBAYHNp
      mUqxiOFZSnOn183agyeIzWbPQfFbT5YpF4p4BjrxxmsPHqMz6J/V7q0iSSLbT1fxDQ2i1XW/
      k42HwhxsbOEdHECr1XKys08pn6dSLHGwsYWqqoQOjmi3pZeWGP0qWs0mG4+WUGQFh9tFKV9g
      d3kNq93O1tIy6VgCRVEJ7B/gHx7qKb9qOp5gb3UDp8eD3qAnl0xTLhZRVZXNx0+xOR2c7O5T
      r1RxetwvDIo/owB3P/iAIY+BJ6sHSBoTifAxqWiAew8eotNqWN84RKHJ6soGcjnCP3x6gM8k
      oSKwsryMrAqsrCwTiqaZmhzj/v3HvHNliI/WYxTjx+TrMtGjLcq1Jlu7BzTqdaLBA7IVkSH/
      2YelWwVQFRWby8HJ3gFj01NsPFxCEqXOWb2qkorEKBUKGIxGhqcmespGoNFoEEURRZaxORyY
      LGZOdvdJRmPodDrsTgd7qxvMXr6Epcf8+kazmcDeAeOz04QOjqiWK4zPTtOo1sjEE2QTKS7f
      ucnO8hrDk+Ndy9fp9TRqdcSWiMlsJhmN0ajVmb1yibYkUStXcLhdRA5PGJ+b6Vq+gIDJZCJ4
      eMTY9BS7y2tMX14kFY1x+fZNMokkxUwOj99HvVrrKQGwVqejLUpUSiU8fh+lfJ5UNE4xm2Nw
      dJjd5TUcbhfpWILR6cnzZIWAVqNKMpVBb/dxsvoxxZrEYSTDxJCPfCLM2JVbpMMhwofrYPEw
      OTlNvZhleWmNS7eusv50iYZspJJNIgNivchGqMKdaQeJbI7V9WWCsQoz40NE9pYpq2ZqlRLZ
      r6ji0Q2KIrO/vsXVN24jCAKKojB79RKJUIRCJkcyGmPu6mUGRoY52trpqY10LIEgCAyMjtCW
      2+SS6U52BlVldHqCyHGAH/67P2drabm3Mcgy2WSqUzhCVRkcHeH6d95g8/ES/pFOce+BsRGe
      fPxZT29/gHKxSKVU6ji+oZ46jrXbbUr5AvPXrxALhE6r1HSL2Gp1XFKeV2kXBKSWiKDRcLyz
      x8yVRRxuFzvLq2h6zK5dK1coFQqoioIgCJ2VRBAQNALZZBq720UxlycZeXlygjOGsFQqhd9j
      J1dqsnz/Y97+yb9DykfJlEWG/S7MTj+VdIhSQ2Z8coqTgz0G/F7sbi/72zvMX16kWm2CLOIf
      HCSdSjE4OIgsNdjZP2FgcAhBrFCTO45zalukWS8j6ezMT521eHYbE9xsNNheWsXqsKHICou3
      rrO3usHctcsEdg8YnhxDEDREjk+4fPsWGm33k3+4uU25UMTl9eD2+0hGYji9bvzDQxxu7rB4
      8xoHG9uMTE3g8nq6lq8qCrtrGzjcLiqFEjNXFtlf22T+xlWOtnaRWi0mF+ZIxxNcunWjpxoE
      6ViCZCTK8MQYiqKgPAvEd3rdoHbSmuyurDM5P4vN6ehaviSK7CyvMTYzRTqWYObKIoHdAxZv
      XadcKOL0uElGojQbDSbn53oaQz6dJXx0zMTcLNVymUqxSLPRZOH6VU5297l06zqB/SOGxkdf
      eh9ebglWZaq1FjZbbykrvin6QfF9Xicvf/0J2t/7w9+nz+umfwza50LzrbcE/y6C4nvYfnbF
      t9vd8GLTXwH6XGj6CtDnQnNGAVRFptHsLePw7xNVVWnUOtmUy4Ui9WqnCryqKBQyWVRVpVIs
      0aw3UJ5Fi8nt7gLjW81mJ/uzKFLOF07bLWY7NYglUSSfzj7L6FzoqQL68zEUc/nTDNdyWyYT
      T1Cv1mg1m5RyeSSxUwVdkbsLihdbLdrtNm1JIpdKn0Z71SoVKqVOtftcKo0iK1RKJVrNZlfy
      VVWlUa+jKAq5VPoL19erVRRFoVwo0qjWKObyZOIJJLG7QtmSKCGJImKzRSaeOM2QXSkWn9ke
      5NMK8plE8gvj/DJnLMGtUoqPlnYwyFUqooZ6pUihWCIdC9LCQCkdIV+XqVdLaOQm+XKNw6MA
      /gE/mtewme4mKP7Jh58wOT9L9CTA8fYu47Mz7K9vUS4UiRyfkEkkCR8cUa9UqVdr2F1O9Ab9
      ub4B2pLEJz9/H7ffx+7qOvlUBrPNisFkJBmOsrOyRi6ZolapUi1VqBSLHO/sMdGFFTWfznDv
      /Q+Yu3aFRCjM3uo6E/OzhA+Pyaez2J0ONh4voTcYThUvHgydO2JMkWXu//IuRpOJWrlMLpWm
      Wirj9HoIHx5ztLWD3miglCsQOT4hHgyTjEQZm5k+9xhiwRCbD5cYnhwjm0hyuLnN+OwMrWaT
      f/1//p7R6UmSkRhH27u4vB721zZx+ryYu8gQ/fTjzxCbLaLBEAgCdqcDrU7H0kefYbXbCB8d
      06w3SMcS6PR6tp4sM3N58XyWYIB2Pc17v37I/QcP+PCD95AFhbXVbQrZGP/6wSc8uv+AtZUl
      PnnwlNDhFvlSGbH9+/3SGxgdwTs40AmAVpRT03er2eTqm7ef5e1vUch2rMG1SoXoyflTi+v0
      +tPMAgajkdmrl6gUimi1WhS1Y0XVGQwUMllsTjttqc3EbHcuBJ4BP4Njo6eWUUVREADf0ADt
      dpv99U0K6SyBvX0MRiPRQBCH9/zB5Bqt9rR6vcPtplIsMT47jUajwWA04hsaxDPgp5jL4R0c
      YHJhFrO1uyQHY9NT2N1OTBYLlWIJg6GTJeTJbz7BZLVQLnR8dcZmpvCPDGN12Lv2l1q4cQ2N
      VsPY9CT5VJrYswQHQxNjHG7toNXpqVWqnRrLLidTlxZeamj7ggIoYp2nqxtMz1/H77Fx+dIl
      fEMTTA8PMD45ykkwwYDXyaXLl3jr5jzJisLE2ASVVIRC7fe7bYoFQ4QPjzne3uVkZ49Wo0kq
      GsPt93L//V8zND6K1W5nfHaa8dlpWs1mVze31WhwtLXD8c4egiCw+WQZq8NONpmimM11HloV
      jGYT2USK4P4hzUajqzGkY3HCh8eEj47JpzPodDqSkRj1ah2p1cLucuL2+9AbjGSTKax2O83a
      +bdZbUniYH2Tk909lj+9j1arpZTLk44l2HzyFEmUWH/wGFXtpHo/2tqlLXVnhAzuH56OQXyW
      tygZifHWH3+f6UsLaHVaDje3aTUapGNxxmamurICq6rK7uo6J7v7VEpl2lLHzyubTFEtV9Dp
      dHgH/Egtkdmrl4gGgsxcWnipvJ5jgk92VtF4ppgaOv8bqBdeJSY4n86cK+NArzu3YjaH3e36
      Woe6VzkGPe8YeqVSKmE0mTAYu/eKPQ+qolLIdZzeXheNWg1VBYut+5Q8/aB4+naAi0z/GLTP
      heZbbwlOVEQq4uv7vtAIwmuPOe6cVL6+ZaBjLX+9y4z0ms3xiqwgid3na+0GjVZzJjL+W68A
      y5EK2Xp3Z9HdoNMI6Htwie6GVlt+rXl12oqCJL/eB7TUbL9W+a2GSClXe61t6C3GM5VB+1ug
      PheavgL0udCcsQR/9Ov3GJyYY/nT99B5prCZXnLEp8hk8kWsz+rWqorC5uYmg4ODp78SPdpm
      deuAakshEQ2Ry+cY8HdXdfxuIE5devHyq6oqiY0VGqUCOpOZ5NYaJoeLzOEeeqsVncFAOREj
      c7RHs1KiFA2TDwXQaLWkD3aw+vzodDq0X/ERUIiGyRzuY7Q7SOxuokgS5VSi8zObHa3eQGxj
      lVohh95kJn20h80/SHR9Gavbg0anQ/6a/XNye51KOoVGoyG1u4XeZCZzuIdYq2Jxe2lWyiS3
      1kCjoZbLUE7EsPr8pHY3MdrsCDrdV3rMSvUaqa11DDY7heAROoMRWRTJnxxg9Q+iKgrpnQ1q
      mRSqopA72EFvsZLa2aDdqGN2e2m1X26NV1WV4tEeUrWMWKtQChxicLjI724gt1oYnW6ahRz5
      /U2MLi+5nXUUScJgd5LdWsXsG0CRVVqNl5/2eSxGbo/5qbYkro96mfU5qYsSt8Z8pKsNZEVl
      3G3DZTbQbMu8OTFAXWwz4bajEQTqYhutXnfG5nDmGyAeOuTg5AobSw/Bf5mtpQDugTHEah4T
      0BAkUBT0Jiuf3lvif/of/jsCR/u4RuapVGrc/dV7SKLKD//yr4hHAuhsIwROAnhsBlLZFGKt
      gt3hJBKPMzoyzGEoyZ07bzLs6z7sDqCSTuKbXSCxuYrebO6kwdbrkOo1jFYbjuFRZEnEaLVj
      crlJbq9TTsTwzS6QOz5k9Or1r5afSiBoNDSKORyDw1RSSUZv3iG+udZ5+DQanCOjZE8OYWwS
      qdlEVRRQVdqiiM749fUBKqkkzrEJqplU57pWE73ZQjWTxjM1i8FswTd/idzJEZVUHNf4FI1i
      nnIihnNkDP3XtJHaXkej16MqMlq9HrFWwWB30m4+M9QJAvbhUXJHe3hnF2iWCtSyGRr5HDb/
      4FfKBhArJarxMI7JWZrZNLaRcerpBCbvAM18FpimGo/gXrhG8ul93PNXqUQ6VvhWKQ/nKPIx
      43OyGs1yc9TL42CKNyYGmPTaWYlkmPM72U7kqYsSEx47I04rKp0DjobUxm0xkqm+2Ch5Zgvk
      GZkiuv4prrF5Ysf7DC1cJ3yyTzKZJB1PkMzkmZ0ZpVCVmJ6Zo1VKUKnVOAqGyGSylOsiDq1E
      RepMrNXmQGo1yKTTyPUi++Es+xtPaQt6BI0OndwgnS997QS8jNkf/phaLgPAwOI1CqETDJbf
      ZmJQVZV6PofZ46UYCeIam8Q1PklqdxPNOQLKR67dwjczR+bo4FnlFAWp2UCj13cC0lUVrd6A
      LEkdhRAENFotBuv5skGoqsr0935Es1zEPTnD4NUblJNxZEmkUewkCmhVyxRCJwxevobF7UUQ
      BNK7W8itFuVk/GvbEDQa/ItXKYaDp/163tfnaLQ6UFQUuY3BYkVuNpj+4U8oJ85R7V5VsQ6N
      IlZKWAaGKB7tIWi16Exm5NMTPBVVkbEOjdLIpZBqFaqJCK1SgWYx97VNHKaL3BrzoQJTXgeh
      fBkBAa1Gc2pnKTXE08O2zViOWZ+TcuOrHe3OPAGLl29QLRbx+6xgGSRysM3tt75LOXlCtSkw
      PejCZvcxP2unkgqhMXvw+FTc/mFoD4GqYNGCTQfjU3McRdNcvXEDQeqY7G0GkDRGItE4VqsV
      l8uFx2X/+kl+EapKZn8Hi9uLfWiY1O4m3pl5sod7aI1Garks3pk53BMdc7vebMHi8VJJJTDa
      HHhn5r+2iWo2TSkZZ/q73yd9uI9jaASp0cA/O08pEUer01GMRbAPDFGMhhHrdUrxGLVcBlmS
      GL7y1SsMQO74AKPNjlivUQwH8c0tUggFGLlxh8zhHgarDVmSqOUy2AaGkJp1Jr/7A5qlIrpz
      VKDxX75OZm8bz/Qc+ZMDBK0WWZKQGg3yxwforTbquQwml5tWuUQ9l8U5MUVmbxPb4NenNTc4
      XNTTSYzOTkC9ye3F7PVTPN7HOjhM4WgX+9g0pcAh7oWrFA938F27jdk7QCOXxuT2Ira++ghU
      oxFoKwobsRwei5FMtYkoK1wd9nCcLTHpseM0G7CbDKxFs1wf9XKUKTE/4MKo03CYebHcb70l
      +H/5zVLPx6Cqqn6tn8mrHIM+n7qva+NVjkHPM4ZXOQY97xh6PQY99xz9no5Bv/V2gFehl1Qb
      3yb5v4s2/tDlvyr9Y9A+F5pv/QoQOUkTL72+pVGj1SC8Zkuw2u6tZOq55WsEVOH1jkFqNF9r
      mZh2o0UzXXx9DQDqkPfMvf7WK0Ct2qRa7s6vviu0GgRd9/lBu6Itv94aQxoBekyReF7U1xzv
      IdebKJXXeJ8BxSWeudf9LVCfC01fAfpcaM64Qvy//+lvCRwf4RpbQKoW0OgNNKolGpKCXqOy
      9+QugZYbeyvE3/3LPQ4O9mmW80SO1mk7pjEKbSqlIhq9kVq5gIwWg7735fnvH+1Saf7WmDE/
      5ObKiI9wrowgwF/fmSNTrnNlzMfVMR8tqc27C6OIbYVSo8W1MR/Xxvy02jJ3pgbRajRcHvEy
      5LKRrTSQ4Qu56bUagZ9enaLUaFEX28z4nNwc86PRCMz4XVwb9dGWFW48+1mx3uKdmeH/v70z
      e47jyvLzl5mVte9VKKCw7yRAShTV6k0dPTMeh6dtT9ie8JNjHvwv+cET4RePHRMxjnCPo6fd
      q3apWxIl7gCIfSsUCrXv+5KrHwpCSyIlAiDRZjTxPWK5N+/NPHlvnnPu7xByO/A7bSwMBWl2
      VSIeBz+YjrJfqD2m7DUZ9nJzYpB4sYZFFPgXCxMM+90M+lxcifZP2F2NBpmK+EmU6lwfDTMT
      8aPpBm/OjTAW7AexboxF0E2TeleBL43BIVv4D69Ns5urYJjw2liYxeH+df31K1PsF2q8OTPM
      QjRIptbCIVt4cybKiN9F1O9iIRqkq+r82fwIiq73A0xfS0cZCXr44dwI+7n+vv17M8P4XTas
      ksSrExECLjsTYR/fnYmyle4Huv71jWkUTeeV8Qg22UKl2eE//XCRw2KNbqeH0fqDu9sqS/zN
      n9/gKFdB0XREUeBvf/IGiWyFH9+YwSJJBH1O3lgYp9Hu0lM1/s2bi0xGQ4S8Tq5NR1FVjWvT
      UebGIsQzJSw+18m9FgWBv3p16vEVQFEVdE0ns/OQB1uHvPPeh6wv3eV//P0/8s67b1NrNNEM
      A9PUUTUNTTepFvN0ej3e+c2v2V2/z//6p5/xy3fe5Wc//RnZ4vORPf+C3WwF9VgKxDRhPVkE
      oe9vfniQJehy0OgqhD39HCWrReLjrSNuTgxily1E/S6qrS5um4zT+rhh6obJZqZ0onDhd9r4
      3U4Sv8NGtd1FEgRS1SaKplNp9ZBEgdGAmwG3g67aD+ZEvA4GvS6a3SfntsSLdVq9/u+8Dht7
      uQodRUM4vt6OotFWtJPvhnqn34/XYcUiiYiiQKHRxue0nbTzZTqqxkHxD9F1URC4F8/jc1jZ
      zlWOfwZWSUQzDCRRQBAEwm4HVklC0QxGA24q7R4DbscTx5AqN6i1//BdsHqURxAEMsdzkyw3
      2MmWWU/2I1DTET/5ehuLJLKRLAAm18YGSJRqSE9wQiiqzvZh7uS03vXpKIlsheGwF6fDSnTA
      S6HSxO200VU0DNNEEkUkUcBikej0FKJhH62O8sQTf4Zpsp4sPm4AgfAQNpuEpqoo3TaYBvvJ
      AtGQD83Q6Cl/mHC7w4VkKvTzpAQGwz7K9Q6jE/N8/7XrXLs2x+27D584gedlMuxjMuzF57Dh
      ssnMDgaYCvuxWiReGRug1unR7Ko4ZAteR1+R4HszUXayZVS9ryOvGSaaYXy1Ys0xFlFkZsDP
      ZMiL126lrWh8b2qInqbjtMogwI/nRlCP9fRdVplKu4dumAhAW9Vw26wous5Y0IPlCYl2Y0HP
      yRgM02Q64sdtl7HJEuVmlyF//2yrbBHxOqwYJnQUnaDL3jcUQWDQ6yJZbjDofVxOxGWVmQz7
      GA148NqtiILAjdEwHVVjOuxlMuTFIopUOz0iHieKZpCvt8nW28RLdQzTJFNr0eqp2L9h9Y76
      3UwO+Ai67HjsVuaGgkwO+PjRlVEAdMNgdtBPLF/F47CiaDp+p52I18nsUJCpAT9dRSPidZ28
      rL6M3SozMxpmIhrC67LT6ioMBvuJbaqmIwoCAwE32VKdwaCHoNeJomqYwFGuggnkKg1EUcDy
      BAOTRIHZocDjkWBVVZFEAd0ETVWQrXZ0tQeChEUS6CkqVpsdSTBptTvIVhuSAAggiBKGbqCp
      ChbZiqYqSLL1mbZAf/Nffkaq0vzDxMj97E2/006h3kK2SBimSU/VkS0iPVXHYbXQU3VGgx6O
      SnVssoWuqmG1SBjH2xGLJPTf2F/zAgmA09rPrhzwOEiUGzhkCx1Vwy4f/78AsiRhlURMoNFV
      kEQRVddxyJb+2xuQBAHdNB/zAtksEhZJxOewUWp2ME0T3TQxTbDJEl1Fw2G1oGgGwwE3yXID
      m9xfGWzH4zVNsFrEfl9f8wJJgoBdtmBiEnTaSdWa2CSJnq7jlGV000DTDSySiN9ho9zuIgki
      PU1DEkVEQaCn9cfS0/R+f62vemisFglZEnHZZDqKhn5cpELVDWSpfx9MTHTDZDzk7W9ZjyfY
      YZUBaPdUREHAME30dhf1eHWC/qrlsMuYpknY7yaRrZz8rfX4PghCP4rvcthodxU03TieGxOL
      JNJVNJx2GUXV0XQD21jk5F4LgNMmv/ipEF83gOfOpRv0VHzdAJ43XzeAi+DLBvAFl16gS15q
      XvhAmN9tu9gKMZKIcI5CeWfB1I2L1UYR/wjRbIt5oauYYRVQtbNphJ4VPwQ3XgAAGDtJREFU
      q8/+2L1+4bdAfwolkv4YE/xip5y9uFxugS55qbk0gEtear4WCTZJ7O9wmKtiqB2cLs8TJc8V
      RTlXgenzcNZC2dAXss0l03gDfjrtNmt3HxAIhzjaP6BWKiNKEntrG/1K7+eoUZuOJ9hd3SA4
      OIAoimwurdBptXF53GQSSdw+L5sPl5FtNuzOJweSvg3TNMkmkhSzOQLhEIZhsLX0CFmWsdnt
      xLd3sdpsbC2tEB4aRBTFM2+B8qkMm8uPCIRDWGSZ+PYu9UqVeqXC7uoGkmwhdRAHwOk+e7Fv
      Q9d5eOs2mqrgCwaJ7+z15detMrHNbXRNQ+n1SOztE4pEznVuQFNVVu89xBcMnAgJl/MF7A4H
      q3cf4A/3Zd+VbheX1/PEPr5293t8/NkKU2NDLN25xe3PPiWVL3H704/Z2I3z6OFdbn16i7/7
      r3/H4eEBH3/yKaX6xYlWnQfTNFm795B0PAHAxv0lJuZmWLv3AH84RPYoyfbyKuHoEHvrW+fq
      Y3tlFV8ocCIoOzE3S3x7l3KhSGxrm6O9GMFIhI0Hy+dqX1UUlm7dPpFJ311dp1YqI4giW8ur
      JHb32VpaYfLKHKt3z1eMe3tlFY/Pi93pRFVUCukM6XiCkckJBkdHyCfT2B0Odh6tnav9SrFE
      KZsnGOmrgEzOzxKODmEC0wtX2F/fIraxjWy1kk2mztXH0qe30TUNAQHTNEkdxKmVyjz45DMm
      5mb4/L2P0FSVg62db0xH/6oBmDb+8s9u8sv/+1s0i5XJES+fv/cOenCWnfVlVtc2+e4PfsjU
      1DR+O9TrdTpPCMX//6SQzlLI5Djc3ccwDJweN/sbW7h9Xo72YkzMzzEwEmVvfQOldz7j/bO/
      /gkAqYPDvpTJUQpR7Fcqd7pcDI6NcLizR71yvvx2TdUIRsIUMllUVaVeqXL15qus3rlPbGOL
      TCJJIBxiZ2X1zBVcvuDNv/qXeAMBdh6tYZoGvW4PVVUwDJN8KsPVmzfIpdKU88VztR8YCPMX
      //7fsvLZHaCvEl3K5RmIDpE6OESWZXzhIPHtXdRzSl922m3mri+yvbKKaZpY7Xacbhd2h539
      jS0GR4Zp1uoU0tlv9MJ9dQtkKKxv7mJ1ehgaCDIQDmPzDdHM7BMemSAUDDA+NkIln0Ry+NF7
      TWRvhJD37Mv8aTnrFsjl9TCzeAWn20W1UMLudNCs1fH4fGSOkoCJ2+OhVW+y+J2bWM4Rpd59
      tE4xmycQDtFptcgmU8hWK5qmkdiN4Qv6adTqTF2dwxc8u3y8RZYpZrJYZJluq83E/Cwb95cY
      m53mu3/xY1weD26fl1K+wKvf/y6y1XrmLdDB1g7JWJyhsRE6rTbtRgurzYo/FMThcuHyuMmn
      M4zPThM4h7R5s1Zn7d4DQoMD1EoVLLIFj9eLJEkkdmPYHHZcHjeGrjP36vWT1e4s2F1OtlfW
      GBobpdVo0Ot0aLfaRIaHaNTqhCIDKL0eQ2OjhIcGn7gF+pN2g3Y7HeyOizPObqeDzW5/6v71
      WSb4tGM4rxtU6fawWOVzPYCnwTRNlG4Pm+Pp6hXnRVUURFHsy9SckT9pA3hRuIwDvLhcukEv
      eam5NIBLXmpe+FygPwUutycvLpcrwCUvNZcGcMlLzde2QBofvfs+tmCUN9+48ZXf1LJ7JOou
      XpmP/hEv79k52j8gdRBncGyUdDyB29uXNG9Ua9z4wXdx+84ny/4FpmmyevcB9XKF4clxsokk
      s9cXSR0c4vS4uHLj6eK4p+H3v36bV7//BrHNHQxDJzIcJX14xNwri4SHni5h/jQyh0ek4oeE
      BiNkE0km5mdJxQ+xORy8+v03nsMI4O6Hv2difpbMUQqbzYbT4+ZoP8bwxDjTC1eeuf1SLs/2
      yirjs9MkY3FGpibJJVOIksTNH/3gNKkQJpVyFb/fz/1bH/Lu27/m9p3b/PxXb3OUTrN05xbL
      G2v8z//+D8SOss98wX8MRqYm8Ph9GLpOr9ul02rRabVQul107dnrXgmCQCgSptvpUC2WGJ2Z
      YvPhMon9GI1qvV8r4Bk52j/AZrdhGAZzrywiCALBwQidVuu5aG+qikIulUYQRUKDETrtNg6X
      k4n52eem7ZlL9pW0W40msY0tGrV+lXp/MMjI1MQzt2/oOondGFarjWAkQrfdwWqzMrN49VvH
      8FUDMEx++Od/ydpnn5AsN5ibGiUR3yc0MoffZaPZqCNJDhauTbO1vffMF/3HYPPBMqHBCDa7
      ndGpCVRVRen2GJ+doVZ59iN4hmHgcLmwOx2MzU5TyuUJDUaIDEcRBAFdf/bKh816nWI2z9H+
      Aev3H/LK999AUxSuvPYquaPz5dF8mVajSbfd5mBzG6XXY+baAnvrWyRjca6+9nxWsEa1SqVY
      Ip9MExmOYpFlep0OmqZhO4XE+9Podjp0O232N7do1utcvXmD+PYuu2sbXHvj5jf+31cDYabJ
      YWwH0+ZndMDNzkGaK/MzJPZ38IYiWGQ7vU6DarFAcGSKsO/slbnPyrMGwlIHh5TyBSbmZihl
      8wQiYQRBoJTNMf2Ut8NpME2Tw919JEkiEA6RTaaYWbxKMZvDMAwGR56ur38auu02pgn765vY
      HHaCkQEK6SzzN86XRvB1TNOkVW/QbXco5fOMTE1wsLmDNxhgYm7mOYwAlJ6CYei06g2UnkJw
      IIQgis+1Sn2jVkNTteNt3Az7G9u4PG6mrs6/fKkQl1zyNC69QJe81FwawCUvNS98JNgw4YKL
      oF94pPaF3mO+5FyuAJe81FwawCUvNV8xAFNTODyMEz9MUKn1RVLPSrfdRNUMqqU88XicUrVx
      8rtGo/Et//lsmKZJs1bHNE0KmSyt474MwyB7lOxXQ0+lySVTaKpKPpU+c9miTquFrmnkkmmO
      9mIovR6maZJPpU+OP9YrFQzDoFoqUy+fPc7wxRjK+QLtZvNkDOl4Ak1VKWZzZA6PqBSKHO3F
      aNbqZ2q/2+6gKiq9bpdsInkyB6ZpUiuVT9pt1RvUj333Z6HX7ZI5POrXZ240MY7jINrxvCm9
      Hkd7MXLJVP9vE0dnvg9fXKeu6+SSqZP/r1cqJ3PerNXRVJVyoUir/s3P3VePRJomew9vkTV8
      VFIxrFaZbCaLqnTYT2RwySaPNvaIDA3SrZdYWlnH7Xawv79Po6tjdqr8+le/IDC2gNsKv337
      HRavzlCpdKjVyvz+w3fpqhAKenm08ginN8Du+jLYPLgdT/YFJ055JLKQzvDg97cID0WIbWyR
      OogzNjPN9tKj44c2RXxrl4Hh6HHAR6HTbuMLBk71DaCpKp/85l0CAyEsVpmDzR0CAyFkq5Va
      qczGg2UiI1He/t8/Y3BshI0Hy3iDAdze09dALucLfPbuhwxPjLO3tsHRboyx2Wm2llYAiG/v
      kjropys4XK4TIwlGBk7VvqHrfP7+h9jsdnZW+meB280W/lCQ2OY26/ceMj43c/IC2V/bxBPw
      4fH7Tj2G/FGKeqVKrVRm6dPPiU6MY7XbWL1zH1Xp0aw3cLpd7D5ax2azUc4XMXT9TH2k44fE
      NreoHP9vs1bHE/Czcus22WSageEhPvznX2K12ckfJUnsxRibmXp6KoQgWQj5/QRDIRqVMrur
      98nUFd757VvEtlZZWnpEoVxBMaBazNPI7fH2+x+TapisPLjLx5/dY25yBAC314/X68Mhd1h6
      tM/9u/cQRCshocQ//PTnHGZyLC0/YG09RqP57EXwIiPDBCJhJNlCo1o7GayqKMxeX0Tp9Rif
      n2XzwRIAUwvzdJqnF921yDKz164C4AsGsDnseAMBJEmi1+liYvLpW+8hWiR2V9cRBIG9tfUz
      vd2CkQEiI1Fkm5Vep0OtXO4f7He5SMcT2J1OJq/MsbOyitVuo9VoMHNt4dTti5LE3CvXAJCt
      VvKpDA6Xk06zxertu1SKRWwOO912h1BkAF3X2FvdONMYHB43zVqNyavzTH0pv0cAphauoPZ6
      KD2FuVev4fS46XU6DIycLb/MNE0GR0eQLBLTiwu0Gg0EQUAQROqVCp+99R4mJpVCgV63S6VY
      +sbdzGNeINnhwm6VcHs82O0WxuZmMCtxZN8Qw2EvGytLVJoqpXwBpy+IpyMR8HkRW14seotU
      scnIsU6l1+tBsrjp1NMgy3jtIhtHdX54c5F4usz0+BiyYJBIJJkdf7Yku3T8kOT+Af5QEKvN
      hmSxkDk8IjgY4dZb7zE2O00hk8Vqt+Pyern/u0+49sbrp26/1+mwt7GFLxhgpNNl8socrXqD
      dqtFtVjCIln48X/8CbFjNYKlz27j8Z3+rQaQT6VJ7h8wODqCw+3G7fOST6YxDAOr3YZJf2tk
      kWWUbg9/OHSmSLamquw+WkeUJFzufvpGq9FEslj4d//5b9l8uIzS7REajBAYCCNJljOf5V29
      fQ+X10M5l+dgaxul2yUUHcTt83Lvo49Z/M5NskdJFl5/jY9//TZun5deu4PllCuloetsPlhm
      4sosLo+HO+9/yOwr1yhmcngCPkRJ4vUf/5BcMoVFlskmkozPur4xWv7CR4I/ycTonDMSXC2W
      8IdDT/2787pBa+UKHp8P8SnCtM8ywacdw3lp1hvY7DZkq/VC2jcNk3qlgi8UvJD2oZ8mAsK5
      RMj+pA3gtFzGAV5eLt2gl7zUvPCRYEU36GnPnlP/rVzwK1o3zQvtwjQfK0T53NEvuAPDNNEu
      uA/pCd9LL7wBtFWDlnpxBmBe8MMJ0NOMC62PoRkm2rMfO/hWOhd4D6D/omspFzsIu0V8TOz5
      cgt0yUvNpQFc8lLzlUiwobT55MP32UkWGR0fw/Ik94haZ2njiOjg424trVXkrQ9vkc6V6NWK
      VOtldKsHl+38O62dagn1G87VmqbJ/qO+MnCzWmVnebkf5VxaplWvE4gMUC0U2H7wEMkis7fy
      iGa1SrfTJra2jifgR37KaaRmtcbu8gr+cJj1O3dpVqtU8gViq2u4vF5sDgc7D5f6QrbtNjtL
      S3iDITZu3yEQiWCR5afun492dugci7vuPlzC5rBzsLZGJZcjGI2iKSqbt29jsVpJ7+9TzmQx
      DJ2dhw/xhcNIsvVbvwHKmQyxlRWsDge6ppHY3MDQdfaXlwiP9Ov67j64T61YRFc1YivL2JxO
      DlYfUS+WCA4Nfev+XFNV9u7fo1Epo/Z6JNZW8Q8OsnPnNgAun49SKsnh2iPsLhfxRyuo3S71
      YpHs3h6ecBgkCfUpab+pjTVESaIYP8AwDJROm8TKQzzhCKLFQvEwTrtaRWm3yGxt0GnUKSeP
      qGXT+AajyJL4WNzkK0+maHUyFnRTC82xs/Q5uVKDaMSPFJ6nEF+iVigSnZ2hU9X5zS9/QReZ
      78wPs5/KEYxeZTGiky21cNUb+OQh2qU6h9kK48ODHMYTePwhGsU0IzOLLMw++0FoAKfXQ6te
      Z2R2Bl3TyCeTaIpKrdiX9faGQozPz1NMZ6iXSngCfiq5PACS9HTDFCUJXdex2u0EIxHKuRyv
      /OhNNttt3H4fmCYun49Wvc7Rzg5OjwddU5HtdjRFxXYKYVuX13t8TSY2h516qUR4eJh0LNZP
      T1le6tdC7vRrCguSSGJrm+s/epPte/dZ+NGPvrV9fySCoes0SiVa1SqmaVDOZJj7zhskd7YZ
      GBvHYrXSrtdROl2sDieNSpngUJTC0dFTr98iy/gHBymlU2i9HuPXrrP60Qdc+cGbZPZ2GRgf
      JzAURdc0Mru7BIai1AsF1F4PyWJBlCSe9oXRrlVplorYXG5sbg/dZoNOvcbkd75Hbm+HkcXr
      uPx+CvEDJl57Ha3Xw+7x4vT5id27/Y3y6N+wBTKJHeVYmAqyublHtdkml8/TViVevz5NsVCm
      Z0gIRo/tnQOuzo9RKvXTCgLhQayGSqFcQtUUVla3MGppYtkmVqsVr9vJ0VHyqZN6GgRBOKle
      kjk4QFUUBkZHcAf8mIYJpkkpk6GczzPz6nW+95N/RSWXZ+r6NSYXFkhs7zy1D6en376uafjC
      IbqtNp1mE6vdjmSxIIgiDlf/bLTVbmdycZHsYeJMqtSO4zF0221Cw8O0m02cXi+6qmEYBqJk
      Yer6NfKJIzRVRVdVJhYX2LxzF9n+9PO0hWSSTrOJPxKh3aiT2u1r8vc6HSSLBYfbjShJVHM5
      VKVHMDpEp97A7fejdDtPTYUwdB1XIIB6rLShdDvINhtKt4Nh9D9s84eH6KpKdG4etddD1zWm
      XruJf2iIUvLpz0MpcYjSaVPNpLC5+vNlGgZar9uvkCMIf/i5adIslXAFg8Qf3mf8tde/sRLQ
      Y4Gwej5Nzx5AaBXYTxa5dnWKtc0D/AE3smRlZjpKbD+HKRiAgR2FldVlPKPf5c3FMJ/cXcET
      GGDIZwdJxOpw0GwoFLMJQsOTdCoZBGeIxbnJpw4a4FcHO7TUJwfCTNNk9bPPMQ0Dt9/fXwlm
      ZsgfHeEJ+Ok0WzjcbsrZHEMT41QKBRwuFw63i1ziiPmbryHbbN/qBcocxEntxxidm6WUzuAb
      COHxB3B6PZRzOWSrleTuPgBT1xZJbG0zvnCF3YfLuP0+5m6+hqKb3+gFMk2TnQcP6TSbjF+9
      QjYeZ3R+nvT+Pg6XC03VmFi4yu7SMsMz0zQr/Te4OxAgc3DA/OuvY4rSt3qBUru71ItFojMz
      +CMRytl+7YFsLMbA+DiGrlErFnEHAljtdrKxA0bm5sjs7+H0+Ridv/KtXiBNUYg/WsHp9+P2
      +ykkEkzeuEF8ZYXw2BjVXA6LLNOsVhmenaOUTuENhdBUlXqxyNSN19AF8aleIKXdRtdUsjvb
      GLrO0JWrFGJ7hCen6Tb6K0KnUWf81ZtoSg+7x0P84X2sDidjr9zAIUuPeYGeORLcqpWIp0tc
      uTKHRXz+MdVvM4DnwaUb9HT8qbpBnzkO4PKFuOa7uFyVSy65SC7doJe81LzwkWCvTUKWLnb5
      vehNkGlebElZ07z4hDvjgtMUTC4+3UIUhMcyH194A7CIArJ00fmalwr+LyuXW6BLXmouDeCS
      l5qvpEKYapNfvPV73L0MOw07dCpYHU5KuTS6CR+89y5Dw6MUcjlsTjeWp5yEeh6ctU6wqigs
      37rN7uoGE3MzVIslPvjnXzL7yiKfv/cRvW6XdrPJ2t2HaJpG4BynrQ5391n57A6TV+bodbs8
      +vwudz/6GF8gwO0Pfsfo9BTv/p+fY5Hlc7VvGgaf/OYdEAT8oSB7axtsLT+iUa2ztfyIw509
      mvUGsY0t3N5+tfczj2Fnj/X7/fPRTreb3//qLQ62dmjU6sS3d5GtMrff/x3JWJyJ+dkzt99p
      tVi+defkQHp8Z48HH9+ilMuzv75F+vCIyEiUf/pvf8/Vm68iSWf/TkodHLJ+f4let0tgIMzv
      fvlbGrU64aFBfvOPP8Xj8/Hgk89IxuKMzU6f4lC87EY2OqwlqjirW/zm/Q/47bvv8tZbH1Gp
      NymVy7QyW3xw+xG15vkqlF80stWKy+M5OQMaGAgTnRxHEAQWXr+BAAyOjiDbrITOWVhiYm7m
      pLCGzW5n5toCi9+5SXRijOBAGAEIRSIU0plztS+IIlePrxX6sQrZaiV1cEg4OsTQ2Ej/+J8g
      UMoXztXH0PgoFouF8NAgoihgHOdbBcJBDMOgXqmia/q5P4/sTie+UJDB0b469sTcDMGBAaYX
      +orc5XyB5c/uMD47jaoo5+qjfzDewkB0CIDQYIRyvsDm0gqhoUEqxRLjs9P4QsGT8X2dx17h
      01E3qaaIx2EnHBnjxrUFJsZCPFzdwWkV6VkChMUq67Fn16W/CLrtDq1GE01VqRZLFNJZ9tc3
      ScbibC2tsLu2wa13PsDQjRPNmrNysLXD3vom5XyBZq3OweY2UwvzJGNx9tY3yafSSBYJRTlf
      AE/X9eNr3aRcKBKODmGaJpNX5iikM2QSSWS5X9z6vA/Pp2+9hyiJKN0u5XwRURQxDRNVURFE
      gWqpgs3Rz2c6D9mjJDuP1hAEgWqxRKNaw+V14wsFcHk8DI2PMjQ2Sq1codNqn6uPz9/7sJ8O
      oarUyxVM0+ynrAQD6JqGrusk9mJUi6XTH4o3zX7YXhRANwwEUcQ0jP4EfUlE6Yv8i4vmrPLo
      pmliGAaCIJBNJBkaH8U0TARROLl+QRAwDRPxCdmBp8HQDUzTpJTL93WCZBlB6L9FTcPsv1Gf
      YY6+GANA7ihFdGKsnxP0pXvwRX/n7eOLa61X+g/6F9uoL7drGgYIwrnqD5imiaH3x5BPpRka
      H/1K319c95fHc94xdFot9C9pC339WRXg9LlALxqX9QEuuUguvUCXvNT8P2uxNM4+VoV3AAAA
      AElFTkSuQmCC
    </thumbnail>
    <thumbnail height='192' name='Year wise Production cost' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAASyUlEQVR4nO3dS3NTZ57H8a+O5ItsS7blC/gCGEMgEGLoTlHpLtKrWWWqZjWVFzBTldl1
      9VvIaha9nRcwvZmq3iS9aJpK9XRXqmcoQjKEDhiIwTa+S5ZkSdb1SOf2nwXgxGC6aV0GD8//
      s3HJ1nnO8zzSr3Ss81xCIiIoZSjrdVdAqddJA6CMpgFQRtMAKKNpAJTRNADKaB0NgIjQ6res
      7SojCIKWygiC4I1pC3AoyjgM/fH/IgCH5cVqVTv6o111OSx9+rrroZdAymgaAGW0jgfA9Vv/
      iFOqUzoegP/4z/lOn0KppuklkDKaBkAZTQOgjKYBUEbTACijaQCU0TQAymgaAGU0DYAymgZA
      GS3SzEFLC/colF2OjsfI50uMHztFbuVbhk7/FG9nmUK5yszZiwxGw3vH/PGPfySbzTI5OUm9
      Xue9994jkUjw6aefMj4+zsTEBN999x29vb1EIhGy2SwfffRR2xqq1EGa+gQ4evQIfbEYdkO4
      eOk8lXyJkyem8QVciXDhndPs7hT2HfP2228zOjrKyMgIjUaDfD4PwMjICJlMhuPHj5NIJPjg
      gw84c+YMY2NjhEKh1luo1F/QRAA8bv/5HvGBfrosn4cPl+geiJJMbpNJp8FvsLS4Sv9QfO+I
      IAj4zW9+w/j4OJFIhFgsRrFYpFgs0tfXh4hQq9XwPA/Lsrh69SpjY2NtbKZSBwv97SvDCbVq
      DQlZ9EV7qNcdenu6sW0bCVlEe7ppuB7R3h6CIOBXn9/hnz68RKFQIBQKMTAwQLVaBcC2bQYH
      BxERotEoQRBgWRbFYpFQKLT3NxEhHA7/lXr9hRqL4Ps+kUhTV3wA+L6PZVktfSo9m7lkWc3/
      6/VsFlUr/QHgeV5L/dGOMp5NM32dr20TR4Xo6+/fexSN9gLs/91zDQqFQiQSib3HQ0ND+34+
      8+yNMTw8vPc7XblRdZJ+C6SMpgFQRtMAKKNpAJTRNADKaBoAZTQNgDKaBkAZTQOgjNbxAPzD
      T890+hRKNa3jARgZjHb6FEo1TS+BlNE0AMpoGgBlNA2AMpoGQBlNA6CM1tqcuFdw494GDddv
      +ngREASrxQnyz6ZbNn+8ELJCtFKLZ7PbWp3sHwSCZb3e/mhHGU+6Q17aH0MDUd47O9F0+a+i
      4wH492vfslOyO30a9QZ6Z2as4wHQSyBlNA2AMpoGQBlNA6CMpgFQRtMAKKNpAJTRNADKaBoA
      ZbSmArCbS7OdzSO+w/rGFoEI9WqJuhsgElAsldtdT2UwEWF1dXVvpfC7d++SzWZZW1tjfn4e
      gFQqtW8h5WfPf/ToEalUimw2y/z8/AuLLTcVADcIkUtusvBokYEoPF5Ls/rwDumyRyGb5tHj
      lRaaq9R+9+/f54svvgCgWq0yMjLCzZs3iUajbG9vs7q6ytWrV/H9J2POUqkUn332Gaurqyws
      LABQKpXI5XIUCvs3bmkqAE61RGx8gki4m+FEAnE9jk1PApAYn2Cgt6vpxir1Q41Ggy+++IJ0
      Ok25XKa3t5f79+9z+fJlXNcll8uRSCQ4derU3jFXr17FcRxu3brF1NQU33zzDa7r0tPTs2/p
      fWhqMJzLxuY2w+MWiXgvd+8+ZGpmltXleWo9Qo/dzU52h8JUjcH+3lbbrwzX09PDz3/+c27d
      ukUulyOVSpHP50mn01QqFaLRKJlMhuXlZUZGRuju7ubjjz/m9u3bnDlzht///vdMTEzw7bff
      Mjo6SqPRoLf3+/dlEzvEvLogCPj4l7/V0aCqKe/MjPGv//J3e493d3cZHBzcN3z6hzvE+L5P
      rVYjFou98jk6PhxaqXZ5fkeh54XD4b/pzQ/6NagynAZAGU0DoIymAVBG0wAoo2kAlNE0AMpo
      GgBltI7fCEsMRgm1soiTgAAtriWFSGtliPBkUayWVsZ6+rPFMg5Df7SljL/SlqGBzg+l6fhQ
      CKDFFdkCRIRwONx0GT+8Xd4s3/exLKulVd3a0R8iQhAELfUHgOd5LfVHO8o4DK+tXgIpo2kA
      lNE0AMpoGgBlNA2AMpoGQBlNA6CM1vEbYf/22deUak7zBTy7TdHiXRt5usNLa/UItXgTqw1t
      ebqryuvqj1/84/vE+3taOvdh0vEA3F1K65zgN4jjNb/d1WGkl0DKaBoAZTQNgDKaBkAZTQOg
      jKYBUEbTACijaQCU0TQAymhNBeDxowc8eLhMpZDlzt17VBsuqw/nyVc9sqkN7s3fo1z32l1X
      dUgsLy9z7do10uk0AK7rcu3aNWzb5ne/+x0iQrVa5Q9/+MPeMfPz8ywuLvKnP/2JX//616yu
      rnLt2jWSyeTragbQZABOzJ5GGjbrmQJz52fYWt8m3heh3AgYOTLF6FAv1Uq93XVVh8TU1BS9
      vb0UCgVEhJs3b+I4Dp7n0dXVRRAEfPnll7iuC0ClUmFjY4N8Ps/PfvYzRkdHSafTRKNRUqnU
      a21LEwHwuXv7NvGxI/SEfDa30nRFe7Ftm1KpxMriAsW6RWIw2v7aqkMhnU4TBAHFYhHbtjl7
      9ix9fX0AZLNZstksP/7xjwmHw9i2jW3b/OQnPwHgwYMHnDt3jlqtBrD383VpYjBciOkTJxHL
      Yur0WxR2ywwNxsjvBBwJW/QOHcVuuATPRk+qN87IyAie5xGLxchkMszMzHDlyhU8z+Py5cuE
      w2ESiQRXrlxheXmZc+fOATA3N0cQBPT39zM+Ps76+jrT09OvtS1NBMBi7MiRvUcjiSd7Lv3w
      d/GnP58tA6LeLAMDAwwMDOB5HuPj4wD09/cDMDg4uPe8/v5+Lly4sPc4Gv3+qqCrq4uTJ0++
      sGvj/zX9FkgZTQOgjKYBUEbTACijaQCU0TQAymgaAGU0DYAymgZAGa3j6wL94qP3cf0W7vaJ
      ICKEWthUAiDwfawWNmIIggArFGppQSp5eme8pbaIEIi0tMkGNN8f8b43Z1Es0B1iXpnuENP+
      Mg7Da6uXQMpoGgBlNA2AMpoGQBlNA6CMpgFQRuv8/gDLaVy/+ZlhIk++6rJa2dwC8P2AcLi1
      r2NDIau1vS0CQaCltjzpj6Dl+wCt9kc7yhARRNrQH0GA1WQ9Or9DzKdf6wYZ6tDSSyBlNA2A
      MpoGQBlNA6CMpgFQRtMAKKNpAJTRNADKaBoAZbSmAlAtFymUKkjgkd3JISK4DRvXF+q1Culs
      jte85qlSr6SpoRDFUolsOkemv5uBvh5WN33qme/oO/lThkIOTjnNut/FsfGBdtdXqbZq6hPA
      CjxiY0ewQl1MTU/h2Q2OH3uyzntPd5j0Ton+aFdbK6pUJzQRAJel5XVc2ybW18WdO98RH42z
      spYktbnOzk6O3mgU7+n2OEodZh1fFeLjX/5WR4OqQ0u/BVJG0wAoo2kAlNE0AMpoGgBlNA2A
      MpoGQBlNA6CMpgFQRuv4ukDdXWF6ulpby16pTtENMl6RbpDR/jIOw2url0DKaBoAZTQNgDKa
      BkAZTQOgjKYBUEbTACijdTwA1+c3On0KpZrW8QAsbeU7fQqlmqaXQMpoGgBlNA2AMpoGQBlN
      A6CMpgFQRtMAKKNpAJTRNADKaE0FYG35EQ+X17BLee7d/46667O18oii7QPC4sJD/OeOuXHj
      BtevXyeTyfD5559j2zYiwuPHj1lcXATg1q1b5PN5bty4wY0bN+jgbE2lgCYDMDF9HL9aZiWZ
      5e23JllfTRGhwa7tU0hvkd4pvhCAubk5isUid+7c4cqVK9y5cweA7u5utra22N3d5cGDB5RK
      JS5dukQ+n9cAqI5rIgDC6uMlJmdniYQgRAisMAP9fQBsbKWoVwrkCuXvjxDhyy+/5PLly/sm
      QIdCIcbHxwG4ffs2juOwtLTE9evXef/991uaPK7Uq2hiKr1H3XZIJ7eZmhjjweIWs7MzrC+m
      cLtDXPjRZXZ3dhgcjsHTVRCCIKBSqbCwsMD58+e5fv06ly5dYmFhgZ2dHfL5PB9++CFzc3P4
      vs/XX3/NwsICo6OjbW6uUvt1fFmUX31+h3/++x8d+LdQKPRXlxk5DEtngC6L0okyDsNr2/GF
      sV5GL2/UYaDvQmU0DYAymgZAGU0DoIymAVBG0wAoo2kAlNE0AMpoHQ/A6cnhTp9CqaZ1PAAf
      zB3v9CmUappeAimjaQCU0To+GO7ZiL9miUhbJsYEQYDvPz9N5287XkRaGg36rC1vQn+0o4x2
      tOVZGc3Wo+MBsCyr5eG/ItJyGUDLQ4jfpOHQ0Hp/tFrGYeiPjgaglTdLu8tpdfh1O4Zvt6s/
      2lGXw1DGYeiPjk6IUeqw6/glUKOS57ulDRJHpgiLgzg2I9OzRCMHp391aYGqCyemj7K4tMrg
      yBEsp8huzWN2ehSve5BCJsOp2RMHHp9ce0g5iDM5GGJ5Y5ujUzMUttcJIlHGB3tJpnc4dmqW
      esnDtneZPX2KfTURn4f37hCfPItf3GK37jNzYpqlR0sMDI/RFdTIlxqcnJlEQj3kczvMzs7s
      LyPwWVp8RNUJODoSYzuT49jJ02yuLNMTG2Yg4pEtVDh56jhOPUyxuMOp07PP1UNYWXpIseow
      MRInVapxYnqa5PoKoZ4YI/0WqewuJ986SXU3wLbzL7YFWH+8SH63wtTxKba2khyZOEExu4ln
      9XA00cdWKsv07CyN8kv6A9haf0x2Z5epY9OsbyaZPjZDaWcT2wtz/OgQq1tpJo7N4NsOTqPM
      9MwsEWt/KY1ynm8Xtzg9NcxmKsv0zGmSa8t09Q8R7w7I5MucPH2cetWiXN7h1AH1cO0St+aX
      OTedYKVQ5cTUNNubq9DVz2gsQipT4MTpk9SKPrZdOLAtjx7cxQ9FmZoY4vHaVue/Bcqmcpyd
      m6NRLpBcW2TXsV765gcQhMBtsL2RZvbCu/j1Im7QzYnRQXKFAjdv/g+Tx4699PijE5MEnkc6
      X2Ju7iKF1Aq9g6PEuoV00ebddy+Qz2ywcOcbuuIjL3QQWExNHsFxfHwg8Fx2k2scfesi4tZw
      fDg7FSeVK/HNV//D2OTUi2WELI4dP0bYstit1Hn33QtsPfwzieNnsIIGNcfn3OwkmVyO2ze/
      YvjoAWUA08eP0xUJ4wpExKNSyDMwNkWv5VGoOlw4d5pcbpv5W1/TlzhyYBmDg3HqjkuusMuF
      dy9SzG7QPTDEUC8k8zXmLl4kn17j0fxtwgOJg8uIx6k3HALxCVsW9YYD4W4m4mEWU2Uuzs1R
      ym2x+nAeJxx74c0vgc9qcod4b4R86ck5k4/+zNDUKcLiUG14nD99jGxuh2+/+orB8ckD6iGs
      rieJ9ffQEOgSn2qxQN/wEfoiPjvlBhfeOUtuJ8n927foHRo7sC0COE6dzE6B8xcuEv7kk08+
      OeB5bePaRYqNgFq1Tiwex7OrJMbGsV5y/ScSor87oFR2cAjRsOv4XgPP8+iN9jA4PEKpXGd4
      aODABrqNKplcjWjEwxWh3ghoOA08xyVsCeDjEiHWn6BaKzE+OsoPqyJArZyn7IQZjvcx0GOx
      W6tjO4Jbtwl8F9/16Ir2MTI0RLFSIzEU319G4HH/wSNOnXmL6m6OIPDxiGA3PLyGDeIT+D5W
      Tw9DsWGKlTKjieEX6vHg/n1OnH6LrnCY4aE425kMrid4ToMwARL4SKSLgWiCql1mbCTB891a
      d1zCTomKFyZiCfW6T8Op47ouEQtC+DT8MLGBBLVqkfGx0RfKsOsNolKjHIozfXSUdHqbwHdw
      HZ/uridPth0hFk9gV4uMjo/te30Dt852OsPmZor+WB9WyMcJwk/7o06IgMD3CfX0MNifoFx9
      sT8IXJLJbZJbKcYnpxkfHWI7ncXzAzzHwbIExMcPRYj1JajWyoyNjrzQllDIArtA3eoB8UE6
      LAh8SW5uiOP5Ytu2+I4t1Yb30ufXyruyvrUtgYikk5tSa7hiV4qync2L59TF8QKpVCoSvOT4
      rfUVWVp+LPWGI5sbm+L6gRTzWckXK+K7DdnYTEognth2Q2qVinjPFRR4jqw8XpallXWxKyVZ
      30iKHwSSSW1Jte5Io1aWZHpHXLchjus9qcvzZfiurK0sycrahrjOs3OK5DIpKVXr4tarspXK
      iO+7Um+4Uq1UxH++jCCQ9ZUlebyyKvV6TVZX18X1AynspGW3XBPPqcvm1vbTtjhSO6AMEZFc
      OimbqYxI4O/1R6mwI7ndivieIxsbWxIEL+8PEZHCTlrWt7bF9xxZX10V2/GkUsxLNl+UIPBk
      Y2NTPN8X265LvVYVx/MPfm2r1e/PKSL57LaUqra4jZpsJtNP+qN+cH/8sAzPsff6YzeXkUKp
      Kp5bl42t1L7+OKgtxXxWkpmcSODL1uam6D/Bymh6J1gZTQOgjPba1gVS6nXIZ7YpVm2GRsYZ
      jvfrJ4AyS1dPiP/+r5tEwvDg7m0NgDJLbHCIeGyQ8vodSpEjGgBlmhDx+ADDR2fIrd3XsUDK
      bPoJoIz2vztRVXGwa7xHAAAAAElFTkSuQmCC
    </thumbnail>
  </thumbnails>
</workbook>
