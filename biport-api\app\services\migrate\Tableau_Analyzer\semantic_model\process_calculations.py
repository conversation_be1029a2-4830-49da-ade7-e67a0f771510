from typing import <PERSON><PERSON>, List, Dict
from .datasources_handler import parse_xml
from app.core.enums import Datasource, GeneralKeys
from app.core.gpt_model import gpt_model

import xml.etree.ElementTree as ET

async def process_parameters(datasource):
    parameters = []

    for column in datasource.findall(Datasource.COLUMN.value):
        param_data = column.attrib.copy()

        calc_tag = column.find("calculation")
        if calc_tag is not None:
            formula = calc_tag.attrib.get("formula", "").strip()
            param_data["formula"] = formula

        parameters.append(param_data)

    return parameters

async def process_calculations(column: ET.Element) -> Tuple[dict, bool]:
    """
    Process a single <column> tag.
    Returns (column_data, has_calculation)
    """
    col_data = column.attrib.copy()
    calculation_tag = column.find(Datasource.CALC.value)

    if calculation_tag is not None:
        formula = calculation_tag.attrib.get(Datasource.FORMULA.value)
        if formula:
            col_data["formula"] = formula
        return col_data, True
    else:
        return col_data, False

async def process_datasource(datasource: ET.Element) -> <PERSON><PERSON>[List[dict], Dict[str, List[dict]]]:
    """
    Processes a single datasource (except 'Parameters').
    Returns (columns, calculations)
    """
    calculations = {
        "measure": [],
        "dimension": []
    }

    for column in datasource.findall(Datasource.COLUMN.value):
        col_data, has_calculation = await process_calculations(column)
        if has_calculation:
            role = col_data.get(GeneralKeys.ROLE.value, "").lower()
            if role == GeneralKeys.MEASURE.value:
                calculations["measure"].append(col_data)
            else:
                calculations["dimension"].append(col_data)

    return calculations

async def simplify_table_column_json(table_column_data):
    result = []
    for datasource, tables in table_column_data.items():
        ds_obj = {}
        for table_name, table_data in tables.items():
            column_names = [col["column_name"] for col in table_data.get("column_data", [])]
            ds_obj.setdefault(datasource, {})[table_name] = column_names
        result.append(ds_obj)
    return result

async def extract_formulas(data):
    result = {
        "measures": [],
        "dimensions": []
    }

    for item in data:
        for _, content in item.items():
            # Extract measures
            for measure in content.get("measure", []):
                result["measures"].append({
                    "name": measure.get("name", ""),
                    "formula": measure.get("formula", "")
                })

            # Extract dimensions
            for dimension in content.get("dimension", []):
                result["dimensions"].append({
                    "name": dimension.get("name", ""),
                    "formula": dimension.get("formula", "")
                })

    return result

async def get_calculations(twb_file_path: str) -> dict:
    datasources_result = []
    parameters = []
    has_calc = False

    root = await parse_xml(twb_file_path)
    datasources = root.find(Datasource.DATASOURCES.value).findall(Datasource.DS.value)
    
    for datasource in datasources:
        datasource_name = datasource.attrib.get(Datasource.CAPTION.value, "")

        if datasource.attrib.get(Datasource.NAME.value, "") == GeneralKeys.PARAMETERS.value:
            parameters = await process_parameters(datasource)
            continue

        calculations = await process_datasource(datasource)

        if calculations["measure"] or calculations["dimension"]:
            has_calc = True

        result = {
            datasource_name: calculations
        }
        datasources_result.append(result)

    return {
        "datasources": datasources_result,
        "parameters": parameters,
        "has_calc": has_calc
    }

def get_openai_pbi_response(tab_formula, table_info):
    model="gpt-4o"
    response_format = "json_object"
    user_message = f"Tableau Formula:{tab_formula}, Table Information:{table_info}"

    system_message = '''You are an expert in both Tableau and Power BI. Your task is to convert a list of Tableau calculated fields into their corresponding Power BI DAX functions. For each Tableau formula, you must accurately translate it into the equivalent DAX expression in Power BI, ensuring that the table and column names are maintained exactly as provided.

            Input Format:
            Tableau Formula: A list of Tableau calculated fields (formulae) that need to be converted.

            Table Information: A dictionary containing the table names and their associated columns.

            Output Instructions:
            DAX Conversion: Provide the exact Power BI DAX equivalent for each Tableau formula.

            Preserve Structure: The structure of the Tableau formula must be maintained without alteration in the DAX conversion.

            No Additional Aggregations: Do not introduce any new aggregations unless explicitly specified in the Tableau formula.

            Exact Field Mapping: Ensure the translated DAX formula references the correct table and field names from the provided table information.

            Formatting: Output the results in the following JSON format, ensuring the Power BI DAX formula is in a single line:

            {
            "json_output": [
                {
                "tableau_formula": "<Tableau formula>",
                "pbi_dax": "<Power BI DAX formula>"
                }
            ]
            }

            Make sure all formulas are carefully translated to maintain functional equivalence between Tableau and Power BI without skipping any.'''

    
    return gpt_model(model, system_message, user_message, response_format)
