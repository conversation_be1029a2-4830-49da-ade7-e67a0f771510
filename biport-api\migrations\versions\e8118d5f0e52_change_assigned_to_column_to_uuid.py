"""Change assigned_to column to UUID

Revision ID: e8118d5f0e52
Revises: 1dbfbac963a7
Create Date: 2025-07-16 12:42:50.759233

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e8118d5f0e52'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        'project_details',
        'assigned_to',
        type_=sa.dialects.postgresql.UUID(),
        postgresql_using='assigned_to::uuid',
        schema='biport_dev'
    )


def downgrade():
    op.alter_column(
        'project_details',
        'assigned_to',
        type_=sa.String(),
        postgresql_using='assigned_to::text',
        schema='biport_dev'
    )
