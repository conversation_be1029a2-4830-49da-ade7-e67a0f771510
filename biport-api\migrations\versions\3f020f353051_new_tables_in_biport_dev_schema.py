"""New tables in biport_dev schema

Revision ID: 3f020f353051
Revises: 141da23008f3
Create Date: 2025-07-01 09:34:55.807914

"""
from typing import Sequence, Union
from sqlalchemy.dialects import postgresql


from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3f020f353051'
down_revision: Union[str, None] = '141da23008f3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.execute("CREATE SCHEMA IF NOT EXISTS biport_dev")

    server_status_enum = postgresql.ENUM('ACTIVE', 'INACTIVE', name='serverstatus')
    server_type_enum = postgresql.ENUM('ONPREMISE', 'CLOUD', name='servertype')
    server_auth_type_enum = postgresql.ENUM('CREDENTIALS', 'PAT', name='serverauthtype')

    # Create enums explicitly (checkfirst will avoid duplicates)
    server_status_enum.create(op.get_bind(), checkfirst=True)
    server_type_enum.create(op.get_bind(), checkfirst=True)
    server_auth_type_enum.create(op.get_bind(), checkfirst=True)

    op.create_table('organization_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('credits', sa.Integer(), nullable=True),
    sa.Column('contact_person_name', sa.String(), nullable=False),
    sa.Column('mobile_number', sa.String(), nullable=False),
    sa.Column('address', sa.String(), nullable=True),
    sa.Column('service_type', sa.String(), nullable=True),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('mobile_number'),
    schema='biport_dev'
    )
    op.create_table('roles',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.Enum('ADMIN', 'MANAGER', 'DEVELOPER', name='roleenum'), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    schema='biport_dev'
    )
    op.create_table('datasource_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('credentials', sa.JSON(), nullable=True),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['biport_dev.organization_details.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('storage_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('storage_type', sa.String(), nullable=False),
    sa.Column('access_token', sa.String(), nullable=False),
    sa.Column('secret_key', sa.String(), nullable=False),
    sa.Column('bucket_name', sa.String(), nullable=False),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['biport_dev.organization_details.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('transaction_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('credits_purchased', sa.Integer(), nullable=False),
    sa.Column('amount', sa.String(), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['biport_dev.organization_details.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('users',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('password_hash', sa.String(), nullable=False),
    sa.Column('phone_number', sa.String(), nullable=False),
    sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('role_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('manager_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),

    sa.ForeignKeyConstraint(['manager_id'], ['biport_dev.users.id']),
    sa.ForeignKeyConstraint(['organization_id'], ['biport_dev.organization_details.id']),
    sa.ForeignKeyConstraint(['role_id'], ['biport_dev.roles.id']),

    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('phone_number'),

    schema='biport_dev'
)
    op.create_table('tableau_server_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('server_url', sa.String(), nullable=False),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='serverstatus', create_type=False), nullable=False),
    sa.Column('type', postgresql.ENUM('ONPREMISE', 'CLOUD', name='servertype', create_type=False), nullable=False),
    sa.Column('report_count', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('project_count', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('site_count', sa.Integer(), server_default=sa.text('0'), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['biport_dev.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('tableau_server_credentials',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('server_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('pat_name', sa.String(), nullable=True),
    sa.Column('pat_secret', sa.String(), nullable=True),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('password', sa.String(), nullable=True),
    sa.Column('server_auth_type', postgresql.ENUM('CREDENTIALS', 'PAT', name='serverauthtype', create_type=False), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['server_id'], ['biport_dev.tableau_server_details.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('tableau_site_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('credentials_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('site_name', sa.String(), nullable=False),
    sa.Column('site_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['credentials_id'], ['biport_dev.tableau_server_credentials.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('project_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('is_upload', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('site_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('server_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('parent_id', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['biport_dev.project_details.id'], ),
    sa.ForeignKeyConstraint(['server_id'], ['biport_dev.tableau_server_details.id'], ),
    sa.ForeignKeyConstraint(['site_id'], ['biport_dev.tableau_site_details.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['biport_dev.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('report_details',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('report_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('is_analyzed', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('is_converted', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('is_migrated', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('unit_tested', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('uat_tested', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('deployed', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('is_scoped', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('semantic_type', sa.String(), nullable=True),
    sa.Column('has_semantic_model', sa.Boolean(), server_default=sa.text('false'), nullable=True),
    sa.Column('view_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['biport_dev.project_details.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    op.create_table('report_analysis',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('dashboard_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('worksheet_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('datasource_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('calculation_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('complexity_type', sa.Enum('SIMPLE', 'MEDIUM', 'COMPLEX', 'HIGHLY_COMPLEX', name='complexitytypeenum'), nullable=True),
    sa.Column('report_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['report_id'], ['biport_dev.report_details.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='biport_dev'
    )
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_table('report_analysis', schema='biport_dev')
    op.drop_table('report_details', schema='biport_dev')
    op.drop_table('project_details', schema='biport_dev')
    op.drop_table('tableau_site_details', schema='biport_dev')
    op.drop_table('tableau_server_credentials', schema='biport_dev')
    op.drop_table('tableau_server_details', schema='biport_dev')
    op.drop_table('users', schema='biport_dev')
    op.drop_table('transaction_details', schema='biport_dev')
    op.drop_table('storage_details', schema='biport_dev')
    op.drop_table('datasource_details', schema='biport_dev')
    op.drop_table('roles', schema='biport_dev')
    op.drop_table('organization_details', schema='biport_dev')
    # ### end Alembic commands ###
