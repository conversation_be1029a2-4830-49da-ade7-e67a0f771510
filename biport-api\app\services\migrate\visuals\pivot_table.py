from enum import unique
import json,uuid
from app.core import pivot_table_template, pivot_table_colors
from app.core.enums import (
    TableauXMLTags, ChartType, PowerBITemplateKeys,
    PowerBIReportKeys, PowerBIChartTypes, VisualRequest
)
from ..core import *
from app.core import COLOR_PALETTE_DATA
import json 
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_multiple_encodings_data,
    generate_projections_data
)
from app.core.logger_setup import logger


def get_highlight_report(rows, cols, visual_type, pane_encodings, table_column_data, datasource_column_list, values, worksheet_name, worksheet_title_layout, style):
    try:
        highlight_chart_result=[]
        projection_dict = {}

        tooltips = pane_encodings.get('tooltip')
        tooltips_data = process_multiple_fields(tooltips)

        result_queryref, table_list, select_list = get_projections_data([rows, cols, values, tooltips_data], table_column_data, datasource_column_list)
        rows_qref_list = result_queryref.get(rows)
        cols_qref_list = result_queryref.get(cols)
        value_qref_list = result_queryref.get(values)
        tooltip_list = result_queryref.get(tooltips_data)

        if rows_qref_list : projection_dict["Rows"] = rows_qref_list
        if cols_qref_list: projection_dict["Columns"] = cols_qref_list
        if value_qref_list : projection_dict["Values"] = value_qref_list
        if tooltip_list : projection_dict["Tooltips"] = tooltip_list

        from_list = get_from_list(table_list)

        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)
        objects_values = '''{ "properties": { "bandedRowHeaders": { "expr": { "Literal": { "Value": "false" } } }, "fontSize": { "expr": { "Literal": { "Value": "13D" } } } } }'''
        if values:
            values_input, value_queryref, min_color, mid_color, max_color = get_color_formatting(style, values, table_column_data, datasource_column_list)
            if visual_type == ChartType.PIVOT_TABLE.value:
                color_type = "fontColor"
            else:
                color_type = "backColor"
            objects_values = pivot_table_colors.format(color_type = color_type,values_input = json.dumps(values_input), min_color = min_color, mid_color = mid_color, max_color = max_color, selector_metadata = value_queryref)

        highlight_json={
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "projections_dict" : json.dumps(projection_dict),
            "from_list":json.dumps(from_list),
            "select_list":json.dumps(select_list),
            "objects_values" : objects_values,
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }
        highlight_chart_result.append({"config":highlight_json,"template":pivot_table_template})
        return highlight_chart_result
    
    except Exception as e:
        logger.error(f"---Error in generating highlighted chart visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating highlighted chart visual for {worksheet_name} - {str(e)}")


def process_pivot_table_report(request: VisualRequest):
    pivot_table_result = {}
    rows = request.rows
    cols = request.cols
    panes = request.panes
    calculations_related_data = request.calculations_related_data
    table_column_data = request.table_column_data
    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )
    unique_tooltips = remove_duplicate_fields(
        encodings[TableauXMLTags.TOOLTIP.value],
        encodings[TableauXMLTags.LOD.value],
        encodings[TableauXMLTags.TEXT.value],
        rows,
        cols
    )
    unique_color = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])
    table_ex_fields_mapping = {
        PowerBIReportKeys.ROWS.value: rows,
        PowerBIReportKeys.COLUMNS.value: cols
    }

    if unique_color:
        table_ex_fields_mapping[PowerBIReportKeys.VALUES.value] = unique_color

    if unique_tooltips:
        table_ex_fields_mapping[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips
    projections_data = generate_projections_data(table_column_data, calculations_related_data, table_ex_fields_mapping)   
    pivot_table_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.PIVOT_TABLE.value
    pivot_table_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    pivot_table_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data
    
    return pivot_table_result