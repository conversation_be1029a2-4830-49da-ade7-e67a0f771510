from typing import Dict
from app.core.enums import ChartType, WorkSheet as WS, GeneralKeys as GS

class ComboChart:
    @staticmethod
    def check_combo_chart(worksheet) -> Dict[str, str]:
        """
        Analyzes the worksheet to determine if it represents a combo chart.

        Returns:
            A dictionary containing the status and chart type.
        """
        panes = worksheet.findall(WS.PANES_PANE.value)
        columns = worksheet.findall(WS.DS_COLS.value)

        if len(panes) != 3:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}
        
        date_types = GS.DATE_TIME_SET.value
        has_date_column = any(
            col.get(WS.DATATYPE.value) in date_types for col in columns
        )
        if not has_date_column:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        # Extract mark classes from panes
        mark_classes = [
            pane.find(WS.MARK.value).get(WS.CLASS.value) if pane is not None else None
            for pane in panes
        ]

        # Validate extracted mark classes
        if None in mark_classes:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        # Determine chart type based on mark classes
        if mark_classes[0] == ChartType.AUTOMATIC.value:
            if mark_classes[1] == ChartType.BAR.value:
                if mark_classes[2] == ChartType.AREA.value:
                    chart_type = ChartType.BAR_AND_AREA.value
                elif mark_classes[2] == ChartType.LINE.value:
                    chart_type = ChartType.BAR_AND_LINE.value
                else:
                    chart_type = None
            else:
                chart_type = None
        elif mark_classes[0] == ChartType.LINE.value:
            if mark_classes[1] == ChartType.BAR.value:
                if mark_classes[2] == ChartType.AREA.value:
                    chart_type = ChartType.BAR_AND_LINE.value
                elif mark_classes[2] == ChartType.LINE.value:
                    chart_type = ChartType.LINE_AND_AREA.value
                else:
                    chart_type = None
            elif mark_classes[1] == ChartType.AREA.value and mark_classes[2] == ChartType.LINE.value:
                chart_type = ChartType.LINE_AND_AREA.value
            else:
                chart_type = None
        else:
            chart_type = None

        return {GS.STATUS.value: bool(chart_type), GS.CHART_TYPE.value: chart_type}
