from .datasource_services import DSServices
from app.core import ServiceResponse
from app.schemas.datasources import RemoveUserDetails, UpdateExistingDataSource, GetUserDetails, AddDataSourceDetails


class DatasourceProcessor:
    """Request validation for Datasource Operations."""

    @staticmethod
    def add_data_source_details(request: AddDataSourceDetails) -> ServiceResponse:
        """Adding data sources for existing users."""
        return DSServices.execute(DSServices().add_data_source_details,request)

    @staticmethod
    def remove_user_details(request: RemoveUserDetails) -> ServiceResponse:
        """To remove data source details."""
        return DSServices.execute(DSServices().remove_user_details,request)

    @staticmethod
    def update_existing_data_source(request: UpdateExistingDataSource) -> ServiceResponse:
        """To update an existing data source."""
        return DSServices.execute(DSServices().update_the_existing_datasources,request)

    @staticmethod
    def get_user_details(request: GetUserDetails) -> ServiceResponse:
        """To get user details."""
        return DSServices.execute(DSServices().get_user_details,request)