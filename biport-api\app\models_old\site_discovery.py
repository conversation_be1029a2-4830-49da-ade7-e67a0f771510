from datetime import datetime
import uuid
from sqlalchemy import <PERSON>umn, <PERSON><PERSON>ey, Enum, DateTime, String
from sqlalchemy.dialects.postgresql import UUID

from app.core import Base, scoped_context, NotFoundError
from app.core.enums import ServerType, DiscoverSiteStatus


class SiteDiscovery(Base):
    __tablename__ = "site_discovery"

    id = Column(UUID(as_uuid=True), primary_key=True, nullable=False)
    server_id = Column(UUID, ForeignKey('server_details.id'), nullable=False)
    server_type_id = Column(UUID(as_uuid=True))
    status = Column(Enum(DiscoverSiteStatus))
    server_type = Column(String(), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
class SiteDiscoveryManager():
    
    def __init__(self) -> None:
        """Initializes the SiteDiscoveryManager."""
        pass

    def get(self, id: UUID) -> SiteDiscovery:
        """Fetches the site discovery of a server."""
        with scoped_context() as session:
            site = session.query(SiteDiscovery).filter(SiteDiscovery.id == id).first()
            if not site:
                raise NotFoundError("site discovery not found.")
            return site

    def add(self, server_id: uuid.UUID, server_type: str, status: DiscoverSiteStatus, server_type_id: uuid.UUID) -> SiteDiscovery:
        """Adds a new site discovery to the database."""
        id = uuid.uuid4()
        with scoped_context() as session:
            site = SiteDiscovery(
                id=id,
                server_id=server_id,
                server_type=server_type,
                status=status,
                server_type_id = server_type_id
            )
            session.add(site)
            session.commit()
            session.refresh(site)
            return site

    def update(self, id: UUID, status: DiscoverSiteStatus) -> None:
        """Updates the site discovery of a server."""
        with scoped_context() as session:
            site = session.query(SiteDiscovery).filter(SiteDiscovery.id == id).first()
            site.status = status
            session.commit()

    def get_site_discovery_by_server_type_ids(self, server_id: uuid.UUID, server_type_ids: list[uuid.UUID]) -> list[SiteDiscovery]:
        """Fetches the site discoveries of a server."""
        with scoped_context() as session:
            return session.query(SiteDiscovery.status).filter(
                SiteDiscovery.server_id == server_id,
                SiteDiscovery.server_type_id.in_(server_type_ids)
            ).all()
            
    def delete(self, server_id: UUID) -> None:
        """Deletes a site discovery."""
        with scoped_context() as session:
            sites = session.query(SiteDiscovery).filter(SiteDiscovery.server_id == server_id).all()
            for site in sites:
                session.delete(site)
            session.commit()