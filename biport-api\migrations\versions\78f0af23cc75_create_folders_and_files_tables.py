"""create folders and files tables

Revision ID: 78f0af23cc75
Revises: afdd419d770b
Create Date: 2025-04-16 16:42:20.619001

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '78f0af23cc75'
down_revision: Union[str, None] = 'afdd419d770b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('folders',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('parent_id', sa.String(length=36), nullable=True),
    sa.Column('is_file', sa.Integer(), nullable=True),
    sa.Column('is_deleted', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['folders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_folders_id'), 'folders', ['id'], unique=False)
    op.create_table('files',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('filename', sa.String(), nullable=False),
    sa.Column('filepath', sa.String(), nullable=False),
    sa.Column('folder_id', sa.String(length=36), nullable=False),
    sa.ForeignKeyConstraint(['folder_id'], ['folders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_files_id'), 'files', ['id'], unique=False)
    # op.alter_column('users', 'id',
    #            existing_type=sa.INTEGER(),
    #            server_default=None,
    #            existing_nullable=False,
    #            autoincrement=True)
    # op.alter_column('users', 'password',
    #            existing_type=sa.VARCHAR(),
    #            nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # op.alter_column('users', 'password',
    #            existing_type=sa.VARCHAR(),
    #            nullable=True)
    # op.alter_column('users', 'id',
    #            existing_type=sa.INTEGER(),
    #            server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1),
    #            existing_nullable=False,
    #            autoincrement=True)
    op.drop_index(op.f('ix_files_id'), table_name='files')
    op.drop_table('files')
    op.drop_index(op.f('ix_folders_id'), table_name='folders')
    op.drop_table('folders')
    # ### end Alembic commands ###
