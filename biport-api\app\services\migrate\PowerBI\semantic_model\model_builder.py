from .process_relationships import generate_all_relationships
from .process_tables import process_table_columns, process_column_data
from app.core.exceptions import NotFoundError
from app.core.config import S3Config
from app.core.enums import PowerBITemplateKeys, GeneralKeys
from ...Tableau_Analyzer.semantic_model.process_calculations import get_calculations, simplify_table_column_json, extract_formulas
from app.core.constants import     LOCAL_DATE_TEMPLATE, PARTITION_BLOCK_EXCEL, PARTITION_BLOCK_CSV, PARTITION_BLOCK_SQL, MODEL_TMDL_HEADER, MODEL_TMDL_REF_CULTURE, TABLE_NAME_CALCULATIONS, PARTITION_BLOCK_Calculations

import os
import shutil
import uuid
import json

s3_config = S3Config()

async def generate_lineage_tag():
    return str(uuid.uuid4())

async def get_tables_folder_path(pbi_env_file):
    pbi_dir_list = os.listdir(pbi_env_file)
    powerbi_semantic_folder = [path for path in pbi_dir_list if '.SemanticModel' in path]
    
    if not powerbi_semantic_folder:
        raise NotFoundError("No '.SemanticModel' folder found in the given directory.")
    
    definition_folder = os.path.join(pbi_env_file, powerbi_semantic_folder[0], 'definition')
    tables_folder_path = os.path.join(definition_folder, 'tables')

    # Remove the existing folder if it exists
    if os.path.exists(tables_folder_path):
        shutil.rmtree(tables_folder_path)

    # Create a fresh tables folder
    os.makedirs(tables_folder_path, exist_ok=True)

    return tables_folder_path,definition_folder


async def create_table_tmdl_file(table_column_data, date_columns_data, tables_folder_path):

    model_table_names = []
    for datasource_name, tables_data in table_column_data.items():
        for table_name, table_info in tables_data.items():
            model_table_names.append(table_name)
            file_path = os.path.join(tables_folder_path, f"{table_name}.tmdl")
            column_data = table_info.get(PowerBITemplateKeys.COLUMN_DATA.value, [])
            connection_type = table_info.get("connection_type")
            file_path_value = table_info.get("file_path")
            server = table_info.get("server")
            database = table_info.get("database")

            column_types_list = []

            with open(file_path, 'w') as tmdl_file:
                # Write table declaration
                tmdl_file.write(f"table {table_name}\n")
                tmdl_file.write(f"\tlineageTag: {table_info.get(PowerBITemplateKeys.LINEAGE_TAG.value)}\n\n")

                # Write columns
                for column in column_data:
                    col_name = column.get(PowerBITemplateKeys.COLUMN_NAME.value)
                    variation = column.get(PowerBITemplateKeys.VARIATION.value)

                    partition_col_name = col_name.strip().strip("'\"")
                    tmdl_file.write(f"\tcolumn {col_name}\n")
                    tmdl_file.write(f"\t\tdataType: {column.get(PowerBITemplateKeys.DATA_TYPE.value)}\n")
                    if {column.get(PowerBITemplateKeys.DATA_TYPE.value)} == "int64":
                        column_types_list.append(f'{{"{partition_col_name}", Int64.Type}}')
                    else:
                        column_types_list.append(f'{{"{partition_col_name}", type {column.get(PowerBITemplateKeys.DATA_TYPE.value)}}}')
                    tmdl_file.write(f"\t\tformatString: {column.get(PowerBITemplateKeys.FORMAT_STRING.value)}\n")
                    tmdl_file.write(f"\t\tlineageTag: {column.get(PowerBITemplateKeys.LINEAGE_TAG.value)}\n")
                    tmdl_file.write(f"\t\tsummarizeBy: {column.get(PowerBITemplateKeys.SUMMARIZE_BY.value)}\n")
                    tmdl_file.write(f"\t\tsourceColumn: {column.get(PowerBITemplateKeys.SOURCE_COLUMN.value)}\n\n")

                    if variation:
                        tmdl_file.write(f"\t\tvariation Variation\n")
                        tmdl_file.write(f"\t\t\tisDefault\n")
                        tmdl_file.write(f"\t\t\trelationship: {variation.get(PowerBITemplateKeys.RELATIONSHIP_ID.value)}\n")
                        tmdl_file.write(f"\t\t\tdefaultHierarchy: {variation.get(PowerBITemplateKeys.LOCAL_DATE_TABLE_REF.value)}.'Date Hierarchy'\n\n")
                        tmdl_file.write(f"\t\tannotation UnderlyingDateTimeDataType = Date\n\n")

                    tmdl_file.write(f"\t\tannotation SummarizationSetBy = Automatic\n\n")

                tmdl_file.write("\n")

                # Write partition block only if required values are present
                if connection_type :
                    column_types_block = ", ".join(column_types_list)
                    column_count = len(column_data)

                    partition_block = None
                    if connection_type == "excel-direct":
                        partition_block = PARTITION_BLOCK_EXCEL.format(
                            table_name=table_name,
                            file_path=file_path_value
                        )
                    elif connection_type == "textscan":
                        partition_block = PARTITION_BLOCK_CSV.format(
                            table_name=table_name,
                            file_path=file_path_value,
                            column_count=column_count
                        )
                    elif connection_type == "sqlserver":
                        if server and database:
                            partition_block = PARTITION_BLOCK_SQL.format(
                                table_name=table_name,
                                server=server,
                                database=database
                            )

                    if partition_block:
                        tmdl_file.write(partition_block)
                
    if date_columns_data:
        for date_column in date_columns_data:
            column_name = date_column.get("column_name")
            local_date_table_ref = date_column.get("local_date_table_ref")
            table_name = date_column.get("table_name")
            model_table_names.append(local_date_table_ref)
            date_table_path = os.path.join(tables_folder_path, f"{local_date_table_ref}.tmdl")
            with open(date_table_path, 'w') as date_file:
                date_file.write(LOCAL_DATE_TEMPLATE.format(
                    column_name = column_name,
                    local_date_table_ref = local_date_table_ref,
                    table_name = table_name,
                    table_lineage_tag = str(uuid.uuid4()),
                    date_lineage_tag = str(uuid.uuid4()),
                    year_lineage_tag = str(uuid.uuid4()),
                    quarter_lineage_tag = str(uuid.uuid4()),
                    month_lineage_tag = str(uuid.uuid4()),
                    day_lineage_tag = str(uuid.uuid4()),
                    month_no_lineage_tag = str(uuid.uuid4()),
                    quarter_no_lineage_tag = str(uuid.uuid4()),
                    hierarchy_lineage_tag = str(uuid.uuid4()),
                    hierarchy_year_lineage_tag = str(uuid.uuid4()),
                    hierarchy_quarter_lineage_tag = str(uuid.uuid4()),
                    hierarchy_month_lineage_tag = str(uuid.uuid4()),
                    hierarchy_day_lineage_tag = str(uuid.uuid4()),

                ))
    return model_table_names

async def create_calc_tmdl(datasources: list, tables_folder_path: str):
    calc_file_path = os.path.join(tables_folder_path, f"{TABLE_NAME_CALCULATIONS}.tmdl")
    
    with open(calc_file_path, 'w') as calc_file:
        calc_file.write(f"table {TABLE_NAME_CALCULATIONS}\n")
        calc_file.write(f"\tlineageTag: {generate_lineage_tag()}\n\n")

        for datasource in datasources:
            for ds_name, calc_groups in datasource.items():
                measures = calc_groups.get("measure", [])
                dimensions = calc_groups.get("dimension", [])

                for measure in measures:
                    await add_measure(calc_file, measure)

                for dimension in dimensions:
                    await add_column(calc_file, dimension, is_calculation=True)
        partition_block = PARTITION_BLOCK_Calculations.format(table_name = TABLE_NAME_CALCULATIONS)
        calc_file.write(partition_block)


async def create_model_tmdl_file(table_names: list[str], defn_folder: str):
    try:
        model_file_path = os.path.join(defn_folder, "model.tmdl")
        sorted_tables = sorted(table_names)

        # Format query order
        query_order_annotation = "[" + ",".join(f'"{table}"' for table in sorted_tables) + "]"

        # Build full content
        model_content = MODEL_TMDL_HEADER.format(query_order=query_order_annotation)
        for table in sorted_tables:
            model_content += f"\nref table {table}"
        model_content += f"\n\n{MODEL_TMDL_REF_CULTURE}\n"

        # Write to file
        with open(model_file_path, "w", encoding="utf-8") as f:
            f.write(model_content)

    except Exception as e:
        from app.core.config import logger
        logger.error(f"Error creating model.tmdl: {e}")


def get_pbi_format_hint(datatype: str) -> str:
    """
    Returns JSON string for PBI_FormatHint annotation line based on datatype.
    GeneralNumber = true if numeric, else false.
    """
    numeric_types = {"integer", "int64", "double", "decimal", "real"}
    is_general_number = datatype.lower() in numeric_types
    return f'{{"isGeneralNumber":{str(is_general_number).lower()}}}'

async def add_measure(tmdl_file, measure_data: dict):
    """
    Writes a single measure block to the TMDL file.
    Measures need only: caption, formula, lineageTag, and a static annotation.
    """
    lineage_tag = await generate_lineage_tag()
    measure_name = measure_data.get('caption')
    formula = measure_data.get('formula', '')
    datatype = measure_data.get('datatype')

    # Reuse your existing formula block writer
    await write_formula_block(tmdl_file, "measure", measure_name, formula)

    # Add metadata lines
    tmdl_file.write(f"\t\tlineageTag: {lineage_tag}\n")
    tmdl_file.write("\n")
    format_hint = get_pbi_format_hint(datatype)
    tmdl_file.write(f"\t\tannotation PBI_FormatHint = {format_hint}\n\n")
    tmdl_file.write("\n")

def quote_if_needed(name: str) -> str:
    if " " in name:
        return f"'{name}'"
    return name

async def write_formula_block(tmdl_file, kind, name, formula):
    """
    Writes the declaration line for a column or measure with the formula,
    using single-line or multi-line backtick format.
    """
    name = quote_if_needed(name)
    if "\n" in formula.strip():
        # Multi-line with backticks
        tmdl_file.write(f"\t{kind} {name} = ```\n\n")
        for line in formula.splitlines():
            tmdl_file.write(f"\t\t{line.rstrip()}\n")
        tmdl_file.write(f"\n\t\t```\n")
    else:
        # Single-line formula
        tmdl_file.write(f"\t{kind} {name} = {formula}\n")

async def add_column(tmdl_file, column_data: dict, is_calculation: bool = False):
    """
    Writes a single column (or calculation) block to the TMDL file.
    If is_calculation is True, writes formula instead of sourceColumn.
    """
    # print(json.dumps(column_data, indent=4))
    lineage_tag = await generate_lineage_tag()
    column = column_data.get('caption')
    datatype = column_data.get('datatype')
    aggregation = column_data.get('aggregation', 'none').lower()
    processed_data = await process_column_data(datatype, aggregation)
    format_string = processed_data.get('format_string')
    formula = column_data.get('formula', '')

    await write_formula_block(tmdl_file, "column", column, formula)
    tmdl_file.write(f"\t\tdataType: {datatype}\n")
    tmdl_file.write(f"\t\tformatString: {format_string}\n")
    tmdl_file.write(f"\t\tlineageTag: {lineage_tag}\n")
    tmdl_file.write(f"\t\tsummarizeBy: {aggregation}\n")
    
    tmdl_file.write("\t\tannotation SummarizationSetBy = Automatic\n\n")

async def upload_pbi_to_s3(pbi_folder_path, unique_id):
    s3_key = f"semantic_model_test/{unique_id}"
    zip_file_path = shutil.make_archive(pbi_folder_path, 'zip', pbi_folder_path)
    zip_s3_key = f"{s3_key}/{os.path.basename(zip_file_path)}"
    await s3_config.upload_to_s3(zip_file_path, zip_s3_key)
    return {
        'file_name': os.path.basename(zip_file_path),
        'download_link': await s3_config.generate_presigned_url(zip_s3_key)
    }

async def create_relationships_tmdl_file(relationships_data, definition_folder):
    """
    Creates or replaces the relationships.tmdl file under the given definition folder.
    """
    relationships_file_path = os.path.join(definition_folder, "relationships.tmdl")

    # Remove existing relationships.tmdl file if it exists
    if os.path.exists(relationships_file_path):
        os.remove(relationships_file_path)

    # Write new relationships file
    with open(relationships_file_path, "w") as rel_file:
        for rel in relationships_data:
            rel_file.write(f"relationship {rel['id']}\n")
            if "joinOnDateBehavior" in rel:
                rel_file.write(f"\tjoinOnDateBehavior: {rel['joinOnDateBehavior']}\n")
            if "fromCardinality" in rel:
                rel_file.write(f"\tfromCardinality: {rel['fromCardinality']}\n")
            if "toCardinality" in rel:
                rel_file.write(f"\ttoCardinality: {rel['toCardinality']}\n")
            rel_file.write(f"\tfromColumn: {rel['fromColumn']}\n")
            rel_file.write(f"\ttoColumn: {rel['toColumn']}\n\n")


async def create_semantic_model(twb_file_path, pbi_env_file):
    table_column_data, date_columns_data = await process_table_columns(twb_file_path)
    tables_folder_path, defition_folder = await get_tables_folder_path(pbi_env_file)
    relationships = await generate_all_relationships(twb_file_path, date_columns_data)
    model_table_names = await create_table_tmdl_file(table_column_data, date_columns_data, tables_folder_path)
    await create_relationships_tmdl_file(relationships, defition_folder)
    result = await simplify_table_column_json(table_column_data)
    calc_data = await get_calculations(twb_file_path)

    formulas = await extract_formulas(calc_data.get("datasources"))

    if calc_data.get("has_calc"):
        await create_calc_tmdl(calc_data.get("datasources", []), tables_folder_path)
        model_table_names.append("Calculations")
    
    await create_model_tmdl_file(model_table_names, defition_folder)
    
    
