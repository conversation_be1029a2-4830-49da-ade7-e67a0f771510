from typing import List, Optional
from uuid import UUID
from datetime import datetime

from pydantic import BaseModel

class FolderCreate(BaseModel):
    name: str
    parent_id: Optional[UUID] = None
    is_file: int = 0

class ProjectCreate(BaseModel):
    name: str
    parent_id: Optional[UUID] = None


class FolderUpdate(BaseModel):
    name: Optional[str] = None
    parent_id: Optional[UUID] = None

class FolderResponse(BaseModel):
    id: UUID
    name: str
    parent_id: Optional[UUID] = None
    is_file: int
    is_deleted: int
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    filepath: Optional[str] = None
    file_url: Optional[str] = None
    folder_path: Optional[str] = None

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

    def dict(self, **kwargs):
        data = super().dict(**kwargs)

        if "id" in data and isinstance(data["id"], UUID):
            data["id"] = str(data["id"])
        if "parent_id" in data and isinstance(data["parent_id"], UUID):
            data["parent_id"] = str(data["parent_id"])

        if "created_at" in data and isinstance(data["created_at"], datetime):
            data["created_at"] = data["created_at"].isoformat()
        if "updated_at" in data and isinstance(data["updated_at"], datetime):
            data["updated_at"] = data["updated_at"].isoformat()

        return data

class FolderTree(FolderResponse):
    folder_path: Optional[str] = None
    usage: int = 0
    children: Optional[List["FolderTree"]] = []

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

    # def dict(self, **kwargs):
    #     data = super().dict(**kwargs)

    #     if "id" in data and isinstance(data["id"], UUID):
    #         data["id"] = str(data["id"])
    #     if "parent_id" in data and isinstance(data["parent_id"], UUID):
    #         data["parent_id"] = str(data["parent_id"])

    #     if "created_at" in data and isinstance(data["created_at"], datetime):
    #         data["created_at"] = data["created_at"].isoformat()
    #     if "updated_at" in data and isinstance(data["updated_at"], datetime):
    #         data["updated_at"] = data["updated_at"].isoformat()

    #     # if "children" in data:
    #     #     data["children"] = [child.dict(**kwargs) for child in data["children"]]

    #     if "children" in data:
    #         data["children"] = [
    #             child.dict(**kwargs) if isinstance(child, BaseModel) else child
    #             for child in data["children"]
    #         ]

    #     return data

    def dict(self, **kwargs):
        def convert(value):
            if isinstance(value, UUID):
                return str(value)
            elif isinstance(value, datetime):
                return value.isoformat()
            elif isinstance(value, BaseModel):
                return value.dict(**kwargs)
            elif isinstance(value, list):
                return [convert(item) for item in value]
            elif isinstance(value, dict):
                return {k: convert(v) for k, v in value.items()}
            return value

        data = super().dict(**kwargs)
        return convert(data)

FolderTree.update_forward_refs()

class ProjectUpdate(BaseModel):
    project_id: UUID
    new_name: str

class FileUploadRequest(BaseModel):
    project_id: Optional[UUID] = None  # For direct file upload
    parent_id: Optional[UUID] = None   # For zip upload (to set parent of new project)
    # The file itself will be handled as a form-data UploadFile in the API, not in the schema

class DeleteReportRequest(BaseModel):
    report_id: UUID

class EditReportNameRequest(BaseModel):
    report_id: UUID
    new_name: str
