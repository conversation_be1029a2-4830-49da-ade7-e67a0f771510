from datetime import datetime
import uuid
from sqlalchemy import <PERSON>umn, <PERSON>, Inte<PERSON>, Foreign<PERSON>ey, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID

from app.core import Base, scoped_context

class CloudServer(Base):
    __tablename__ = "cloud_server"

    id = Column(UUID(as_uuid=True), primary_key=True, nullable=False)
    server_id = Column(UUID, ForeignKey('server_details.id'), nullable=False)
    pat_name = Column(String(100), nullable=True)
    pat_secret = Column(String(100), nullable=True)
    project_summary = Column(JSON, nullable=True)
    created_by = Column(Integer, nullable=False)
    updated_by = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
class CloudServerDetailsManager:
    @staticmethod
    def add_cloud_server(server_id, pat_name, pat_secret, created_by, updated_by, project_summary=None) -> CloudServer:
        """Adds a new cloud server to the database."""
        with scoped_context() as session:
            new_server = CloudServer(
                id=uuid.uuid4(),
                server_id=server_id,
                pat_name=pat_name,
                pat_secret=pat_secret,
                project_summary=project_summary,
                created_by=created_by,
                updated_by=updated_by
            )
            session.add(new_server)
            session.commit()
            session.refresh(new_server)
            return new_server
            
    @staticmethod
    def get_cloud_server_by_pat_secret(pat_secret):
        """Fetch a cloud server by its PAT name and secret."""
        with scoped_context() as session:
            return session.query(CloudServer).filter(
                CloudServer.pat_secret == pat_secret,
            ).first()
            
    @staticmethod
    def get_cloud_server_by_id(server_id) -> list[CloudServer]:
        """Fetch a cloud server by its ID."""
        with scoped_context() as session:
            return session.query(CloudServer).filter_by(server_id=server_id).all()
        
    @staticmethod
    def update_cloud_server(server_id, pat_name, pat_secret, updated_by):
        """Updates an existing cloud server."""
        with scoped_context() as session:
            server = session.query(CloudServer).filter_by(server_id=server_id, pat_name=pat_name).first()
            if server:
                server.pat_name = pat_name
                server.pat_secret = pat_secret
                server.updated_by = updated_by
                session.commit()
            else:
                CloudServerDetailsManager.add_cloud_server(
                    server_id=server_id,
                    pat_name=pat_name,
                    pat_secret=pat_secret,
                    created_by=updated_by,
                    updated_by=updated_by
                )
                
    @staticmethod
    def delete_cloud_server(server_id, pat_name) -> None:
        """Deletes a cloud server by its ID and PAT name"""
        with scoped_context() as session:
            server = session.query(CloudServer).filter_by(server_id=server_id).filter_by(pat_name=pat_name).first()
            if server:
                session.delete(server)
                session.commit()
                
    @staticmethod
    def delete_cloud_server_id(server_id) -> None:
        """Deletes a cloud server by its ID."""
        with scoped_context() as session:
            session.query(CloudServer).filter_by(server_id=server_id).delete()
            session.commit()

    @staticmethod
    def update_project_summary(cloud_server_id, summary_data) -> CloudServer:
        with scoped_context() as session:
            server = session.query(CloudServer).filter_by(id=cloud_server_id).first()
            if server:
                server.project_summary = summary_data
                session.commit()
            
    @staticmethod
    def get_all_servers_with_summary() -> list[CloudServer]:
        """Returns all cloud servers with non-null project summaries."""
        with scoped_context() as session:
            return session.query(CloudServer).filter(CloudServer.project_summary.isnot(None)).all()
