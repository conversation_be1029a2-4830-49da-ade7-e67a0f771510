import re
import xmltodict
import xml.etree.ElementTree as ET
from collections import OrderedDict
from typing import List, Tuple, Any, Dict
from app.core.enums import PowerBIReportKeys, PowerBITemplateKeys, PowerBIObjectKeys, TableauXMLTags


def convert_xml_to_dict(data):
    try:
        string_data = ET.tostring(data, encoding='utf-8')
        return xmltodict.parse(string_data)
    except Exception as e:
        raise ValueError(f"Error in converting xml to dict")


def extract_datasource_filter_column(data: str) -> Tuple[str | None, str | None, str | None]:
    """
    Extracts the datasource ID, column value, and filter value from the input string.

    Example:
        "[federated.xyz].[none:Sub-Category:nk]" → ('federated.xyz', 'Sub-Category', 'none')
        "[federated.abc].[cum:sum:Profit:qk]"    → ('federated.abc', 'Profit', 'sum')

    Returns:
        Tuple of (datasource_id, column_value, filter_value) or (None, None, None) if format is invalid.
    """
    match = re.match(r'\[([^\]]+)\]\.\[([^\]]+)\]', data)
    if not match:
        return None, None, None

    datasource_id, filter_info = match.groups()
    parts = filter_info.split(':')

    if len(parts) < 3:
        return datasource_id, None, None

    column_value = parts[-2]
    filter_value = parts[-3]
    
    return datasource_id, column_value, filter_value

def get_fields_data(data):
    if data is None: return []
    queryref_string_list=re.findall(r'\[.*?\].\[.*?\]', data)
    return queryref_string_list

def remove_duplicate_fields(*args: List[Any]) -> List[Any]:

    combined_data = [item for lst in args for item in lst]
    return list(OrderedDict.fromkeys(combined_data))

def find_table_name(table_columns, datasource_name, column_name):
    """Find the table name for a given datasource and column."""
    datasource = next(
        (ds for ds in table_columns if ds['datasource'] == datasource_name),
        None
    )
    if not datasource:
        return None

    for table_name, columns in datasource['table_columns'].items():
        if any(col['column_name'] == column_name for col in columns):
            return table_name

    return None

def generate_projections_data(table_columns_data: List[Dict], calculation_related_data: Dict, field_mapping: Dict[str, List]) -> Dict:
    """
    Generates projection data for multiple visual encodings like rows, columns, tooltips, colors, etc.
    
    Args:
        table_columns_data (List[Dict]): The table columns and metadata.
        field_mapping (Dict[str, List]): A mapping from semantic roles (e.g., Y, CATEGORY) to fields.
    
    Returns:
        Dict: Mapping of PowerBIReportKeys to corresponding processed projection data.
    """
    projections = {}
    for key, fields in field_mapping.items():
        if fields:
            projections[key] = []
            for field in fields:
                if field:
                    datasource_id, column_value, filter_value = extract_datasource_filter_column(field)
                    table_name = find_table_name(
                        table_columns=table_columns_data,
                        datasource_name=datasource_id,
                        column_name=column_value
                    )
                    if table_name is None and "Calculation_" in column_value:
                        table_name = calculation_related_data.get(column_value).get("table_name")
                        column_value = calculation_related_data.get(column_value).get("column_value")
                    projections[key].append({
                        PowerBITemplateKeys.COLUMN.value: column_value,
                        PowerBITemplateKeys.FILTER.value: filter_value,
                        PowerBITemplateKeys.TABLE_NAME.value: table_name
                    })
    return projections

def get_format_data(format_data, field, scope):
    result_value = None
    if format_data:
        for data in format_data:
            field_data = data.get(TableauXMLTags.FIELD.value)
            scope_data = data.get(TableauXMLTags.SCOPE.value)
            if field_data and field_data in field:
                result_value = data.get(TableauXMLTags.VALUE.value)
            elif scope_data and scope_data == scope:
                result_value = data.get(TableauXMLTags.VALUE.value)
            elif not result_value and not field_data:
                result_value = data.get(TableauXMLTags.VALUE.value)
    return result_value

def to_list(data):
    if isinstance(data, list):
        return data
    elif isinstance(data, dict):
        return [data]
    return []

def handle_format_data(format_data, field=None, scope=None):
    color_list, font_style_list, font_weight_list, text_decoration_list, font_size_list = [], [], [], [], []
    attr = TableauXMLTags.ATTR.value
    format_list = to_list(format_data)
    for item in format_list:
        if item.get(attr) == TableauXMLTags.COLOR.value:
            color_list.append(item)
        if item.get(attr) == TableauXMLTags.FONT_STYLE.value:
            font_style_list.append(item)
        if item.get(attr) == TableauXMLTags.FONT_WEIGHT.value:
            font_weight_list.append(item)
        if item.get(attr) == TableauXMLTags.TEXT_DECORATION.value:
            text_decoration_list.append(item)
        if item.get(attr) == TableauXMLTags.FONT_SIZE.value:
            font_size_list.append(item)

    color = get_format_data(color_list, field, scope)
    font_style = get_format_data(font_style_list, field, scope)
    font_weight = get_format_data(font_weight_list, field, scope)
    text_decoration = get_format_data(text_decoration_list, field, scope)
    font_size = get_format_data(font_size_list, field, scope)

    return color, font_style, font_weight, text_decoration, font_size

def extract_hex_color(color: str) -> str:
    """Extracts the first valid 6-digit hex color code from a given string."""
    if color.startswith("#"):
        return color[:7]
    return color

def process_label_data_new(style_rule):
    label_properties_dict = {}
    label_properties_dict[PowerBIObjectKeys.SHOW.value] = PowerBIObjectKeys.TRUE.value

    
    if style_rule:
        for element, value in style_rule.items():
            format_data = value.get(TableauXMLTags.FORMAT.value)
            if element == TableauXMLTags.CELL.value and format_data:
                color, font_style, font_weight, text_decoration, font_size = handle_format_data(format_data=format_data)

                if color:
                    label_properties_dict[PowerBIObjectKeys.COLOR.value] = f"'{extract_hex_color(color)}'"

                if font_style and font_style != TableauXMLTags.NORMAL.value:
                    label_properties_dict[PowerBIObjectKeys.ITALIC.value] = PowerBIObjectKeys.TRUE.value

                if font_weight and font_weight != TableauXMLTags.NORMAL.value:
                    label_properties_dict[PowerBIObjectKeys.BOLD.value] = PowerBIObjectKeys.TRUE.value

                if text_decoration and text_decoration != TableauXMLTags.NONE.value:
                    label_properties_dict[PowerBIObjectKeys.UNDERLINE.value] = PowerBIObjectKeys.TRUE.value

                if font_size:
                    label_properties_dict[PowerBIObjectKeys.FONT_SIZE.value] = f"{font_size}D"
                else:
                    label_properties_dict[PowerBIObjectKeys.FONT_SIZE.value] = "14D"
    return label_properties_dict

def process_format_data_new(style_rule, field_data, scope):
    properties_dict = {}
    properties_dict[PowerBIObjectKeys.SWITCH_AXIS_POSITION.value] = PowerBIObjectKeys.FALSE.value
    properties_dict[PowerBIObjectKeys.LABEL_PRECISION.value] = "1L"
    properties_dict[PowerBIObjectKeys.SHOW_AXIS_TITLE.value] = PowerBIObjectKeys.TRUE.value
    properties_dict[PowerBIObjectKeys.SHOW.value] = PowerBIObjectKeys.TRUE.value

    if isinstance(style_rule, dict):
        for element, content in style_rule.items():
            format_data = content.get(TableauXMLTags.FORMAT.value)
            if element == TableauXMLTags.LABEL.value and format_data:
                if format_data:
                    color, font_style, font_weight, text_decoration, font_size = handle_format_data(
                        format_data, field_data, scope
                    )
                    if color:
                        color_data = extract_hex_color(color)
                        properties_dict[PowerBIObjectKeys.TITLE_COLOR.value] = f"'{color_data}'"
                        properties_dict[PowerBIObjectKeys.LABEL_COLOR.value] = f"'{color_data}'"
                    if font_style and font_style != TableauXMLTags.NORMAL.value:
                        properties_dict[PowerBIObjectKeys.TITLE_ITALIC.value] = PowerBIObjectKeys.TRUE.value
                        properties_dict[PowerBIObjectKeys.ITALIC.value] = PowerBIObjectKeys.TRUE.value
                    if font_weight and font_weight != TableauXMLTags.NORMAL.value:
                        properties_dict[PowerBIObjectKeys.TITLE_BOLD.value] = PowerBIObjectKeys.TRUE.value
                        properties_dict[PowerBIObjectKeys.BOLD.value] = PowerBIObjectKeys.TRUE.value
                    if text_decoration and text_decoration != TableauXMLTags.NONE.value:
                        properties_dict[PowerBIObjectKeys.TITLE_UNDERLINE.value] = PowerBIObjectKeys.TRUE.value
                        properties_dict[PowerBIObjectKeys.UNDERLINE.value] = PowerBIObjectKeys.TRUE.value
                    if font_size:
                        properties_dict[PowerBIObjectKeys.TITLE_FONT_SIZE.value] = f"{font_size}D"
                        properties_dict[PowerBIObjectKeys.FONT_SIZE.value] = f"{font_size}D"
    return properties_dict