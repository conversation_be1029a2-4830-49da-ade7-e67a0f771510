import json
from ..core import (
    get_background_config, get_border_config, get_from_list, get_queryref, 
    get_nativeReferenceName, get_title_config, get_projections_data
)
from app.core import table_json
from app.core import logger
from app.core.enums import (
    PowerBITemplateKeys, PowerBIReportKeys,
    PowerBIChartTypes, VisualRequest
)
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    generate_projections_data,
    extract_multiple_encodings_data
)
import uuid
from app.core import logger


def get_table_report(rows, table_column_data, datasource_column_list, worksheet_name,  worksheet_title_layout, style):
    try:
        overall_table_result = []
        projection_dict = {}
        result_queryref, table_list, select_list = get_projections_data([rows], table_column_data, datasource_column_list)
        projection_dict["Values"] = result_queryref.get(rows)
        from_list = get_from_list(table_list)
        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)
        
        table_json_data = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "projections_data": json.dumps(projection_dict),
            "from_list" : json.dumps(from_list),
            "select_list" : json.dumps(select_list),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }
        overall_table_result.append({"config": table_json_data, "template" : table_json})
        return overall_table_result
    except Exception as e:
        logger.error(f"Error in generating table visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating table visual for {worksheet_name} - {str(e)}")

def process_table_ex_report(request: VisualRequest):
    table_ex_result = {}

    rows = request.rows
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data


    table_ex_fields_mapping = {
        PowerBIReportKeys.VALUES.value: rows
    }

    projections_data = generate_projections_data(table_column_data, calculations_related_data, table_ex_fields_mapping)
    table_ex_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.TABLE_EX.value
    table_ex_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    table_ex_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data    
    return table_ex_result
