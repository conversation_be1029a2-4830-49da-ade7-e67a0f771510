from app.core import BaseService, ServiceResponse
from app.models_old.user_reports import UserReportsManager
from app.models_old.server import ServerDetailsManager
from app.models_old.reports import ReportDetails, ReportDetailsManager
from app.models_old.upload_file_report_details import UploadFilesReportManager


class DashboardServices(BaseService):
    @staticmethod
    def get_sites_count_service(user_id: int) -> ServiceResponse:
        """Get sites count and list from the ServerDetails table."""
        sites = ServerDetailsManager.get_sites_count_and_list(user_id)

        server_names = []
        sites_counts = []
        report_counts = []
        project_counts = []
        total_site_count = 0

        for site in sites:
            server_names.append(site.server_name)
            sites_counts.append(site.site_count)
            report_counts.append(site.report_count)
            project_counts.append(site.project_count)
            total_site_count += site.site_count

        response_data = {
            "sites": total_site_count,
            "server_name": server_names,
            "report_count_list": report_counts,
            "report_count" : sum(report_counts),
            "project_count": sum(project_counts)
        }

        return ServiceResponse.success(response_data)


    @staticmethod
    def get_server_reports_service(user_id: int) -> ServiceResponse:
        """Get user dashboards and worksheets from the server_report_details table."""
        worksheet_dashboard_counts = ReportDetailsManager.get_server_reports_details(user_id)


        # dashboards = sum(p.dashboard_count for p in projects if p.dashboard_count is not None)
        # worksheets = sum(p.worksheet_count for p in projects if p.worksheet_count is not None)
        # projects_count = sum(p.project_count for p in projects if p.project_count is not None)
        # workbooks = sum(p.report_count for p in projects if p.report_count is not None)
        # response_data = {
        #     "dashboards": dashboards,
        #     "worksheets": worksheets,
        #     "projects": projects_count,
        #     "workbooks": workbooks
        # }

        dashboards = sum(p.dashboard_count for p in worksheet_dashboard_counts)
        worksheets = sum(p.worksheet_count for p in worksheet_dashboard_counts)
        response_data = {
            "dashboards": dashboards,
            "worksheets": worksheets,
        }
        return ServiceResponse.success(response_data)

    @staticmethod
    def get_upload_files_reports_service(user_id: int) -> ServiceResponse:
        """Get user dashboards and worksheets from the upload_files_report_details table."""
        worksheet_dashboard_counts = UploadFilesReportManager.get_upload_file_reports_details(user_id)

        dashboards = sum(p.dashboard_count for p in worksheet_dashboard_counts)
        worksheets = sum(p.worksheet_count for p in worksheet_dashboard_counts)
        response_data = {
            "dashboards": dashboards,
            "worksheets": worksheets,
        }
        return ServiceResponse.success(response_data)

    @staticmethod
    def get_dashboard_details_service(user_id: int) -> ServiceResponse:
        """Get dashboard details from both server details and user reports tables."""
        sites_count_response = DashboardServices.get_sites_count_service(user_id)
        user_reports_response = DashboardServices.get_server_reports_service(user_id)
        upload_files_reports_response = DashboardServices.get_upload_files_reports_service(user_id)

        combined_data_w_d = {}
        total_workseets_count = user_reports_response.data.get("worksheets", 0) + upload_files_reports_response.data.get("worksheets", 0)
        total_dashboards_count = user_reports_response.data.get("dashboards", 0) + upload_files_reports_response.data.get("dashboards", 0)
        # you have to send these details as user_reports by combining these two counts
        combined_data_w_d["worksheets"] = total_workseets_count
        combined_data_w_d["dashboards"] = total_dashboards_count


        # if sites_count_response.error or user_reports_response.error or upload_files_reports_response.error:
        #     return ServiceResponse.failure(
        #         error=sites_count_response.error or user_reports_response.error or upload_files_reports_response.error,
        #         status_code=404
        #     )

        combined_data = {
            "server_details": {
                "sites": sites_count_response.data.get("sites", 0),
                "server_name": sites_count_response.data.get("server_name", []),
                # "sites_count": sites_count_response.data.get("sites_count", [])
                "report_count_list": sites_count_response.data.get("report_count_list", []),
                },
            "user_reports": {
                "dashboards": combined_data_w_d.get("dashboards", 0),
                "worksheets": combined_data_w_d.get("worksheets", 0),
                "projects": sites_count_response.data.get("project_count", 0),
                "workbooks": sites_count_response.data.get("report_count", 0)
            }
        }
        return ServiceResponse.success(data=combined_data)
