from fastapi import APIRouter, Depends
from pathlib import Path
import zipfile
import json
import shutil
from app.schemas.prep import PrepRequest
from app.core import STORAGE_BASE_DIR, PREP_FILE_OUTPUT_DIR, PARSED_FLOW_FILENAME, FLOW_FILE_NAME
from typing import Dict, List
from app.core.dependencies import get_current_user
from app.models_old.user import UserOld
from app.core import logger
from fastapi import HTTPEx<PERSON>, status


prep_router = APIRouter()


def convert_tfl_to_tflx(tfl_path: Path, tflx_path: Path):
    """
    Rename a .tfl file to .tflx format.

    Parameters
    ----------
    tfl_path : Path
        Path to the .tfl file.
    tflx_path : Path
        Desired path for the .tflx file.
    """
    shutil.move(tfl_path, tflx_path)


def read_flow_file(flow_path: Path) -> List[Dict]:
    """
    Reads and parses a Tableau Prep flow file (JSON format) into a nested dictionary structure.

    This function extracts the flow structure from the given `flow` file,
    including node information and the hierarchy of next nodes, including
    nested container nodes.

    Parameters
    ----------
    flow_path : Path
        The path to the Tableau Prep `flow` file.

    Returns
    -------
        A list of node dictionaries representing the parsed flow tree.
    """

    if not flow_path.exists():
        raise FileNotFoundError(f"Flow file not found at {flow_path}")

    with open(flow_path, 'r', encoding='utf-8') as file:
        flow_data = json.load(file)

    nodes = flow_data.get("nodes", {})
    initial_node_ids = flow_data.get("initialNodes", [])

    def traverse(node_id, nodes_dict, visited):
        """
        Recursively traverses a node and its children, avoiding cycles.
        """
        if node_id in visited:
            return None
        
        visited.add(node_id)
        node = nodes_dict.get(node_id, {})
        result = {
            "id": node.get("id"),
            "name": node.get("name"),
            "nodeType": node.get("nodeType"),
            "baseType": node.get("baseType"),
            "fields": node.get("fields", [])
        }

        # Handle container nodes
        if node.get("nodeType") == ".v1.Container" and "loomContainer" in node:
            sub_nodes = node["loomContainer"].get("nodes", {})
            sub_initial_ids = node["loomContainer"].get("initialNodes", [])
            sub_trees = [traverse(sub_id, sub_nodes, set()) for sub_id in sub_initial_ids]
            result["next"] = [st for st in sub_trees if st]
        else:
            next_nodes = node.get("nextNodes", [])
            next_trees = [traverse(n.get("nextNodeId"), nodes_dict, visited) for n in next_nodes if n.get("nextNodeId") in nodes_dict]
            result["next"] = [nt for nt in next_trees if nt]

        return result

    # Build the full tree
    flow_tree = [traverse(init_id, nodes, set()) for init_id in initial_node_ids]
    return [tree for tree in flow_tree if tree]


@prep_router.post("/unzip_tflx_files")
def unzip_tflx_files(request: PrepRequest, user: UserOld = Depends(get_current_user)):
    """
    Unzips a Tableau Prep `.tflx` file, extracts the `flow` JSON,
    parses it into a tree structure, and saves the result locally.

    Parameters
    ----------
    request : PrepRequest
        Request containing the input `.tflx` or `.tfl` file path and
        the desired extraction path.

    Returns
    -------
    dict
        A message confirming success or an error.
    """
    zip_path = Path(request.input_path)
    extract_path = Path(request.output_path)

    extract_path.mkdir(parents=True, exist_ok=True)

    # Convert .tfl to .tflx if necessary
    if not zip_path.exists():
        tfl_path = zip_path.with_suffix(".tfl")
        if tfl_path.exists():
            convert_tfl_to_tflx(tfl_path, zip_path)
        else:
            logger.error("TFLX or TFL file not found.")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="TFLX or TFL file not found.")

    # Unzip the file
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
    except zipfile.BadZipFile:
        logger.exception("The provided file is not a valid ZIP archive.")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid ZIP file.")


    target_file = next((f for f in extract_path.rglob(FLOW_FILE_NAME) if f.is_file()), None)
    if not target_file or not target_file.exists() or target_file.stat().st_size == 0:
        logger.error("Invalid or empty 'flow' file.")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid or empty 'flow' file.")


    nodes = read_flow_file(target_file)
    output_dir = STORAGE_BASE_DIR/PREP_FILE_OUTPUT_DIR
    output_dir.mkdir(parents=True, exist_ok=True)
    output_file_path = output_dir / PARSED_FLOW_FILENAME

    try:
        with open(output_file_path, "w", encoding="utf-8") as f:
            json.dump(nodes, f, indent=2)
            logger.info(f"Flow data saved successfully at {output_file_path}")
    except IOError as e:
        logger.exception("Failed to write parsed flow data to file.")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to save parsed flow data.")
    
    return {"message": "Flow data parsed and saved successfully.", 
            "output_file": str(output_file_path)}   
