"""created new columns in all tables

Revision ID: ba480bcde273
Revises: 417256756549
Create Date: 2025-04-14 17:53:07.126267

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ba480bcde273'
down_revision: Union[str, None] = '417256756549'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cloud_server', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('cloud_server', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.drop_column('cloud_server', 'last_modified')
    op.add_column('on_premise_server', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('on_premise_server', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.drop_column('on_premise_server', 'last_modified')
    op.add_column('organization_details', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('organization_details', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.add_column('server_details', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('server_details', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.drop_column('server_details', 'last_modified')
    op.add_column('user_reports', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('user_reports', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('updated_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'updated_at')
    op.drop_column('users', 'created_at')
    op.drop_column('user_reports', 'updated_at')
    op.drop_column('user_reports', 'created_at')
    op.add_column('server_details', sa.Column('last_modified', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.drop_column('server_details', 'updated_at')
    op.drop_column('server_details', 'created_at')
    op.drop_column('organization_details', 'updated_at')
    op.drop_column('organization_details', 'created_at')
    op.add_column('on_premise_server', sa.Column('last_modified', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.drop_column('on_premise_server', 'updated_at')
    op.drop_column('on_premise_server', 'created_at')
    op.add_column('cloud_server', sa.Column('last_modified', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.drop_column('cloud_server', 'updated_at')
    op.drop_column('cloud_server', 'created_at')
    # ### end Alembic commands ###
