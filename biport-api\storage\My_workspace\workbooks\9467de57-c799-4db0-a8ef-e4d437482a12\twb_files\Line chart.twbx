<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20252.25.0609.1419                               -->
<workbook include-phone-layouts='false' original-version='18.1' source-build='2024.2.2 (20242.24.0807.0327)' source-platform='win' version='18.1' xml:base='https://prod-apnortheast-a.online.tableau.com' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <AnimationOnByDefault />
    <IncludePhoneLayoutsOptOut />
    <MarkAnimation />
    <ObjectModelEncapsulateLegacy />
    <ObjectModelRelationshipPerfOptions />
    <ObjectModelTableType />
    <SchemaViewerObjectModel />
    <SheetIdentifierTracking />
    <WindowsPersistSimpleIdentifiers />
  </document-format-change-manifest>
  <repository-location id='Linechart' path='/t/sunilbatchu-828452d6d1/workbooks' revision='1.0' site='sunilbatchu-828452d6d1' />
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Parameter 1' datatype='integer' name='[Parameter 1]' param-domain-type='list' role='measure' type='quantitative' value='10'>
        <calculation class='tableau' formula='10' />
        <members />
      </column>
      <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
        <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        <members>
          <member value='&quot;PROFIT&quot;' />
          <member value='&quot;SALES&quot;' />
          <member value='&quot;COST&quot;' />
        </members>
      </column>
    </datasource>
    <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' inline='true' name='federated.1nl426t13auwkc10rmst40m1iuwe' version='18.1'>
      <connection class='federated'>
        <named-connections>
          <named-connection caption='SPARITY-SRIKRIS\SQL_SERVER_1' name='sqlserver.0k11eaw1owt4cc1b7523912w0z0e'>
            <connection authentication='sspi' class='sqlserver' dbname='AdventureWorksDW2022' minimum-driver-version='SQL Server Native Client 10.0' odbc-native-protocol='yes' one-time-sql='' server='localhost' />
          </named-connection>
        </named-connections>
        <relation type='collection'>
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
        </relation>
        <cols>
          <map key='[ArabicDescription]' value='[DimProduct].[ArabicDescription]' />
          <map key='[CalendarQuarter]' value='[DimDate].[CalendarQuarter]' />
          <map key='[CalendarSemester]' value='[DimDate].[CalendarSemester]' />
          <map key='[CalendarYear]' value='[DimDate].[CalendarYear]' />
          <map key='[CarrierTrackingNumber]' value='[FactInternetSales].[CarrierTrackingNumber]' />
          <map key='[ChineseDescription]' value='[DimProduct].[ChineseDescription]' />
          <map key='[Class]' value='[DimProduct].[Class]' />
          <map key='[Color]' value='[DimProduct].[Color]' />
          <map key='[CurrencyKey]' value='[FactInternetSales].[CurrencyKey]' />
          <map key='[CustomerKey]' value='[FactInternetSales].[CustomerKey]' />
          <map key='[CustomerPONumber]' value='[FactInternetSales].[CustomerPONumber]' />
          <map key='[DateKey]' value='[DimDate].[DateKey]' />
          <map key='[DayNumberOfMonth]' value='[DimDate].[DayNumberOfMonth]' />
          <map key='[DayNumberOfWeek]' value='[DimDate].[DayNumberOfWeek]' />
          <map key='[DayNumberOfYear]' value='[DimDate].[DayNumberOfYear]' />
          <map key='[DaysToManufacture]' value='[DimProduct].[DaysToManufacture]' />
          <map key='[DealerPrice]' value='[DimProduct].[DealerPrice]' />
          <map key='[DiscountAmount]' value='[FactInternetSales].[DiscountAmount]' />
          <map key='[DueDateKey]' value='[FactInternetSales].[DueDateKey]' />
          <map key='[DueDate]' value='[FactInternetSales].[DueDate]' />
          <map key='[EndDate]' value='[DimProduct].[EndDate]' />
          <map key='[EnglishDayNameOfWeek]' value='[DimDate].[EnglishDayNameOfWeek]' />
          <map key='[EnglishDescription]' value='[DimProduct].[EnglishDescription]' />
          <map key='[EnglishMonthName]' value='[DimDate].[EnglishMonthName]' />
          <map key='[EnglishProductCategoryName]' value='[DimProductCategory].[EnglishProductCategoryName]' />
          <map key='[EnglishProductName]' value='[DimProduct].[EnglishProductName]' />
          <map key='[EnglishProductSubcategoryName]' value='[DimProductSubcategory].[EnglishProductSubcategoryName]' />
          <map key='[ExtendedAmount]' value='[FactInternetSales].[ExtendedAmount]' />
          <map key='[FinishedGoodsFlag]' value='[DimProduct].[FinishedGoodsFlag]' />
          <map key='[FiscalQuarter]' value='[DimDate].[FiscalQuarter]' />
          <map key='[FiscalSemester]' value='[DimDate].[FiscalSemester]' />
          <map key='[FiscalYear]' value='[DimDate].[FiscalYear]' />
          <map key='[Freight]' value='[FactInternetSales].[Freight]' />
          <map key='[FrenchDayNameOfWeek]' value='[DimDate].[FrenchDayNameOfWeek]' />
          <map key='[FrenchDescription]' value='[DimProduct].[FrenchDescription]' />
          <map key='[FrenchMonthName]' value='[DimDate].[FrenchMonthName]' />
          <map key='[FrenchProductCategoryName]' value='[DimProductCategory].[FrenchProductCategoryName]' />
          <map key='[FrenchProductName]' value='[DimProduct].[FrenchProductName]' />
          <map key='[FrenchProductSubcategoryName]' value='[DimProductSubcategory].[FrenchProductSubcategoryName]' />
          <map key='[FullDateAlternateKey]' value='[DimDate].[FullDateAlternateKey]' />
          <map key='[GermanDescription]' value='[DimProduct].[GermanDescription]' />
          <map key='[HebrewDescription]' value='[DimProduct].[HebrewDescription]' />
          <map key='[JapaneseDescription]' value='[DimProduct].[JapaneseDescription]' />
          <map key='[LargePhoto]' value='[DimProduct].[LargePhoto]' />
          <map key='[ListPrice]' value='[DimProduct].[ListPrice]' />
          <map key='[ModelName]' value='[DimProduct].[ModelName]' />
          <map key='[MonthNumberOfYear]' value='[DimDate].[MonthNumberOfYear]' />
          <map key='[OrderDateKey]' value='[FactInternetSales].[OrderDateKey]' />
          <map key='[OrderDate]' value='[FactInternetSales].[OrderDate]' />
          <map key='[OrderQuantity]' value='[FactInternetSales].[OrderQuantity]' />
          <map key='[ProductAlternateKey]' value='[DimProduct].[ProductAlternateKey]' />
          <map key='[ProductCategoryAlternateKey]' value='[DimProductCategory].[ProductCategoryAlternateKey]' />
          <map key='[ProductCategoryKey (DimProductSubcategory)]' value='[DimProductSubcategory].[ProductCategoryKey]' />
          <map key='[ProductCategoryKey]' value='[DimProductCategory].[ProductCategoryKey]' />
          <map key='[ProductKey (DimProduct)]' value='[DimProduct].[ProductKey]' />
          <map key='[ProductKey]' value='[FactInternetSales].[ProductKey]' />
          <map key='[ProductLine]' value='[DimProduct].[ProductLine]' />
          <map key='[ProductStandardCost]' value='[FactInternetSales].[ProductStandardCost]' />
          <map key='[ProductSubcategoryAlternateKey]' value='[DimProductSubcategory].[ProductSubcategoryAlternateKey]' />
          <map key='[ProductSubcategoryKey (DimProduct)]' value='[DimProduct].[ProductSubcategoryKey]' />
          <map key='[ProductSubcategoryKey]' value='[DimProductSubcategory].[ProductSubcategoryKey]' />
          <map key='[PromotionKey]' value='[FactInternetSales].[PromotionKey]' />
          <map key='[ReorderPoint]' value='[DimProduct].[ReorderPoint]' />
          <map key='[RevisionNumber]' value='[FactInternetSales].[RevisionNumber]' />
          <map key='[SafetyStockLevel]' value='[DimProduct].[SafetyStockLevel]' />
          <map key='[SalesAmount]' value='[FactInternetSales].[SalesAmount]' />
          <map key='[SalesOrderLineNumber]' value='[FactInternetSales].[SalesOrderLineNumber]' />
          <map key='[SalesOrderNumber]' value='[FactInternetSales].[SalesOrderNumber]' />
          <map key='[SalesTerritoryAlternateKey]' value='[DimSalesTerritory].[SalesTerritoryAlternateKey]' />
          <map key='[SalesTerritoryCountry]' value='[DimSalesTerritory].[SalesTerritoryCountry]' />
          <map key='[SalesTerritoryGroup]' value='[DimSalesTerritory].[SalesTerritoryGroup]' />
          <map key='[SalesTerritoryImage]' value='[DimSalesTerritory].[SalesTerritoryImage]' />
          <map key='[SalesTerritoryKey (DimSalesTerritory)]' value='[DimSalesTerritory].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryKey]' value='[FactInternetSales].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryRegion]' value='[DimSalesTerritory].[SalesTerritoryRegion]' />
          <map key='[ShipDateKey]' value='[FactInternetSales].[ShipDateKey]' />
          <map key='[ShipDate]' value='[FactInternetSales].[ShipDate]' />
          <map key='[SizeRange]' value='[DimProduct].[SizeRange]' />
          <map key='[SizeUnitMeasureCode]' value='[DimProduct].[SizeUnitMeasureCode]' />
          <map key='[Size]' value='[DimProduct].[Size]' />
          <map key='[SpanishDayNameOfWeek]' value='[DimDate].[SpanishDayNameOfWeek]' />
          <map key='[SpanishMonthName]' value='[DimDate].[SpanishMonthName]' />
          <map key='[SpanishProductCategoryName]' value='[DimProductCategory].[SpanishProductCategoryName]' />
          <map key='[SpanishProductName]' value='[DimProduct].[SpanishProductName]' />
          <map key='[SpanishProductSubcategoryName]' value='[DimProductSubcategory].[SpanishProductSubcategoryName]' />
          <map key='[StandardCost]' value='[DimProduct].[StandardCost]' />
          <map key='[StartDate]' value='[DimProduct].[StartDate]' />
          <map key='[Status]' value='[DimProduct].[Status]' />
          <map key='[Style]' value='[DimProduct].[Style]' />
          <map key='[TaxAmt]' value='[FactInternetSales].[TaxAmt]' />
          <map key='[ThaiDescription]' value='[DimProduct].[ThaiDescription]' />
          <map key='[TotalProductCost]' value='[FactInternetSales].[TotalProductCost]' />
          <map key='[TurkishDescription]' value='[DimProduct].[TurkishDescription]' />
          <map key='[UnitPriceDiscountPct]' value='[FactInternetSales].[UnitPriceDiscountPct]' />
          <map key='[UnitPrice]' value='[FactInternetSales].[UnitPrice]' />
          <map key='[WeekNumberOfYear]' value='[DimDate].[WeekNumberOfYear]' />
          <map key='[WeightUnitMeasureCode]' value='[DimProduct].[WeightUnitMeasureCode]' />
          <map key='[Weight]' value='[DimProduct].[Weight]' />
        </cols>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>1</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryAlternateKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryAlternateKey</remote-alias>
            <ordinal>2</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>EnglishProductCategoryName</remote-alias>
            <ordinal>3</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>SpanishProductCategoryName</remote-alias>
            <ordinal>4</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>FrenchProductCategoryName</remote-alias>
            <ordinal>5</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>7</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryAlternateKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryAlternateKey</remote-alias>
            <ordinal>8</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>EnglishProductSubcategoryName</remote-alias>
            <ordinal>9</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>SpanishProductSubcategoryName</remote-alias>
            <ordinal>10</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>FrenchProductSubcategoryName</remote-alias>
            <ordinal>11</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey (DimProductSubcategory)]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>12</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>14</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductAlternateKey</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductAlternateKey]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductAlternateKey</remote-alias>
            <ordinal>15</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>16</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeightUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[WeightUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>WeightUnitMeasureCode</remote-alias>
            <ordinal>17</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeUnitMeasureCode</remote-alias>
            <ordinal>18</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishProductName</remote-alias>
            <ordinal>19</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SpanishProductName</remote-alias>
            <ordinal>20</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchProductName</remote-alias>
            <ordinal>21</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[StandardCost]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StandardCost</remote-alias>
            <ordinal>22</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FinishedGoodsFlag</remote-name>
            <remote-type>11</remote-type>
            <local-name>[FinishedGoodsFlag]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FinishedGoodsFlag</remote-alias>
            <ordinal>23</ordinal>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Color</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Color]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Color</remote-alias>
            <ordinal>24</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>15</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SafetyStockLevel</remote-name>
            <remote-type>2</remote-type>
            <local-name>[SafetyStockLevel]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SafetyStockLevel</remote-alias>
            <ordinal>25</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ReorderPoint</remote-name>
            <remote-type>2</remote-type>
            <local-name>[ReorderPoint]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ReorderPoint</remote-alias>
            <ordinal>26</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ListPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ListPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ListPrice</remote-alias>
            <ordinal>27</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Size</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Size]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Size</remote-alias>
            <ordinal>28</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeRange</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeRange]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeRange</remote-alias>
            <ordinal>29</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Weight</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Weight]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Weight</remote-alias>
            <ordinal>30</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DaysToManufacture</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DaysToManufacture]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DaysToManufacture</remote-alias>
            <ordinal>31</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductLine</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductLine]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductLine</remote-alias>
            <ordinal>32</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DealerPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DealerPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DealerPrice</remote-alias>
            <ordinal>33</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Class</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Class]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Class</remote-alias>
            <ordinal>34</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Style</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Style]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Style</remote-alias>
            <ordinal>35</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ModelName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ModelName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ModelName</remote-alias>
            <ordinal>36</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LargePhoto</remote-name>
            <remote-type>128</remote-type>
            <local-name>[LargePhoto]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>LargePhoto</remote-alias>
            <ordinal>37</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishDescription</remote-alias>
            <ordinal>38</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchDescription</remote-alias>
            <ordinal>39</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ChineseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ChineseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ChineseDescription</remote-alias>
            <ordinal>40</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ArabicDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ArabicDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ArabicDescription</remote-alias>
            <ordinal>41</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HebrewDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[HebrewDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>HebrewDescription</remote-alias>
            <ordinal>42</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ThaiDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ThaiDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ThaiDescription</remote-alias>
            <ordinal>43</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>GermanDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[GermanDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>GermanDescription</remote-alias>
            <ordinal>44</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>JapaneseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[JapaneseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>JapaneseDescription</remote-alias>
            <ordinal>45</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TurkishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[TurkishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>TurkishDescription</remote-alias>
            <ordinal>46</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StartDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[StartDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StartDate</remote-alias>
            <ordinal>47</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EndDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[EndDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EndDate</remote-alias>
            <ordinal>48</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Status</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Status]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Status</remote-alias>
            <ordinal>49</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>7</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>51</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[OrderDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDateKey</remote-alias>
            <ordinal>52</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DueDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDateKey</remote-alias>
            <ordinal>53</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ShipDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDateKey</remote-alias>
            <ordinal>54</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CustomerKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerKey</remote-alias>
            <ordinal>55</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PromotionKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[PromotionKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>PromotionKey</remote-alias>
            <ordinal>56</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CurrencyKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CurrencyKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CurrencyKey</remote-alias>
            <ordinal>57</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>58</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesOrderNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderNumber</remote-alias>
            <ordinal>59</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>20</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderLineNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[SalesOrderLineNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderLineNumber</remote-alias>
            <ordinal>60</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RevisionNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[RevisionNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>RevisionNumber</remote-alias>
            <ordinal>61</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderQuantity</remote-name>
            <remote-type>2</remote-type>
            <local-name>[OrderQuantity]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderQuantity</remote-alias>
            <ordinal>62</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[UnitPrice]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPrice</remote-alias>
            <ordinal>63</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ExtendedAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ExtendedAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ExtendedAmount</remote-alias>
            <ordinal>64</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPriceDiscountPct</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UnitPriceDiscountPct]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPriceDiscountPct</remote-alias>
            <ordinal>65</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DiscountAmount</remote-name>
            <remote-type>5</remote-type>
            <local-name>[DiscountAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DiscountAmount</remote-alias>
            <ordinal>66</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductStandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ProductStandardCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductStandardCost</remote-alias>
            <ordinal>67</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TotalProductCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TotalProductCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TotalProductCost</remote-alias>
            <ordinal>68</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SalesAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesAmount</remote-alias>
            <ordinal>69</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TaxAmt</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TaxAmt]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TaxAmt</remote-alias>
            <ordinal>70</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Freight</remote-name>
            <remote-type>131</remote-type>
            <local-name>[Freight]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>Freight</remote-alias>
            <ordinal>71</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CarrierTrackingNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CarrierTrackingNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CarrierTrackingNumber</remote-alias>
            <ordinal>72</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerPONumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CustomerPONumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerPONumber</remote-alias>
            <ordinal>73</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[OrderDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDate</remote-alias>
            <ordinal>74</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DueDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDate</remote-alias>
            <ordinal>75</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[ShipDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDate</remote-alias>
            <ordinal>76</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DateKey</remote-alias>
            <ordinal>78</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FullDateAlternateKey</remote-name>
            <remote-type>7</remote-type>
            <local-name>[FullDateAlternateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FullDateAlternateKey</remote-alias>
            <ordinal>79</ordinal>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
              <attribute datatype='boolean' name='TypeIsDateTime2orDate'>true</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfWeek</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfWeek</remote-alias>
            <ordinal>80</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishDayNameOfWeek</remote-alias>
            <ordinal>81</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishDayNameOfWeek</remote-alias>
            <ordinal>82</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchDayNameOfWeek</remote-alias>
            <ordinal>83</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfMonth</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfMonth]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfMonth</remote-alias>
            <ordinal>84</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[DayNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfYear</remote-alias>
            <ordinal>85</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeekNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[WeekNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>WeekNumberOfYear</remote-alias>
            <ordinal>86</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishMonthName</remote-alias>
            <ordinal>87</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishMonthName</remote-alias>
            <ordinal>88</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchMonthName</remote-alias>
            <ordinal>89</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MonthNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[MonthNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>MonthNumberOfYear</remote-alias>
            <ordinal>90</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarQuarter</remote-alias>
            <ordinal>91</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[CalendarYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarYear</remote-alias>
            <ordinal>92</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarSemester</remote-alias>
            <ordinal>93</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalQuarter</remote-alias>
            <ordinal>94</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[FiscalYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalYear</remote-alias>
            <ordinal>95</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalSemester</remote-alias>
            <ordinal>96</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey (DimSalesTerritory)]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>98</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryAlternateKey]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryAlternateKey</remote-alias>
            <ordinal>99</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryRegion</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryRegion]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryRegion</remote-alias>
            <ordinal>100</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryCountry</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryCountry]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryCountry</remote-alias>
            <ordinal>101</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryGroup</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryGroup]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryGroup</remote-alias>
            <ordinal>102</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryImage</remote-name>
            <remote-type>128</remote-type>
            <local-name>[SalesTerritoryImage]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryImage</remote-alias>
            <ordinal>103</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</object-id>
          </metadata-record>
        </metadata-records>
      </connection>
      <aliases enabled='yes' />
      <column caption='Arabic Description' datatype='string' name='[ArabicDescription]' role='dimension' type='nominal' />
      <column caption='CLEAR FILTERS' datatype='string' name='[Calculation_295830217743659008]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Clear All&apos;' />
      </column>
      <column aggregation='Sum' caption='Quarter' datatype='integer' name='[Calculation_447545216406446083]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='QUARTER([OrderDate])' />
      </column>
      <column aggregation='Sum' caption='Month' datatype='integer' name='[Calculation_447545216414334984]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='MONTH([OrderDate])' />
      </column>
      <column caption='MonthName' datatype='string' name='[Calculation_447545216416518153]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='MonthShortName' datatype='string' name='[Calculation_447545216416817162]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='LEFT(DATENAME(&apos;month&apos;,[OrderDate]),3)' />
      </column>
      <column aggregation='Sum' caption='Weekday' datatype='integer' name='[Calculation_447545216417103883]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekdayName' datatype='string' name='[Calculation_447545216417656844]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekEnd/WeekDay' datatype='string' name='[Calculation_447545216417820685]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='IF [Calculation_447545216417103883]=1 or [Calculation_447545216417103883]=7 THEN &quot;WeekEnd&quot; &#13;&#10;ELSE &quot;WeekDay&quot;&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='FinacialMonth' datatype='integer' name='[Calculation_447545216418512910]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='IF MONTH([OrderDate])-3&gt;0&#13;&#10;THEN MONTH([OrderDate])-3&#13;&#10;ELSE MONTH([OrderDate])+12-3&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='Finacial Quarter' datatype='integer' name='[Calculation_447545216418717711]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;quarter&apos;,DATEADD(&apos;month&apos;,-3,[OrderDate]))' />
      </column>
      <column caption='YearMonth' datatype='string' name='[Calculation_447545216419405840]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;year&apos;,[OrderDate])+&quot;-&quot;+DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='Month,Day,Year' datatype='integer' name='[Calculation_447545216421498898]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='(DATEPART(&apos;year&apos;, [OrderDate])*10000 + DATEPART(&apos;month&apos;, [OrderDate])*100 + DATEPART(&apos;day&apos;, [OrderDate]))' />
      </column>
      <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
      </column>
      <column caption='DM' datatype='real' name='[Calculation_447545216423968789]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='IF [Parameters].[Parameter 2]==&quot;PROFIT&quot; THEN [Calculation_447545216423890964]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;SALES&quot; THEN [SalesAmount]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;COST&quot; THEN [TotalProductCost]&#13;&#10;ELSE 0&#13;&#10;END' />
      </column>
      <column caption='OrderDate' datatype='date' name='[Calculation_474848296898699264]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATE(LEFT(STR([OrderDateKey]),4)+&quot;-&quot;+MID(STR([OrderDateKey]),5,2)+&quot;-&quot;+RIGHT(STR([OrderDateKey]),2))' />
      </column>
      <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='YEAR([OrderDate])' />
      </column>
      <column caption='Reset Filters' datatype='string' name='[Calculation_778278323468275715]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Reset Filters&apos;' />
      </column>
      <column caption='Calendar Quarter' datatype='integer' name='[CalendarQuarter]' role='dimension' type='quantitative' />
      <column caption='Calendar Semester' datatype='integer' name='[CalendarSemester]' role='measure' type='quantitative' />
      <column caption='Calendar Year' datatype='integer' name='[CalendarYear]' role='dimension' type='quantitative' />
      <column caption='Carrier Tracking Number' datatype='string' name='[CarrierTrackingNumber]' role='dimension' type='nominal' />
      <column caption='Chinese Description' datatype='string' name='[ChineseDescription]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Currency Key' datatype='integer' name='[CurrencyKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Customer Key' datatype='integer' name='[CustomerKey]' role='dimension' type='ordinal' />
      <column caption='Customer PO Number' datatype='string' name='[CustomerPONumber]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Date Key' datatype='integer' name='[DateKey]' role='dimension' type='ordinal' />
      <column caption='Day Number Of Month' datatype='integer' name='[DayNumberOfMonth]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Week' datatype='integer' name='[DayNumberOfWeek]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Year' datatype='integer' name='[DayNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Days To Manufacture' datatype='integer' name='[DaysToManufacture]' role='measure' type='quantitative' />
      <column caption='Dealer Price' datatype='real' name='[DealerPrice]' role='measure' type='quantitative' />
      <column caption='Discount Amount' datatype='real' name='[DiscountAmount]' role='measure' type='quantitative' />
      <column aggregation='Sum' caption='Due Date Key' datatype='integer' name='[DueDateKey]' role='dimension' type='ordinal' />
      <column caption='Due Date' datatype='datetime' name='[DueDate]' role='dimension' type='ordinal' />
      <column caption='End Date' datatype='datetime' name='[EndDate]' role='dimension' type='ordinal' />
      <column caption='English Day Name Of Week' datatype='string' name='[EnglishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='English Description' datatype='string' name='[EnglishDescription]' role='dimension' type='nominal' />
      <column caption='English Month Name' datatype='string' name='[EnglishMonthName]' role='dimension' type='nominal' />
      <column caption='English Product Category Name' datatype='string' name='[EnglishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='English Product Name' datatype='string' name='[EnglishProductName]' role='dimension' type='nominal' />
      <column caption='English Product Subcategory Name' datatype='string' name='[EnglishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Extended Amount' datatype='real' name='[ExtendedAmount]' role='measure' type='quantitative' />
      <column caption='Finished Goods Flag' datatype='boolean' name='[FinishedGoodsFlag]' role='dimension' type='nominal' />
      <column caption='Fiscal Quarter' datatype='integer' name='[FiscalQuarter]' role='dimension' type='quantitative' />
      <column caption='Fiscal Semester' datatype='integer' name='[FiscalSemester]' role='measure' type='quantitative' />
      <column caption='Fiscal Year' datatype='integer' name='[FiscalYear]' role='dimension' type='quantitative' />
      <column caption='French Day Name Of Week' datatype='string' name='[FrenchDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='French Description' datatype='string' name='[FrenchDescription]' role='dimension' type='nominal' />
      <column caption='French Month Name' datatype='string' name='[FrenchMonthName]' role='dimension' type='nominal' />
      <column caption='French Product Category Name' datatype='string' name='[FrenchProductCategoryName]' role='dimension' type='nominal' />
      <column caption='French Product Name' datatype='string' name='[FrenchProductName]' role='dimension' type='nominal' />
      <column caption='French Product Subcategory Name' datatype='string' name='[FrenchProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Full Date Alternate Key' datatype='date' name='[FullDateAlternateKey]' role='dimension' type='ordinal' />
      <column caption='German Description' datatype='string' name='[GermanDescription]' role='dimension' type='nominal' />
      <column caption='Hebrew Description' datatype='string' name='[HebrewDescription]' role='dimension' type='nominal' />
      <column caption='Japanese Description' datatype='string' name='[JapaneseDescription]' role='dimension' type='nominal' />
      <column caption='Large Photo' datatype='string' name='[LargePhoto]' role='dimension' type='nominal' />
      <column caption='List Price' datatype='real' name='[ListPrice]' role='measure' type='quantitative' />
      <column caption='Model Name' datatype='string' name='[ModelName]' role='dimension' type='nominal' />
      <column caption='Month Number Of Year' datatype='integer' name='[MonthNumberOfYear]' role='dimension' type='quantitative' />
      <column aggregation='Sum' caption='Order Date Key' datatype='integer' name='[OrderDateKey]' role='dimension' type='ordinal' />
      <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
      <column caption='Order Quantity' datatype='integer' name='[OrderQuantity]' role='measure' type='quantitative' />
      <column caption='Product Alternate Key' datatype='string' name='[ProductAlternateKey]' role='dimension' type='nominal' />
      <column caption='Product Category Alternate Key' datatype='integer' name='[ProductCategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductCategoryKey (DimProductSubcategory)]' role='dimension' type='ordinal' />
      <column caption='Product Category Key' datatype='integer' name='[ProductCategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductKey (DimProduct)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Product Key' datatype='integer' name='[ProductKey]' role='dimension' type='ordinal' />
      <column caption='Product Line' datatype='string' name='[ProductLine]' role='dimension' type='nominal' />
      <column caption='Product Standard Cost' datatype='real' name='[ProductStandardCost]' role='measure' type='quantitative' />
      <column caption='Product Subcategory Alternate Key' datatype='integer' name='[ProductSubcategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductSubcategoryKey (DimProduct)]' role='dimension' type='ordinal' />
      <column caption='Product Subcategory Key' datatype='integer' name='[ProductSubcategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Promotion Key' datatype='integer' name='[PromotionKey]' role='dimension' type='ordinal' />
      <column caption='Reorder Point' datatype='integer' name='[ReorderPoint]' role='measure' type='quantitative' />
      <column caption='Revision Number' datatype='integer' name='[RevisionNumber]' role='dimension' type='ordinal' />
      <column caption='Safety Stock Level' datatype='integer' name='[SafetyStockLevel]' role='measure' type='quantitative' />
      <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
      <column caption='Sales Order Line Number' datatype='integer' name='[SalesOrderLineNumber]' role='dimension' type='ordinal' />
      <column caption='Sales Order Number' datatype='string' name='[SalesOrderNumber]' role='dimension' type='nominal' />
      <column caption='Sales Territory Alternate Key' datatype='integer' name='[SalesTerritoryAlternateKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Country' datatype='string' name='[SalesTerritoryCountry]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
      <column caption='Sales Territory Group' datatype='string' name='[SalesTerritoryGroup]' role='dimension' type='nominal' />
      <column caption='Sales Territory Image' datatype='string' name='[SalesTerritoryImage]' role='dimension' type='nominal' />
      <column aggregation='Sum' datatype='integer' name='[SalesTerritoryKey (DimSalesTerritory)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Sales Territory Key' datatype='integer' name='[SalesTerritoryKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Region' datatype='string' name='[SalesTerritoryRegion]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Ship Date Key' datatype='integer' name='[ShipDateKey]' role='dimension' type='ordinal' />
      <column caption='Ship Date' datatype='datetime' name='[ShipDate]' role='dimension' type='ordinal' />
      <column caption='Size Range' datatype='string' name='[SizeRange]' role='dimension' type='nominal' />
      <column caption='Size Unit Measure Code' datatype='string' name='[SizeUnitMeasureCode]' role='dimension' type='nominal' />
      <column caption='Spanish Day Name Of Week' datatype='string' name='[SpanishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='Spanish Month Name' datatype='string' name='[SpanishMonthName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Category Name' datatype='string' name='[SpanishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Name' datatype='string' name='[SpanishProductName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Subcategory Name' datatype='string' name='[SpanishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Standard Cost' datatype='real' name='[StandardCost]' role='measure' type='quantitative' />
      <column caption='Start Date' datatype='datetime' name='[StartDate]' role='dimension' type='ordinal' />
      <column caption='Tax Amt' datatype='real' name='[TaxAmt]' role='measure' type='quantitative' />
      <column caption='Thai Description' datatype='string' name='[ThaiDescription]' role='dimension' type='nominal' />
      <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
      <column caption='Turkish Description' datatype='string' name='[TurkishDescription]' role='dimension' type='nominal' />
      <column caption='Unit Price Discount Pct' datatype='real' name='[UnitPriceDiscountPct]' role='measure' type='quantitative' />
      <column caption='Unit Price' datatype='real' name='[UnitPrice]' role='measure' type='quantitative' />
      <column caption='Week Number Of Year' datatype='integer' name='[WeekNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Weight Unit Measure Code' datatype='string' name='[WeightUnitMeasureCode]' role='dimension' type='nominal' />
      <column caption='DimDate' datatype='table' name='[__tableau_internal_object_id__].[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]' role='measure' type='quantitative' />
      <column caption='DimProductCategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]' role='measure' type='quantitative' />
      <column caption='DimProductSubcategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]' role='measure' type='quantitative' />
      <column caption='DimProduct' datatype='table' name='[__tableau_internal_object_id__].[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]' role='measure' type='quantitative' />
      <column caption='DimSalesTerritory' datatype='table' name='[__tableau_internal_object_id__].[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]' role='measure' type='quantitative' />
      <column caption='Sales' datatype='table' name='[__tableau_internal_object_id__].[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]' role='measure' type='quantitative' />
      <column-instance column='[SalesTerritoryCountry]' derivation='Attribute' name='[attr:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Max' name='[max:SalesAmount:qk]' pivot='key' type='quantitative' />
      <column-instance column='[SalesTerritoryCountry]' derivation='Max' name='[max:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_447545216406446083]' derivation='None' name='[none:Calculation_447545216406446083:ok]' pivot='key' type='ordinal' />
      <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
      <column-instance column='[EnglishProductCategoryName]' derivation='None' name='[none:EnglishProductCategoryName:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesTerritoryCountry]' derivation='None' name='[none:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[Calculation_474848296898699264]' derivation='Quarter' name='[qr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:2]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[EnglishMonthName]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:3]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[SalesTerritoryCountry]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:4]' pivot='key' type='quantitative'>
        <table-calc ordering-type='CellInPane' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
      <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_474848296898699264]' derivation='Year-Trunc' name='[tyr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <group caption='Action (CLEAR FILTERS)' hidden='true' name='[Action (CLEAR FILTERS)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_295830217743659008]' />
        </groupfilter>
      </group>
      <group caption='Action (Clear All)' hidden='true' name='[Action (Clear All)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323462979586]' />
        </groupfilter>
      </group>
      <group caption='Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))' hidden='true' name='[Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
          <groupfilter function='level-members' level='[qr:Calculation_474848296898699264:ok]' />
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset Filters)' hidden='true' name='[Action (Reset Filters)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323468275715]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset)' hidden='true' name='[Action (Reset)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323455827969]' />
        </groupfilter>
      </group>
      <group caption='Action (Sales Territory Country)' hidden='true' name='[Action (Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (YEAR(OrderDate))' hidden='true' name='[Action (YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Year)' hidden='true' name='[Action (Year)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Month,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,Month,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115184209922]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,MonthName,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,MonthName,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Quarter)' hidden='true' name='[Action (Year,Quarter)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
        </groupfilter>
      </group>
      <drill-paths>
        <drill-path name='Sales Territory Region, Sales Territory Country'>
          <field>[SalesTerritoryRegion]</field>
          <field>[SalesTerritoryCountry]</field>
        </drill-path>
      </drill-paths>
      <layout dim-ordering='alphabetic' measure-ordering='alphabetic' rowDisplayCount='300' show-aliased-fields='true' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[none:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[max:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[attr:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_447545216406446083:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>1</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>4</bucket>
            </map>
            <map to='#e15759'>
              <bucket>3</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_561261115181088768:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>2010</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>2014</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>2013</bucket>
            </map>
            <map to='#e15759'>
              <bucket>2012</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2011</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[:Measure Names]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409763848:qk]&quot;</bucket>
            </map>
            <map to='#9c755f'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[max:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216406446083:ok]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407900165:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407973894:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407998471:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418045966:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[usr:Calculation_447545216407670788:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:2]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:3]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:4]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912410996746:qk]&quot;</bucket>
            </map>
            <map to='#ff5500'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409743366:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912411025420:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418058256:qk]&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:EnglishProductCategoryName:nk]' type='palette'>
            <map to='#00aa7f'>
              <bucket>&quot;Bikes&quot;</bucket>
            </map>
            <map to='#55ff00'>
              <bucket>&quot;Accessories&quot;</bucket>
            </map>
            <map to='#ff55ff'>
              <bucket>&quot;Clothing&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;India&quot;' />
      </semantic-values>
      <field-sort-info field-sort-order-type='custom-order'>
        <field-sort-custom-order field='DateKey' />
        <field-sort-custom-order field='FullDateAlternateKey' />
        <field-sort-custom-order field='DayNumberOfWeek' />
        <field-sort-custom-order field='EnglishDayNameOfWeek' />
        <field-sort-custom-order field='SpanishDayNameOfWeek' />
        <field-sort-custom-order field='FrenchDayNameOfWeek' />
        <field-sort-custom-order field='DayNumberOfMonth' />
        <field-sort-custom-order field='DayNumberOfYear' />
        <field-sort-custom-order field='WeekNumberOfYear' />
        <field-sort-custom-order field='EnglishMonthName' />
        <field-sort-custom-order field='SpanishMonthName' />
        <field-sort-custom-order field='FrenchMonthName' />
        <field-sort-custom-order field='MonthNumberOfYear' />
        <field-sort-custom-order field='CalendarQuarter' />
        <field-sort-custom-order field='CalendarYear' />
        <field-sort-custom-order field='CalendarSemester' />
        <field-sort-custom-order field='FiscalQuarter' />
        <field-sort-custom-order field='FiscalYear' />
        <field-sort-custom-order field='FiscalSemester' />
      </field-sort-info>
      <datasource-dependencies datasource='Parameters'>
        <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
          <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        </column>
      </datasource-dependencies>
      <object-graph>
        <objects>
          <object caption='DimDate' id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
            </properties>
          </object>
          <object caption='DimProductCategory' id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProductSubcategory' id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProduct' id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
            </properties>
          </object>
          <object caption='DimSalesTerritory' id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
            </properties>
          </object>
          <object caption='Sales' id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[OrderDateKey]' />
              <expression op='[DateKey]' />
            </expression>
            <first-end-point guaranteed-value='true' is-db-set-guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point is-db-set-unique-key='true' object-id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductCategoryKey]' />
              <expression op='[ProductCategoryKey (DimProductSubcategory)]' />
            </expression>
            <first-end-point is-db-set-unique-key='true' object-id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A' unique-key='true' />
            <second-end-point object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductSubcategoryKey]' />
              <expression op='[ProductSubcategoryKey (DimProduct)]' />
            </expression>
            <first-end-point is-db-set-unique-key='true' object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' unique-key='true' />
            <second-end-point object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SalesTerritoryKey]' />
              <expression op='[SalesTerritoryKey (DimSalesTerritory)]' />
            </expression>
            <first-end-point guaranteed-value='true' is-db-set-guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point is-db-set-unique-key='true' object-id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductKey (DimProduct)]' />
              <expression op='[ProductKey]' />
            </expression>
            <first-end-point is-db-set-unique-key='true' object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' unique-key='true' />
            <second-end-point guaranteed-value='true' is-db-set-guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
          </relationship>
        </relationships>
      </object-graph>
    </datasource>
  </datasources>
  <worksheets>
    <worksheet name='Line chart1(discrete)'>
      <repository-location id='Linechart1discrete' path='/t/sunilbatchu-828452d6d1/workbooks/Linechart' revision='' site='sunilbatchu-828452d6d1' />
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
            </column>
            <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
            <column-instance column='[OrderDate]' derivation='Month' name='[mn:OrderDate:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_447545216423890964]' derivation='Sum' name='[sum:Calculation_447545216423890964:qk]' pivot='key' type='quantitative' />
            <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
            <column-instance column='[OrderDate]' derivation='Year-Trunc' name='[tyr:OrderDate:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[mn:OrderDate:ok]'>
            <groupfilter function='union' user:ui-domain='database' user:ui-enumeration='inclusive' user:ui-marker='enumerate'>
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='1' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='2' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='3' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='4' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='5' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='6' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='7' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='8' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='9' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='10' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='11' />
              <groupfilter function='member' level='[mn:OrderDate:ok]' member='12' />
            </groupfilter>
          </filter>
          <slices>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[mn:OrderDate:ok]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style />
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
          </pane>
          <pane id='1' selection-relaxation-option='selection-relaxation-allow' y-axis-name='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
          </pane>
          <pane id='2' selection-relaxation-option='selection-relaxation-allow' y-axis-name='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Automatic' />
          </pane>
        </panes>
        <rows>([federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk] + [federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk])</rows>
        <cols>[federated.1nl426t13auwkc10rmst40m1iuwe].[tyr:OrderDate:qk]</cols>
      </table>
      <simple-id uuid='{73D5BC4D-CA64-4966-B1B6-03473FB7421C}' />
    </worksheet>
    <worksheet name='Line chart2(discrete)'>
      <repository-location id='Linechart2discrete' path='/t/sunilbatchu-828452d6d1/workbooks/Linechart' revision='' site='sunilbatchu-828452d6d1' />
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
            </column>
            <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
            <column-instance column='[OrderDate]' derivation='MDY' name='[md:OrderDate:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_447545216423890964]' derivation='Sum' name='[sum:Calculation_447545216423890964:qk]' pivot='key' type='quantitative' />
            <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <aggregation value='true' />
        </view>
        <style />
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Line' />
          </pane>
          <pane id='1' selection-relaxation-option='selection-relaxation-allow' y-axis-name='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Line' />
          </pane>
          <pane id='2' selection-relaxation-option='selection-relaxation-allow' y-axis-name='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk]'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Line' />
          </pane>
        </panes>
        <rows>([federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk] + [federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk])</rows>
        <cols>[federated.1nl426t13auwkc10rmst40m1iuwe].[md:OrderDate:ok]</cols>
      </table>
      <simple-id uuid='{E02FD74D-2982-4AE8-87D0-4052D7CB7264}' />
    </worksheet>
  </worksheets>
  <windows saved-dpi-scale-factor='1.25' source-height='37'>
    <window class='worksheet' maximized='true' name='Line chart1(discrete)'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[mn:OrderDate:ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[yr:OrderDate:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{9211A772-436E-47A1-9E05-C755D2A20945}' />
    </window>
    <window class='worksheet' name='Line chart2(discrete)'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesOrderNumber:nk]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[yr:Calculation_474848296898699264:ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[yr:OrderDate:ok]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{F9EBC13E-5C82-49F3-9194-468EB8B884C7}' />
    </window>
  </windows>
  <thumbnails>
    <thumbnail height='192' name='Line chart1(discrete)' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAW/klEQVR4nO3d6W8cV7rf8W/tvZFNUiJFLaREUrutXV5mPN7tmcFNJri59yZIMIObmwRZ
      XuV9XgTIXxPgIkGACwTJzPXu8cxY1kZZ1i5uIkVxEbdeq7qqzskL0rTsIWWJbDab7OcDSKRE
      nuqHxf7VqTpVdcrQWmuEaFDmZhcgxGayv/2kUCgQx/GGvEgQBHietyHLFmI9lgOQyWTWvBAV
      h4w/HAfbo7UphZ1I4xCR8yOa00kA0uk0hmGsv2Ihqsj+8W/5cYZh0dq2g/sDg1TmDaL0DlKq
      zFTB58yLxzEMA9M0JQCi7lQnAKaJGZfIduzFKOWoVAoEhkXS+e5NbxiGBEDUnaodBE/Oluje
      vQMv4dGWcjHTWRKJRLUWL8SGMGoxDJrL5WhqapIeQNQdGQYVDU0CIBqaBEA0NAmAaGgSANHQ
      JACioVXlRJjWMXdu3sRJt2KWZjDausioPMMzJc6fOFaNlxBiQ1QlAFEpx/R8nkxk0JJuws/P
      EhuQckAphVKKKIrkPICoO1UJgO2lSKdSWKZGafCMiIqdwYhCTNPENE1s25YAiLpTtTPBFb9M
      pE1cx8I0NLG2QMc4jiNngkXdqkoPAOAmkrhP/Nt84m8h6pW8Q0VDkwCIhiYBEA1NAiAamgRA
      NDQJgGhoVRoG1QzcuYl2m8l64GTbSSifRzmfnr2d1XkJITZAdQIQV5icXqC7r4NSYZZyeRKP
      kEJQQe/ZhdYapZScCBN1pzoXw5kur/70VW7fuI6XaIbYJ7ZdbPP7b3iZhVHUm+oEQEUM3L+H
      m2ljRzaDbUQUjSRmWF6eDkXmBRL1SGaFEA1NRoFEQ5MAiIYmARANTQIgGpoEQDQ0CYBoaBIA
      0dCqdktkcW6KG8NTdKbAaNtHRhUYmS1z+tihar2EEFVXnTPBccjY9ALNCRttO5TmZ6gYGodI
      pkURdW3VAERRhG1bRFGMbT89J1EYEAY+Dx9O0tfXg0NIbKUwZVoUUedWvBTCz8/w9//7H+jr
      7SM007z92vlnWJTG9wNMw8A0NCE2hopIJBJyKYSoWytu2st+QHf3fhwvxaG+g8+4KON7j0Sy
      V1+8EHVjxVGgx48fk0wkMA3wg0qtaxKiZlYMgAoD+q9fJ5fL4QdhrWsSomZW3Ec5cvIlkqk0
      twfHaWtrqXVNQtTMqifChsYe8eorp7l1/UYt6xGiplYNQMazufBVP4eOyfz+YvtaNQAzE2Ms
      lHzmFxZqWY8QNbXiMUDoF8nu7uPskWM0pVM/vhStKRRyGLaHY4DpuJjE+KEilfCqXbMQVbNi
      APovX2THrk7u3h/hpy+d/NGFaB2TyxeYeDRAxjOhuZO0KjK+UOalUy9WvWghqmXFADiW5uLF
      q+TzOdxkivMvPv1kmGHaWDqkog3cZBY/yOMbJmnPkmuBRF1bMQCnX32b068++0JUVGE+V8Q2
      DUzLIusoilYG18/LtUB1RGvNxGyBQrlCJuni2BaObWJbJq5t4djWZpdYczItyjantSYIYy7c
      fMinV4dQGna2pCiWK4RRTCVShFFMGCmU0iQ8m6Rr47k26YRD0rNJJVxSnkPKs8mkPFKeTTrp
      knBtmpIunmtjLs//xPLnW4EEYJvSWjMyscDHV4b4ZniKU32dvHuuh33tzau2UUrjVyLKlZBy
      EFEOFj8W/ZByEFIoVxY/90MKfmXxa+UKQRj/2bI8x1oOSTrhkE66iwFKuiQ9ZylciwFLeg4J
      1ybp2Vhmbe/RkgBsM0W/wsXb43x0eYikZ/POmR7OHOrEczfuwkStNUprtAalNUppgspScCqL
      ISmUK5SDcClMEYVy8F3IKhF+JaLkh5iGgefa2KaBY1t4rrUUGHe550l6Dk1Jl4S3FK6Ei2Mv
      PqDRsSxcx8K2ni1IEoBtQCnNyOQ8H14e4t7YDKcPdvLmqf3sfcrWvh5prYliRRippY8xQRgv
      B6fkhxSXep58KaC8FJqiH1IJYypRTLS0W6fReLZFKuGScC0SS73Nk0FKJ1wJwFZW9EP+dGOU
      z/tHcB2L9871cvbIbtwGPJj9oThWyz2LX4nwg4jScg/03e6cBGCLUUpzb2yGDy8PMTg+x0tH
      9/DO2R7aW1KyftdAArAFaK3Jlyr88cYov7/2gGza452zPZzs24XryNZ+PSQAdUxpzcDDOX73
      1X1GJhd49fhe3ji1n/aW9GaXtm1UJwBaMToyRGgkaHbBye4koXwmcgH793RIAJ7TQsHnj9+M
      8vuvH9DanOTdsz2cPtiJacr6q7bqBEApypUKQ/fvkXAcKk4STy8+IunFY8fI5/NkMpkqlLt9
      xUpzd3SGj64MMTq5wMvH9/LW6QPsaE5udmnbWnUGhw0YGxqgs7uP/PQkRN89IklrLc8Ie4qF
      gs+n/SN8cf0Be9uzvHe+h2NdO7CWxrHlsVIbqyoBUFFAMaigpiZpb8nSZi4+IskIS8uPRrIs
      SwKwJIoVN4en+ejyIOMzBd48tZ///m/fpinlyjqqMTkIrhGtNXN5n8/6h/ny1kO62pt5+2wP
      R7p2PPNZS1F9EoANFivF9YEpPrw8yOOFEq+d6OLNUwdoTsuNQvVAZq7aAFprpuaKfNo/zIWb
      D+nZ3cI/e+0Ih/a1NeRGoJ5JD1BFQRhx7f4kn/YPs1DweePUAV49vpdsJvHjjcWmkB5gnbTW
      zCyU+ejKIBduPuTo/p381evH6N3TKuP2W4D0AGsURjFX703wwcUB/DDmrdP7+emLXSQ9Z7NL
      E89BeoDnoJTm0UyeT64O8/XAJL17Wvn1+yfp3pWVrf0WVbUAaKXQGKAVmCYGGqVYPqGzVWmt
      CSPFxdsP+ejKEJUw5r3zvfzl60fJJN3NLk+sU3UCoGMGbl4mbjqItTCMbt279IikEq+eOVGV
      l9gMDyYX+OTqMP33JzjZt4t/84tT7O+UuVK3k6odA0TFKYYe27jRPGUV4xgGQRRy9PARcrkc
      6XR6SxwDlIKQq3cn+KR/BNsyeeNkF+cO797QWwrF5qnSM8IiBgYGGcslOLi7hbQdUraasVRu
      S0yLorRmbCrHR5eHuDkyzYs9Hfy7vzhDV0dz3dYsqqOhR4HKQcgfvxnls/4RTNPg/fO9nD+6
      B8+RrX2jaLgALN5kMsvHV4a5NzbDmUOdvH2mh84dGcw6qE/UVkMEQGtN0Q+5cHOMT68Ok066
      vHuuh5O9u2TfvsFt6wBorRl6NM/vvhpgYHyWl48t3lLY2SY354hF2zIAC0WfP30zxhfXH5BJ
      urxztodzR3bXfNYxUf+2TQCiWHH/4SwfXhpkeGJ+ebqQnVmZLkSsbssHIFcM+Kx/mD/eGKOt
      Ocn753p5oae9IWc6Fs9vSwYgihV3R2f48PIgDyYX+NmJbt48fYCWjCdbe/FcqjQEopkcH0W7
      zSR0gN3UiqcDpvMV9nTsqM4raM1CMeD310b44vooe3ZmeOdsD8f2t8sthWLNqhKAOCgyV9YY
      Cw8pGiZRoUJCBcwH1QvAR5eH+MdLA/zk+D7+629+RovcZCKqoCq7QGFpgdHZMoafQ2mHkBDb
      tAnDgCNL1wI5jrP0AAWTOP5uPnnLWnyM0rdlmEsjNUqpxQKX2oRhtHzJ8bO2WcvrPPk9a23z
      7TQw62ljGMZy/Su1+eFy19JmPeupVm024vfx5HqqSg9gJ5tQhYdoN0NrJolthBSNFKmlaVFM
      0ySVWt9oTEI2+GIDbMmDYCGqRY4eRUNb3gUqFArf20erpnK5LFP8ibq0HICNnLzWMAzZBRJ1
      SXaBREOTAIiGJgEQDU0CIBqaBEA0NAmAaGgSANHQ1nUtUFDKc/32EGfPvsit/n68bDsJVcJq
      20M6LjI6V+aFQz3VqlWIqltXACw3sTg/ZhwRxBorCokNi8LMNCUURCFKKZRSRFEkJ8JE3VlX
      AGzbwQCUNjh45Cijg/eIUlksFaCcJGYUbomZ4UTjWlcAcrPTRCpmajYHQY6dew/QnHCxTEWA
      C3FQrTqF2BByObRoaDIKJBqaBEA0NAmAaGgSANHQJACioUkAREOTAIiGJgEQDU0CIBqaBEA0
      NAmAaGgSANHQJACioUkAREOTAIiGtmoAFifK1Rs2Ya4Q9WDFO8KCwhz/6x/+D4cO9lGKXN56
      7Xyt6xKiJlYMgOUmOXbkMF6qmSPd+2tdkxA1s2IAbDdBbm4aCmXcdJZsJlXruoSoiVVvirfQ
      TM3O0tK5+o3t5fw8124N8vL5k9y6cQNtp2gyK5hte8moAiOzZU4fO7QhhQtRDasGoKm5menc
      FL5fWbVxIpOlpSlFWC6Q3tEJ5QVClSCan6FiaBwimRdI1LVVA7Bz1x5wM+TK+VUbx1GIXy4T
      aYPC/DyokHQyjaECYisl8wKJurdqAGYePeCTL7/hn/yL36zauOKXadu5Az8y2b+nHe0kSdgm
      pqEJsTFUtCFFC1EtqwZgPjD4z//p77h45TaH9638tPdUU5bupuxTFlyVxxALsWFWPRH2wpE+
      /nThKi+cPFHLeoSoqVUDcOvmN/jlEhNTj2tZjxA1tfqlEFGMPNlXbHer76SbNh07OmhKJWtY
      jhC1tWoPcOLUGczIZ2J6ppb1CFFTK/YANy59zo2haeYLRX79r1+pdU1C1MyKPUB331E62jLY
      OmLk4WStaxKiZlZ9PoBSCrQmVhrHWd94vjwfQNSrVd/ZV69eZmZ8BG9nL2/+5GwtaxKiZlYN
      gEuF9p4XyCTTtaxHiJpa/XJoy6bgl2hOyr0A24HWmqm5Ih9fHaZQrpDybJpSHtm0Rzrp0pzy
      SCcdmlMermNhGgamaWAaxrbedV01AAu5PLfv3SU49hP6+uSusK0qihRX7j3ig0uDBGHE22cO
      0J7toOhXyJcrzOTKDE8sUCgHFMshC8WAShRjLr3pDQOSnkNT0iWTchcDk1j8vDnlkUm6pBMO
      Sc8hk3RxHWuTf+Lns2IARu/fYDZXxku1sqO9rdY1iXXSWjOX9/m0f5gvb4zRu6eVf/XOC/Ts
      bsU0f3xrrrRGKY3SmjhWlIOIfCmgUK6wUAwo+iGzuTIj3wbHDymWQ0pBSBwrHNvCcy1c2yKz
      FJxMwqU5vRiYpqXwpBIOnmPjuRYJx8ayaj9JyYqjQIFfYn52lsnHs+zYtZe9u1a+GvRZyShQ
      bUSx4vbIYz64PMjUXJHXTnTxxsn9NKe9mtUQx4ogjKlEi3+K5Qr5UoVcMVjudQqlCvMFn6If
      EkUxfhgTVCJM0yDpOaQTDumkS8pzlnfRFnseh6aUR1PKJZ10sZZ20yzTfKZgr2TFHsBLpLh1
      81Mmp6fpLEbrDoDYWPlSwBfXH/DJ1WE6WzP88pWDHO3eueY3xXpYlknKMknhLP5Hy7MPooRR
      TNEPKQchhXK4GJ5yQKEcMjq1QLEcki9XyJcCiuUKGtB6scezLZNMyiWdWAzLtz1NOuGSzSz2
      Nk1Jj5TnkE46yxvjVc8DjI2NMTZ4k9Suw5w8cmBdK0V6gOqLlWLo0TwfXBzk3tgMr53o4p2z
      PbRkEg2xnrXWaL24u6a1phLF5EsVin5IruhTKFWWwlJZ3n0r+iElf3FXzbZMHNtcuQe4df0a
      vYd6+PyTCfqyB2r8o4mnKfkhf/hmlE+vDpNNe7x7vpd//0/P4Npb6+BzvQzDwDDAZDHsjm2R
      TrjP1FZrTRgpKlG8cgDiIM/f/4//ydu/+ueMDgxWr2qxJlprxmfy/L8v73Nr5DFnD3fyX/76
      ZXa1ZTa7tC3JMAxcx8J1rJV3gbTWxEphGQZKs+6jc9kFWptyEHL5ziM+6x8mjBXvnevl5eN7
      G25rv5FW7AEMw8C2Flfys61qxa3rX+NkdpAiwG7bTSouMjbvc7S3u3rVNgCtNZNzRT69Osyl
      O+O8cKCd3/ziFF0dzctj86J6qnPXerw4Dtyc1oRKk5ueokBMHIYyL9AzipXm1oMZfnvhPnP5
      Eu+e7eG//e3rpBOLoykqjlGbXON2VJUAKCyOnzjB8MAdLK8ZUwfEdgLTCDEMY3leIPHn5go+
      H18e4subY/TsbuFv3jpO357WzS6rYaw6DPo8tFKMjQxiJlvIphPYZoyvPYw4IJvNyjHAD8RK
      cXd0ht9+NcDkbIHXTnTzxqlusunEZpfWcKoSgB8jAVjcty+WQz7/eoQvvn5AW3OSn7/Uxws9
      7VimPKdks0gANpjWmsHxOT64NMi9sdnvnbASm08CsEGKfoXLdx7x0eVBXMfmly/3caJ315a7
      WnK7kyPTKtJaMzFb5LcX7nFjeJozh3bzH391jr3tzZtdmliFBKAKwijmyt0JfnfxPkpp3jnb
      w6/fPylb+y1AArBGT56wunrvEb172vjbX5yiuyO7KVdhirWRY4DnFCvFjaFpfvvVfebzPu+e
      6+GV4/vIJJ/tQixRX6QHeEbzBZ/fXxvh82sP2NfRzF/+7CiH9rVti1A3MukBniKOFXdGZ/jg
      0iATMwV+eqKL1092yxDmNiI9wA9orfErEX+4PsrHV4Zoa07y3rleXuhpx5GrMLcd6QGWaK0Z
      nli8w+ru2AxnD+/m5y/1sTMr08JsZw0fgHIQcunOIz68NIBjW/zi5YOcPrhLtvYNoiF3gZTW
      TM4W+ejyIJfvjHPqYCf/4Vfn2LuzPkMqNk5DBSCKFZduj/OPlwaIY807Zw/wN28eJ+E11GoQ
      T9j2v3mtNbP5Mh9eGuLSnXH69rTyd788zT65w0qwjY8Bwijm5vA0H18ZZmq+yOsnu/nZie6a
      ThIl6t+26wHypYDPr43wydVhundl+eUrfRzp2pxJokT92xY9QKzU4jX3FwcZGJ/j9ZPdvHXm
      ANm0Jwe14qm2dACKfoU/XB/l82sjtGQSvHuuhxd7OmQIUzyzLRcArTXjj/P83y/vcWvkMeeP
      7uH98720P8cclEJ8qzo3xeuYOzdvop00GaOCtWMP6bjIg7kSJw73VSUAfiXi4u2HfNY/QhQr
      fv5SHy8f24u9CVNqi+2jKgfBUamA19KB4S9QUTbh7GPKKMz4u3mBSqXS8hQpcRwvt7UsC6UU
      3+bQXLpBXKnFWXC+bfP5tWGGxuf5l28epqsji9aawC8TPKXNWl7nye9Zaxut9fL3rLWNYRjL
      9a/U5ofLXUub9aynWrXZiN/Hk+upKgEwHZtysYgRVkh4aSwVEDtJzCjENE1M0ySVSq2rB/j5
      K0erUaoQ31OVAFhumt2tKZS7i7RrYpmaQDsYcaUaixdiw1TtPEBre+f3/u088bcQ9Wo5AIVC
      4Xv7aNVULpepwWCTEM9tOQCZzMbNNW8YxvIokFJq+eDlh572tTiOsayVx/dXa/dt6FY69lhr
      HbX8mlJq6UEQf17/Wn+2ta5HrfWqNdbDuvqx+mHldbVlzgNoreWsbhXIevy+LTOILr+06pD1
      +H11eTGcVjH37t7Cj2w6WhJMTM/Rc/gYI7f76Tp2nsLEIDN5n6PHXyThbJkMb4rBe7eYL0R0
      723j4fg0ew4cZP7BDZJdp2lS8wyMz3D2xPHNLnPT1Oe7xzDp7urCNE1ypZBTpw4xNT7L7l1t
      hLHGjwwOde9idj6/2ZXWvX1d3Ti2xex8iROnjjE/NUPXnnaCUNHU2k7SaeweoS4DoOMKt++N
      0Nd7AKIKjyensNNpojCkEoZEYcDs3DyuJ9f2P53i9u279PT14BAxMz0DrkcYRoRhhTiOCcOQ
      KG7cZ8/UZQAwTVqa0kxNP6b30EECnaZrp4evE8SlBfp6e7AzO9nZLPPzPJ2mNdvC1OQ03QcP
      EYTQ07WT2bKFF+XIzc+STqUplPzNLnTTbJlRICE2Qn32AELUiARANDQJgGgsWrOwkAMVkcuX
      6vM8gBAb6dIXH9Pa0YHX3is9gGgwhsEbb7/Bhf5B2tWEBEA0HjvRzOG+Lpp2dMowqGhs0gOI
      hvb/AeMQzfJgQEyLAAAAAElFTkSuQmCC
    </thumbnail>
    <thumbnail height='192' name='Line chart2(discrete)' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAgAElEQVR4nO29Z5Qd13Wo+VXdnG/f2zmjI3IGATCApERKooLtJ1uWrGjZlu3Rs2zPeL31
      Zj17PJple73nNZ55jmNLlmxJFGVZiRItMYsJsRG7AXTOOd2+Od+qOvOjGyBBAt11gSbQBOv7
      A/Sqe6rOqTr7nH322XsfSQghMDB4lyLf6QoYGNxJDAEweFdzRwRgaWnpTjzWwOAt3BEBMJYd
      Bm83SiFPOpNB1a7f1/L5PAJDBTK4C1HTi/zr17/BKz9/ju88+TzKdYTgK//f31MAzLe/egYG
      by/Dp1+k6sCHeWxvJd/56j/S1+fm5NEhzO4gDb44C1kPS2mNpdEuYwYwuPtQFRWz2YwkSZgk
      CVXJkzWX8rlPHeHiYJqP/8pHCDplzh19wZgBDO4+Wg++h1ee+BkdqU2k3bWUOawEgg5kHATt
      aU6d6ySRETS1bUG6ExthoVCI0tLS2/1Yg3cR6USUcDxNaXkFZvJkchIet51cOk4olsVht+D3
      ea8VgEgkclssNIlEAo/H87Y/x8BgLa5Rgbxe79v6ME3T0DSNXC6H0+l8W59lYKCHawTAZDLd
      1E1UVUFRVCxWK5LQyBeUq/8XkgkJDU0Di8UCLP9rt9tvvfYGBrfILS+C1Vyc02cvYbZaaNm2
      l+nuUyRVGclZxvaSFDOu3ZjnLiACLTRV+tajzgYG68Ytm0EXJsfJCQlFlTGLJGm5lEOHDmPJ
      RlERLEz0EVJ8bDI6v8EG5JYFQCkoVDa0s29HLT3do0iyDEhIkoQAEok4ksmMdOt1NTBYd25Z
      ACpqa5gd6WdoeAJ3aQ2W3ALdPZfImJyYkWjedg8BEWJoNoLhAWSw0VhzH0BTC6hCxmI2Ucjn
      MVmsyG8azrOpOMmcRqDEh1DzhCMJ/IEAJq2AItuwSCrZvILdbkPC2Acw2DisOgMIIYiMdXKq
      Z5xcLsfJF59iKfvW39ldXkoDfmRJwmS2UVZWisUkI1tsWE0gySYcK53fwGAjsaYV6OUXnuVS
      2M7lEz42bd1HwLBeGtxFrK0CFVJMzCVxS3FGwnBgRyvSLQ7lhgpksFFYcwbILIwyNAsLvWex
      281sam2lzHE7qmZg8PazphXI5itl8NRzuBt3Ux4oxWG5HdUyMLg9rCkAZmeATVVljHe+yoIU
      xGU4UBvcRawpAMmZfqSGg3zpD/6A8vw4i5nbUS0Dg9vDmuO5s7SW+We/xxODHeTlAPfYbke1
      DAxuD6tagUQ+znQEaspcZHMFlhYXqaqv4+Z8Rl/HsAIZbBRWVYHCgyeZSFuWN7Icdqb6zxE2
      VCCDu4hVVSBf/TbOfeNxLA8cRo3P0z2nstdQgQzuItbcCEtH5zlz4TKSPcChe3ZjNb11F0zN
      p/j5a2d4+OEjhKcGGZhcoLZ5O9XWGGFbI+7sJGHFQ0OlHzBUIIONw9qLYH8FDz5cceMfCMFQ
      by+q2YymZhmdTXHv4cOcOXOeshorcSXF2PAc+w7Vrme9DQzWhTUFIDU/xHjOx9b6MoYunaa0
      7R78b1CD4ouTqJ5qSvOToOYwO7yYTGacZjMKgjOv/Zy9B+/DKkuoqooQAlVVKRQKb2e7DAx0
      saoACCF49skn6FxyUVfuR7Z5+eTWe675TS6XIzI7ydjYKFZvKYXEEvl8DfGCigULB9/zGJHe
      U8TKD+OzLz/OZDJdjQ82MLiTrG4GFYLkwgjdsyo72+oIL4Woqq3jOssApqYmqayqIRWeoWdo
      iqYtOymRosQt1XiJMRfNU1ddZsQDGGwoVp0BJElCKqRJag6cTgcXj53DVlp3XWe42to6AHxl
      tRwuu6LvO1nu5j7qjSwoBhuQNdcAroomQi98m29efA3Z28B+Ix7A4C5iTQFQFJXWbTtJZRUc
      Hj+qCmbDIc7gLmHtoPgVq40sS/Rd6iJvRLYb3EWsOZYLTSWZTJBToJDPklcBw4BjcJewpgDI
      Fhs1tfUoGmzbfcCICTa4q1hVAOKTl/jKt58kkpUJeh1kNSv/+fe/dM1GmIHBO5lVBcBbt4Mv
      fk7i5WGV9x/ayonnn0Ix1gAGdxFrLoJdFa1YQ7088Z1vs0i5Mfob3FWsbQWSzVRWlmOSLDQ0
      1V93F9jA4J3KmgKQXhhiMG7lQx/+APPdJ4lcJzOcgcE7lTWtQBanh+jkSc6bkswnJezGJpjB
      XcSa3VlTobGtneb6Gg7fX4nTEACDuwhd+wATPRcwWcwkM1na2tqw3mpUvIHBBmF1ARCCVDyK
      t6yCQirGkqZxnVPnDQzesawqAIXoKN/43nP4nBK12+/jYHvlW38kBNlMmrwKHrcToSkkU1mc
      bjcmoaBKFkyoFFSB1WLoTwYbi1V75NLYZe7/2O+ww73Esa5huI4AZJNL9AxMIClJrJXbUOd6
      Ua0OcpKbHcEMM67dyDPnMZdvpqHcOBvYYOOQzSurR4RFRs7yt99+BhmBqmm4S2v4wm99Ad8b
      /IGEECA0Jvo6yXsriS4kObC3jQtnztFcaeLcrAmv083e7U0ITQOWI8KCweDb3kADg9V4+cLY
      6jNASdN+/s8/3b/6XYTK5QvncFZsoqXSwdmF5LWXhYamCYwDwgw2Eol0jh+91nfr5wQvjfcy
      HVeptS2w6N6ErbDIhQtJchYPJjLUte7GOn+eweklWmuDSIAsyzd9KLeBwXrw8/PjWM2m1VWg
      6Hgnf/et/7j6t6Ok8i0qkFrIkUwvbw/bHC6sJnGdRbBGQVGxWC1GULzBHSeWyvKHf/ccv/2R
      vWtnhgPIpmKEYykkk4WysjLMt3i4qiEABncKTQi++WwXozMR/uSzR9b2BcqFR/nKV7/O4//2
      Xb7//R+SUW9HNQ3uBEvxNEcvjqOq2p2uyjXMhZN8+4WLJDP5W77X1EKcF8+O8NkP7MJqMa29
      BlBzWbbe8yBL06M4RIacAh4jJPKuQ9ME//STc1wYnGV0NsqnH92J/OYDoe8ABUXlX56+QOfg
      HJPzcf63Xz2EzXpzS1chBN9/pYc9rZU0V5cAOrxBLd5ymhvqObyzFX9NOx4jHuCu5KXzo4zO
      RvijXz3MC2dGeKVz7E5XCSEEr3WNMzIT4c9+82FmQgm+9VzXTd9vaDrM5ZEFfvnBLUgrR52u
      eVB27+mXOHmuk2gO5sd6Sd76LPSuZyaU4NXOMfLKxtAnx+aifPO5Ln77I/s4tK2WP/rEYb72
      0/OcvDx5R+u1GE3z+PMX+dz7d9FWF+S//Nq9nOmb4cdH+9CxdL0GRdV44oVL3Lu9lsaVLOWg
      Jx4gp6Dm0oQjMfY88H4jKP4WmV6M8+ffeo1//ul5/uq7J0hl7+yIUlBU/uVnF9jTWsk9W2qQ
      JIndLZV89v27+OpPz9M3Hroj9VJUjW8+28nWhjIOb69DkiTqK3z85/90gO+93M3ZvpmihODi
      8Dxjc1F+6YHNV0d/WEMAJEni0Pt/hWafYGign8n5yM23yIDJhRh/8fhR9m+u5q9/7wNkcgr/
      7asvMTp7Z96rEIKfnhggksjw+cf2XHPtfQeaec+eRv7n908xF07e4A5vH691jdM3scSvP7Yb
      s+n1brqrpZIvfGQff/2DDoZn9L23bF7hiRcu8aHDbZT5XddcW3MGSM0NEnbU8+nPfApt9hJL
      dzgiTFE1kpk8oViaoekw3aMLZHLFp1pXVI3R2QgXBmfpGVtkajHOXDhJKJYmlsySSOcorKOK
      sjzyH2VHUzmfff8uSv1O/vgzD7CzuZy/ePwoJ7sndY1oQgjyBZVcQUFRNbQiVYE3MjIb4cfH
      +vns+3dR4rl2apdliV97ZAdbGkr5q++eIJZa/cMLIcgry/UqKOry7v9NshRL8+3nL/LJR7ZT
      XuJ6y/WHdjfwC/e183//2wnmI6k173eqe4p4OscHD7a85dqay2m7t4z57u/zk5leYlkb96+z
      BShXUHj65CB5RUWWZcyyhMVswmySMZtkTCaZZDrHQjRFKJZhMZoinsoRT+VQhcBqNlEVdPO7
      v7iflpqArmfGUzm+9VwXRy9O4HXZyOUVEuk8kgQ2ixmbdfn5JR4H77+nmYd2N14zChXLdCjB
      nz9+lPb6IL/5ob1X72W3mvn8Y3uoLvXy9z86w3wkxUfubcMkv/VZiqpxcXiejt5p+sdDaEJg
      NstYTCZMsoTNasZilrFbzWyq9PP+e1pw2m/8sTK5At94upP7d9azr736ur8xm2R++yP7+Mvv
      HONvftDBf/v0A295D/FUjrP9M1weXWBkJoKmieVvZ5Yxycv1sZhkbFYzLTUlPLq/edV6qarG
      t57roqm6hCO7Gq77G0mS+OiRzcyEEvzVd0/wp587gsd5rXUmnsrRNTxH19A8Z/tn+NWHt+Fy
      WN96r1V3gkfPESnZR6NXJZXO4nI5r9GfbpY3boQl0jm+93I3kUQWVROoqkZB1cjmFRACRRO4
      7BbK/S7KSlxUBdyU+pyUl7gIeB1kcgX+/aVunj09xIcOtfLRB7fgsr+1obDciU73TvONZzop
      L3Hxu7+4n9oy79XreUUllcmTyhTIFhQGJpd46ng/AB9/eBv376zHYi7OhWNyIcaff+som+uD
      /OHHDt3w/Q1MLvHfnzhGe12QL330HlwOK8lMnr6JEKe6pzjTN4PLYWFHUwX72qowyRK5N8wE
      ubxKQVHJ5hU6h+dZCKf49Pt2cN+OeuzXMRs+8cIlTnZP8j9+5xHc1+kYbySayPLlb7xCa22Q
      X3vvdkZmI/SMLdI1NM/EQoy6Mi9tdUH2rtQrr6jk8irKyncsKBq5gsKlkQUWoik+9tBWHtrT
      iPU67/LoxQm+9tPz/NUX30eZf/WU4tmcwl88/hoep43f+NAeJhfi9I4v12t4JkxNqZe22gD3
      7ahnd+t1XPlZQwBmzz/Fdztz1K54MVvdJTz6yCM4b3EWKGYnWNMEksSqgqcJQe9YiH95+gIF
      ReVzj+1mT0vlVTu2EIL5SIrHn79I33iIjx7ZwiP7NumyJ8dTOX5+boSnO4Yo9Tr4lYe2sru1
      8rqj9JuZXozzZ996jc31pXzxlw5gtawuPHNLSf7uRx3kFY3aMi+944vIksTetioOba2lqbpk
      1dHzCrmCwsnLU3zv5W6sFhO/9t7t7Guvvjp690+E+O/fPsYfffwwO5pXOf7qDcyEEvzpv7yM
      qgkkYGtjGXvbqmivC1Je4tI1MOQLKq91jfPdly5TV+7jE+/ZRmtt8Op3Woql+eOvvcRHj2zh
      0f1NugbbaDLL//H1l4mncljNJlpqAxzYXE1LTYCKgAvbGjEoawpAv+Netpet/Nhkxu/zcwva
      APD2uUJk8wrPnR7i31/q5t4ddXzqkR34XHae6Rjkh6/2sqWhjF9/bNdbFkJ6SGbyPH1ykJ+e
      HKCtLsjHVz7ejZhajPMXjx+lrTbI7330gO6ZI50t8KPXelFUjf2bq2mtDaz5EW9EIp3n2Y5B
      fnZqkKbqEj796E4qSlz88ddeYn97NZ98dAdyETN6KJYmHM/QWOW/7uitl2gyyw9f7eGFsyM8
      sLOBTz6yHb/bzt/8oINYKscff+atqtZa95tdStJU5S96k2xVAcjFFsjZy/Gu8+bX2+kLJIRg
      dinJP/3kLDNLCcp8TqKpHJ98ZAf3ba+7pd1NIQSJdJ4fvdbLK51jWM2mG3agdK7A3rYqvvTL
      9+iaLd4uhBBEk1mePNrHKxfGcDus2G1m/sdvP7LmjPR212tqMc43n+1iaDrMwS01nOqZ5s9/
      62Hqyn23rR66nOHWm9vhDJcvqBy7NMFcOMn7DjRT6lvfI2rmwklmQ4kbWmEcNgsttYFbGinX
      EyEEY3MxjnaN89CeRuorbl8nW428otLRM8WTr/XxkXvbeGhP47qsM/Wy7gIwP95vnBNs8I5h
      fedm7fVzgufHBymko8RTKS4PzFF1G6c1AwO9rK8A3OCcYF9tE9YN4FloYPBm1jdPidmx6jnB
      bouEEIJcLkcikXhLcVVd3nmVZfm6eqBxffm6JEnI11lYv93XNU1DCHFXXV/3NUBscco4J9jg
      HcNdawUyMNDDNSrQ0tJS0X7WN8P11B8DgzvBNQJwO5NVGTOAwUZgXaxAaj7F8y++QkHVmB/v
      5+ixo4zORsgtjTGbhERokvG56Ho8ysBgXbl1K5AQDPX2oprNaOrr+wBnzpynrMZKXEkxNjzH
      vkO1qKqKWDl4u1Ao3offwGC9uWUBiC9OonqqKc1P3nAfYO/B+1b2AZbdAkwmExaLkVrC4M5z
      yypQLpcjMjvKwMAgfRPhlX2AzMo+gMTB9zxGcqKHWFZZj/oaGKwr62YGnZqapLKqhlR4xtgH
      MHjHYOwDGLyruXOO6gYGGwBDAAze1RgCYPCuxhAAg3c1hgAYvKsxBMDgXY0hAAbvagwBMHhX
      c8u+QJl4iM5LfeRzaWq3HEKZ62EukcVd2sgWX5wZ504coS4yrgZaqv1r39DA4DZyywLg8Jay
      f/8+poa6yRUSxFQPDx45yNmOMygeC8mlaUJxC/e0+q8G2wghbkvgjYHBWqxLUPzM+BChZJ5S
      r8BktgISZpMJAQwP9tPc1IbMcg5PTdPQNA1FMZzjDO48tywAqegiOIJUWQQz4QJyap7JSQtJ
      YcGMxs57HkKbOMNEyE9dqRvzyiHZhju0wUbglp3h1EKOmelpFMlGfV01opBmYnqR6ro6LFqG
      vOzGJhdIpHJ4vW7DG9RgQ2F4gxq8q7njZtBkJs9zp4du6agfA4Ob5Y4LQDyV4zsvXmZyPnan
      q2LwLmRNAchGZhhfWO6c0yP9pNY5lr0q6KYy4KJ7bHF9b2xgoINVrUBCCF566gk6w242VfoJ
      x9J85jfa17UCkiRxeFsdZ/tm+MDBlqJOLDEwuFXWNIPuvf8DVOYc1JT6KQkEKPIEGl0c2FLN
      917uJp7K4XcbJ3Eb3D5W7c6xiS5evTjBzEAXyTw4Sir5wm99Ad8699Fyv4vaMi+dQ3M8tLtx
      fW9uYLAKq6tASp7NW7ayY99httaXvW2VsJhN7Gmr4sTlSUMADG4rqwqAO1DKC//wTXLOCi5U
      +LB5Ajz2gcdwrX6s7E2xu6WCZ04NEoql1/08LwODG7GqAFj8m/i93/8SC0kVu1nG4/Pz5mNq
      NTXPSH8viynB7l07UJMLXB6YpHXbTrwiRsJSgUsLE0mbqXzDodRvprU2iM1qpnc8xAM769el
      cQYGa7G6GVSSsFgkXnnmKV54/lm++8NnyL9pv6qQzeCt3MTeJjddA1Nc6h5jz57t9Fy+RD42
      y1K6wMXOXly+1c/mNZtk9rdXc6Zv+lbbZGCgmzVtOtnIHP7mfTx23zZe/tmPSOTA5nj9us3l
      w6MuceHSApt3bmZILcNmc+KzmCggOPPKc+zafwiP1bRmctwD7ZX8/ZNnSaYzN304tIFBMazZ
      yxxlm3Cee5rvfOcS9ZsPEHBce72QjnCxf4Kd++/BaSmgZJNomkZGKWDGxp57jxAe7iVXG8Rm
      Wj057uaGcmxWM8OzcXY1V6xPCw0MVmHNneBCKsxUwsQnPv1ZHj649S0F4kuLJBNxLpztYHJJ
      ob7UyrHjxwjUtmC2e/C6vezeVs/41AJrefs47RZ2NpVzutdQgwxuD2t6g2bCU3zlq18nodkI
      lNfwmc98Bq/t1h66mjfouf4Z/vmn5/nrL30A+9ux62Zg8AZW72FCkMkLPvKp36WproLb4aSw
      ub6UWCrH2GyUzQ2Gy7TB28uqKlB2vptv/PDnnH/1P3i1c/y2VMhhs7CruYLjlydvy/MM3t2s
      KgDRmRH2PPwh7rv/AaIzfSxFoqja21whWeLA5mouDM6ivN0PM3jXs6oAuCpb6X3l+/z46Z8z
      MzbMT55+gfRtONprT1sViXSeyQUjRsDg7aOgqKuvATzVW/jiF7fcrvpcJeBx0FIToGtonk1V
      Jbf9+QbvDs4PzN75iLAbcXBrDSe6jXWAwdtDXlF58mjfxhWA3S2VTC7EWYym7nRVDO5CTnVP
      EY5nNq4ABH0ONlX6OXl56k5XxeAuI1dQ+NFrvXz43raNKwAmWWZXSwVn+mdQNcMaZLB+dPRM
      k80rPLyn8dYFQAiVyaFuekaW3RdCU8McP36ciYUYufAE80lIhmduKuvDvdvr6BsPsRTL3Go1
      DQwASGcL/PhoHx881IrHabt1AVCyGVTZQjIRBS3L0ESUgwcPMD3STyEVJprOcLl3krJST9H3
      rin1UhV00zU8b+QNug1oQlBQ1DtdjbcQT+U42jW+LnU70zdNJq/w6P4mYB1yg1ocbmoqygkl
      p0HNYXb5MJutuMxmFARnX32BPQfvw26S13SHvh6feM9W/uHH50ils3zwUAuSkTXibePoxQme
      OzPKb31oF42VGyOVvappfP1n53mlc5wjO+v53V/Yi9Viuql7ZfPLuv9HDrdilqFQKKxPduir
      mO0oyQiFQo5EQcGMlQMPvZ9I/1kSFYfw2FZ3h74eh7fX43LY+LsfnWY2nObXH9uFw/bOTqyb
      zhaIJrNUBtzI8sYQ6KmFOP/yTBdbGsr4y387yR9/5oE7vgcjhODVc6N0jy3y5c8/yD88eYZ/
      e6mHz39wNya5OOVFCMGpzgk0AQ/v3XS1/5m+/OUvf/lWKpmOLXChs5t4PI6w+qkLWujqHmRT
      +7blDm8P0FjpZiGSxud1IQHpdBqnU1/cryRJVAbc7G2r4sdH+zg7MMPO5gqc71AhSGXy/L/f
      O8V3XryEzWKitTZ4x2e1dLbAX333BDuaK/jSR+8hnS3wby92s7u1Eq/rFl1/b4GpxTh/+4PT
      fO4Duzm0rZadzRX8+0vd5PIqWxpKi3pv+YLK3/3oDB++t+0aJ8tbFgCL3UVdQwPNTU0EfC4c
      Hj91dXV4nDZMDi8uK5itDvwrnR+KE4AreF02HtjZQNfQHD96tZf2+iBB7zsreD5fUPnqf5xn
      PpLiU4/u5LsvXWYunGR7Uzlm050xyGma4FvPdTEVivNfPnEvNquZnc0VhBMZnnj+EnvukBBk
      8wp/84MOmqpL+JWHtiBLEn63nbbaAP/01NnlwaNO/+DxWtc4PWOLfOEje7GYX1ehblkAboab
      EQAAm8XEPZtriKdyfPO5LoI+Bw0VG0NXXQtF1fjHn5xlbC7KH3/2AbY0lLGvrZqnTvRzfnCW
      HU0Vt121E0Jw4vIkTx3v579+8j7K/K/HbW9tLCOazPGdn19mV3MFPtftTVj2g1d6GJgK80cf
      P3zNewl6HTRVl/D1py9Q6nXqWqukMnn+8Sdn+ci9bWyuv9bF/h0lAAAmk8yOpgq8Ljtf/9kF
      CopKe12waJ0QYC6cZHIhTjKTI6+oIJaD89dbJVE1jW8/f5HTvTP8yWePUFHiBpZntXu21HC6
      b5qfnhxgb1sVbsfbkHPmBixEU/w//36Sj79nG/s311xzzSTLbGssYzGa5nsvd7OntQqP8/bM
      BENTYb72s/P8wS8fpK7Cd801SZKoCnqoKfXwlafOURn0UFd+42wjAK90jnFxeIEv/tIBTG+a
      ad/R5wMMTC7xtz/swG418xsf3KNbL5yPpHjytV6OX5rEabegqBq5gkK+oJJXVGwWMw6bmXK/
      i/fs28R92+tu+uNrmuAHr/TwzOkhvvzrD9JwnRGroKg8/txFXukc4w8/doi9bVVvub4YTdM/
      EaJreJ7h6QgFVUVRNQrK8iahqr3+/+aaEj76wBZ2tVRcM92/kXS2wJ9981Wqy7z8L7+4/4Yq
      mKppPP7cRY5fmuT/+o2HqF4xZyuqRiyZZWYpwdhcjP6JEBPzMfKKiqqK5QFlpf1X/l9d6uFX
      HtzKgc3VN7TkxFM5/uRrL3Hvjjo+8Z7tq77b588M841nOvmvn7r/agy5omrEUllmQgkm5uMM
      TIa4OLLApx/dyXv3bXrLPd7RAgDLL+wnx/t54cwwB7bU8KlHdhDwOq7720yuwLFLk3zvpW4q
      Ai4+/9huKoNuVE2gqtrVDpXNK2RyBUbnovzs5CAIeOxQC4/sa8L55sRIq6BpgqeO9/P0qUH+
      8GOHVhVQVdP42YlBfvBaD596ZCe7WyoYmArTOTjH8EyYWCqH32Vne1MZ2zaVYzHJmE0y5pVE
      A2aThMVsQgjB2f5ZfnpygObqEj7zvp00VZdc81whBE+8cIkz/TP8+W8+vKZwL69dznF5dIH3
      7mtiJhRneDpCOldACEF9hZ9NVX5aawNYzSZMsnxV8EyydLWzXxye5yfH+ynzO/nke3e8Ze2j
      aYJ//MkZphcT/OmvP7hmSKymCf7jRD8/PTnIo/ubmA0lGJ2LkkznkWSJqqCb1tog7XVB9rZV
      XVfI3/ECcIXxuSjfeLaL0dkIH3toKx842HJVLRJCMDgV5itPnSOazPL5D+7m3m11ukyQ6VyB
      VzvHeepYPxqCDx9u45F9m9bU1zVN8ErnGP/6TCf/63VG9Rtxpm+af3jyDNm8QqnPye6WSrZt
      KmdTlZ+KEpdu9WwunORnJwd46fwYR3Y18Iv3t1MZWFa9TvdO848/Psv//qn7aK/X9x1UVeNb
      z11kajHGpqoSmmtKqC3zURlw3XCWuR6RRIafnRzk+TPDbNtUzifeu52GCh9CCI5fmuSff3qe
      P/vNh6l/k+qzGj851s+lkXnqyn201QaoLvNQFfDo2i+4awQAlkfRU93TPPHiRRw2C595307q
      yrw8/vxFzvTN8Av3tfGhw203pWcXFJWXL4zxTMcQyXSeD9/byu7WSqQbREqPzkb4xrNd/M4v
      7OPglhrdHVcIQSpbQFE1XHZLUZ3reveamI/xr890MjgV5hfubWNfezV/+Z3j/Kcjm3nsYHEb
      i0IIBNxyCnshBKFYmh++2svJ7in2tlXx0J5G/uf3TvKpR3bw6IHmou93s/W6qwTgCol0jieP
      9vHiuRHMskxLTYBPvHc7m6r8t7zAzeQKXBic48mjfUwtxm+YKMBhs/C5D+ziyK6GW3reelBQ
      NHrHF/nms13MhhPsb6/m93/54B0zvV5BCMHIbIQnXrjE5ZEFDm+r4/c+euCWhFDvdi4AAB08
      SURBVL5Y1l0ACtkEQ6MzNDS3YFNTZE1e7FKGREbgX7Hb365D8ubCSZbiGbbUl677jqsmBPnC
      jX1TzCs6+kZCUTX6J5dorPThst8+a9NaaEIwE0pQ7nfdtJvDzbK+AiBUTh8/SWNbE/2jC+yt
      hhnnTrIjp6jcvI8yz/Jiyzgl0mCjsL5DlJJCdldQXl6N16SiIDh//EUIbLra+Q0MNhLr6wwn
      m1ALOUCQV1VkTGzbtZvY7CxqUxWS0BBCUCgUyGaz1xQVQnBlMpIk6S26+q1eB9BWAmuM63f2
      OoB8nY3LO3F9fQXA5CRgS/Pqa6/iq2jGZIrhdJdTKUfpH59jS0PF1YebTG/V9d6ojV3vBd7q
      9Te+FOO6cR3uUiuQgYFerpkB0un0bXloPp+/bc8yMFiNjWWng6sRYzditWtrXdc07Ro9cD3v
      fSXa7WbKCiE2bL3u9m9xzQxwsx6axbKaN+iVznC9NQIsV/5G19a6fuWFX2+BdKv3VlUVWb6x
      J+lqZa8s4Ddive72b7HhZgBJkjCZTAhNZXZ8kI6ODjo6TjM2E0IIsepLAa57PZsMMTEdRpbl
      G77wG5WFDIP9k6tcf73sW1+4xkj/EMoaZSVJum69ZvoHSa1R9kb1SizNMhtK3KBeq5eFFIP9
      01e/RXFlFYb6R9DWqPeNvsVk/yCZNcre6NmR6UlCyWxRbd6wJ1CkI7NMhFXuuecgCIWzR08S
      KL8Xr65t8gJnj59AMS3vPWSTEbw1O6mvWaMYAIKBi+cIpQorviUZYpkSWtvrdNV7caKP3okw
      VrMMaCwspKltb9FVNhOb52zXABbrsqPd3PAMD7a3svrxglfIc+rVY2BbnlmTSyGqdxxGnwue
      Rvf50yTyV/5OkSpU0dqu64UxM3yZ4bkEFpMEKCyENZram3SVTYYmOd87cXUHeG58gfe2t+oq
      qxZSdJw4g2xbDtaJzMyz+cH3UurWVZqujlMbVwCsdieF1ATDIyNImkJaMmPTHfRiwed1E2ze
      ScBpIRmeYGxBryuERInPham8huZKL5Cg46T+HKW+YBmeuJnd21uQ0Dh/8pzusna3H7vTw779
      u5GBftFRxKEkVrw+L3Vb9uCxmViaHCSsu6xMwOPAE2ynPmAHonScnNdd2hcI4Fd97GirAwqc
      OXlJd1lXSRC7I8L+/TuRgR7Rob/WZjtOt4fNu/ZgN8vM9Hej6H5hJvxe58Y2gxZyaaLxFEgS
      VpMZp8+HRadPj6YqaCz74xRyKVJZCb/eA7iFRl7RsFrMQIHQYorSMp2hl0JQUBTMFgsSgvDi
      Ev6yUt26ZiGfx2y1IgHxxRD2slL0eu2oigKyjEmWyaXi5LDpjucVQqWgsDIS5wktZild5Vzn
      NxUmryhYLRZAI7QYJVgW0C28b2xzdDGEq6wUvVEXSqGAbDYjSxLpWBRhd+HSGVoqtDXSo99J
      NLVAKpW9qq+NdHZRc/99lMt6qqyRjCdQVmQ7HZ0hnCvRKQCCdDJJtqCs/J2ity/KAzoFIJ9N
      k8zkrt6r53I/9zysrxOrSo5kMnX1MMHhzsu0PPqQTgFQScTjXLGrRGbGULwNOgVAkE4kyV1N
      PBWnrz/D/ToFIJdJkcpe0Z8UerpHuf+hg7oEQMlnSSbTV9s82NXN1kce1CUAQlNIJhJX2zw/
      PIizaZtOAdBIJhIbVwDUfILXjp2kfUV/jsZT6NNIAQRnT7xKcNNm7CaJTDwKbv05boYunibl
      qCDgsgJpCpp+W0F8foSOoTgtdaWAIJ3Tf6KIko7w2vEuNrc1AhBLFrdX0nH0FWratmKRJeKR
      OB6dAzgIus8cR65owmORgCQq+i2CixO99C6IlSAWhczVwWNtCskFXj05wJaW5TVWIq0/DabQ
      8pw4epTG9s2YJAhH41w/FvD6XOw4CuIOsLi4qONXmpifmRGFlb9CE5Mioai6n5GMLIpYKieE
      ECKbiojZ+Zjusko2JuZCiZW/smJ8bE53WU0tiNnZeaEt/yWmxiaEor+0mJuZufr7hbFxkdZd
      VohoaF4ks8tvLBVZFIuRlO6yhUxYzIevPC0txscWdJdVC1kxOx9a+UsRE2NTK+3Xw3Kbr3zZ
      ubFxkS2ibGh+VmQLy6Vj87MiktJfOptYEht2BhCqQjQ8x8jEBJIQOAOVbC/Cpz8WCTE+P4xJ
      Esg2Nzu2b9NdNhWPMzYyxfiQhCaZaN+2U3dZJZdiemKE8fExBBo1jZvR6+Eu1DxLizOMTkwi
      CRVPWR1lup8MsfACM4OjSJLA4vSzfWu77rKJaJyRsX7GJAkhm9m6c7fusvlMkqmxQcZHhxFA
      Q8sW3fq/ULLMz04xNjEJQqWkahN6j0gXQhALLzA4Mo6EwO4tZVup/gPW49HIxp0B4gtj4vzl
      UaFpQmiaKjqPHReRgt6xNCWOvXpGKKomhKaJxYkecXlY74imiNPHjot0XhVC00Q+OSuOn+7X
      WVaIwTMnxHQiJzRteWQ8fvTU1VlsLZbG+8Sl4dnlNquKOPPqUZHU/eSEOPrqOaEuvzAx0dcp
      hmeiOsvmxfHXToiCstzmXHxCnDg7ovvJPR3HxUIqL4QQQsklxLHj54TeuXq2r1P0z0autrnj
      1WO6Z71celGc6rgsVE0TmqaJgQtnxLTuWS8rjr16auPOAA5PgEzfeTo65pFQyWJjq24zqB23
      LcOpjg4sMmRzBbbu1RtnKlMecHD29ClsZhP5fJaq5l26611eW8G586eZtFnR1DyOQI3uGcAT
      LCNy7hIdixOgFchbPUXotA4cphinTnVgkiGX19hVr28HAcyU+cyc6ujAapLJ5zLUbzmg+8ll
      VaV0n+vAbrWiKjm85Zt0zwAllZX0d14kPG4HrYDm9KM3csRs8SDlL9PRcRoZjawic0B3+hoL
      Abe2gc2gQmNxbhph81MW8JBNJDC53Fh1qkGJyAKRjERddRlaIUu2IOHS+XLymTgzCzFqamux
      mFTi8Rxer77OpCl5pianCVbX4bKZSMSTuLwefWZQoTE3M4XZHSToc5GJx7F6vbotFbHQHAnV
      Qk1FECWXQcGsO9tcLhVlZilFXW01ZlklHs/j1Zl6Ui1kmJyao6K2AYcFEvEUbq9HnxAIlZnJ
      SRwlFZR4HKTicWx62ywE4cVZcpKTyjI/+XQKYbFj1xlWmY4vbTxXiCukIrMMTUWYH+1hNpJm
      preP6BoOUq+TpfPCAGpyhp7hOTLxBUbGQzrLalw8fxFEjgsX+xBk6Lk0qrveE90XSEpmLl84
      T0ZRGbjUg16bSGx2lIlQism+iywl80xc6iGh+8lpOruGyCxNMDgZIj4/yeR8XGdZhfPnLmHW
      knR1jyBI0HNpQveThy92kjeZ6Tp3nryq0HNpAL2jami0j9mUytDlC8RyKqOXetBrByrkInT3
      TxGbHWJ8LsbSxAjzcb2l81w437NxBUACbE4P23ftYrK3k0gqv2aZN5Y2WW00tu5Eio0ysRAt
      7uGyicraJuq8KpcGpyjmgCZZkvAEKtjWWktnZzdKEYUlwO4uYdfenQx0nSWRLeZQZgmzzUHL
      5p1k5gaYjRR3uKCQLdQ0tlNmSdA7Oqe7AwNIsowvWMXmxlI6L/ejFqFUyJLA6Q2yd+92Lp05
      Qzqv34QKElaHi/ZtO1kau8xiPLd2kTeUlczWjZsb1Gx3Y5EELo+Xqsoy4vEk5VUVWHSlNTHj
      dduQrE4qq6rJxKJ4y8t15gOSKPG7UbAQKKvEkk9hcvkI+HU5mOAOBlGzefwlQUo9ZpKKTHlZ
      QNdIY3X7MGkKbo+PqooAsWSGyqoKnSqQBY/Litnuoqq6mmQ0QkmF3jTyMkGfg4Jsp7S8AlIJ
      bCVBSnSqQN5ACWpepSRYhs8GWayUl/p1qUB2fwDyueU2l/uJpwtUVJbpWjfJJjsOm4zN6aaq
      uopENEppZQV2Xf5iJkq89g28BjAwuA1sWBUIQGhp+vv166LXojIxNMDcUpybEfHQzDijk3No
      N1E2FV1kYGiMXDH6zwpqIUVfTz+JTDEq3xUURgf6WYgkb6LNgvnJEcZnFm+qzYmlOQZHJyio
      xbc5n43T1ztAOleM+nO1NEO9fSzFbybCUNu4KtAygp6zJ4nmIZ1I4fJ5MenO7CbhcDqJh6bo
      utxDPKPg9/sx67QiWe0ORC5BV2cnc+EkHp8Pm0WfMmK22DBLCgPdFxmemsfu8uBy6LNASbIZ
      p8PC5FAfPQOjSFYnXrdDZ0Y7CYfDQXR+gq7LvaQL4PN7Melss83hpJCKcLGri8VYFq/Pi1Vn
      ljaLzYZJy9N76SJjsyGcHi9Omz4vJlk247CbGe3vpm94Atnmwuuy62yzjMNpJzQzSlf3AHlh
      wuv16G7zhp4BQKZt+3acNguyLFFccjfB9PgIc5EMtQ3N1JWa6B+c0106ujjDyMQcgcp6WjdV
      MNA3rHthmEtFGBoZx+IJsrmthamBfvQuzzQ1x/DAIFlstLZvJrcwSTitdzbQmBodYiGep6Gp
      lXJ3nuFRvdYvWJqbZGImRGlVA811fvr7x3WXzcRCDI5M4CgpZ3NbE2O9/ehdwiuFNEODQyhm
      F23t7cTGh0mqet+2yvjwIOGURlNLG24pwcSsfqPHhl4DCC3Day+/jLDUUmrP0LB3Px7deSML
      9HSPsnVb203VcbS3h4q2LThNxadUXBwbRJQ2UO4uPv1gPrPE+Eye1mZ9oSzXkqOne5Kt2/QF
      4FyLYKi3l7r2LdhuIo3kzEAPjk1bKLEUXzYVnWMxaaGxNlh0WUjT0z3H1m36AnCuRd3gKpDI
      M78YoZDVKBQyVDc06t4IA4mFsW7GQlm0QhaTzaF7OgfIJea40DOBWdZQMeMoIpemTI5TJ89j
      sprJ5VWcLkcRgS1w6WwHedlKJpXF5XUXMU1LTA10MRNXUHNZLA4nliLykyaXJrk8NI8JBSFb
      sRdxZJMk0pw82YXZYiKnCFxOu+42S6icP9UBVhuZdA6Xx1VUm0e6z7GUFRSyGewuF2bdHgPS
      xnWHBpBkJ1vb25iYXqCkoh1nkclmqxrbV+zKN0pifmPcwWrarMubKsVmlDbZvOzY1oZYKVtM
      aUk2s3n7DtSbKAtQ17KVrKJyM232V9TT7F5W1opts9lRwvatK4HmRZaVTTa27dyOdhPPBYlN
      7dvJaxo3o9FvaAEQQuFy10WE1clSYpSK4O6iFsHR0DzToThWiwmLN4iriNNdcskow/2TOHwO
      /MKm2yYOQCHN2NgYJqsNi81LWWmJ/s4oNBbmJ4ilwGS2UV5ToduXCCTCizPMRTJYzSacJaUU
      cxRCOhZmZHQOu8tCqdmDz13EwXj5BCPDo1idNuzuMkoD+g+4QFOYm5sglZGwWB2UVxXjAyux
      tDDJQlzBajbhLSvDVkSv3rCLYCEEqpIEawUHDx6kTM4TK8rElmcunMdpMtPYUEp4qbid0enx
      OTx2K41bmogsxooqOzc6gae0BG+wBi0TL2onOZ8Jo5qcmGUvbqmgeyG5TJZQTMNmslJX6SWW
      yK5d5A3MTC/htlpo3tpIaK643fOp/nH8ZSWU1TSQi8eK2klORhewuL1YzCXYtKxu15FlUkSS
      MhbZTpXPTqqo3XN14wpAIRvn4oVBMrkQ586dYz4ncBa1OLNSUVlCdY2fweFFSkqKy3lU3VhD
      dVMtfWd78ZcWdxRrRUMNXk8pyYVRZJe/qJdstQfw+4N4nXHiwqo7HngZBxWVAarKHYzMxIsb
      wYGqukqqG6voOTdEsMjjZ2taa/H5y1gYHcTu1bcLfAWXrwy3K4jDHCJjcuqOB17GTXllgIqA
      zGQ0h7uIWR5MG1cFsjp87Dmwj96uC6QVcDvtFKfUCkq8btJ5hbpaBy57cU21Oz1EkxkaG+tx
      eIvrSJLNjSUZo7quHkzFLYCRZRxmCWt5Pe6i94U0fG4nWcVGXY0Xu7W48c3l8RBP5WhoqMNd
      pAVLsvswZRLU1tdjsulzG7la1mTBJqkEqxrxFxF+uoyG1+kgb7dR59BWUrPoZ8POAABCSxJL
      29i/fz/79+/EXtRZwBIOtwefz4eWXmQ2rD/WFMDqcOLz+XCb84xMLRVV1mSx4fX58HpcjI7o
      3z8AkCQTHp8Pn8/L1NhoUeoTyLg8y23ORudYShS3m2xzuPD5fDilFMPTxal9Zqsdn8+H12Vl
      ZFR/GhkAyWTGu9LmidGRItss4fZ68fl8RBemSGSLGzU27AwAIEl2IvMDnDiRAGzsObQHh24h
      EKQTcdJ5hUxBYNH0ulIvk8ukiCYyqLkcslrcpKzkc0SjUYRQkDAVJQBCqMSjUVQEQoXiaq2R
      jMfJKipZRcO5Su7N65FNJ4mncig5BVktriMpuSzRaByh5oq2P2lqgVg0ioYATSpSAASJWJS8
      JigoYtWcoNdjQwuAoEBJRTP7DmxDAsxFmcgkVCXH/Ow8mmSh1V9MvgCQhEZoYY68Iqhu1JcV
      7gqyDPFwiGQ2T2llTZHdQSKbirEYS+EKlhX5gSSUQoa5uRDIDvzFbsRpKovzsxQ0qG8uworD
      cpsjoQUyeYXyyuIOBpQkiXQiQjiRwVtWXoTVC0CikEsxtxgFuwdXMSYgNrgASJIZWUlwsasL
      sLF97zbsuoWgQP/gFDv37ADAUdTiCEb7+ilt247XLGOxFSc8i8ODUFLNZp8D2WwtSgDymTCz
      EZXtWzcDcpGdIc/g8By79m4HJBxrHDT9Zob7B6neshOHScJqL27dM9PTg61+Mw02GZOluOOw
      0uF5Ijkrmzc3IEmmIvXyLMNjS+zetw2QsJmLKa1tbAEAG21bmkgpNhxWCWtRM4AJWcswOz+P
      jERFrb2onWCny8z0xAwZlwVvsBJnETvBnhIH3cPTWCo82Jw+XEXsBJstdtKJRebmHciSFadP
      ZzjlcmlQUittlqm2OYraCbZZYXpqFo/dRElZte5wSgBfqYNzQ5NIpXbsngAup34BsjrsxJbG
      mXfJyGYHzqJ2vy2ohfhKm03UOl3olwFpY/sCaVqSE8fOgFRK0Jahas8+/LqP0RQkY7Gr2c5c
      Xh92nd6cANlU4mq2M5vDjVt3sPVytrNYYnnfwWSx4fO6dQuApinEIrEVPdiEP+gvYhbQiEdj
      Ky7JEh6frwihF2SSiauJvOwuT1FHqRayaeKpZUOD2erA63Hqb7OaJxpNLO+cy2b8Jb4iBEAj
      FomiaAKQ8Pr9RQi92NgzgIQFm0UwNTNJ2utkU1H7AIKp0SFml+LYbWYatt9DtV9/c2MLU1zq
      n8ITdOEpa2FrY7nusoXkEpcuDWK2ObBY3RzYt01/nhw1z/DQZWJJGbPFwr1HDhWlBk0O9zMX
      zeKwmmjbe5DSIma90Ow4AyPzuHw2gvU7aK3WLwD5+AKdF0dxeJxYnQH27dCX4RlAy2cYGrxM
      Mm3GYrNx+L79RQiAYGyoh1BcxWYxsePgIXxFzHob2gyKZKaqtoWH3nOYze3NOsMhr1BgKSnw
      Oew0NVURWdIfXg6wMBch6HPRsKWN2GJxu6KhqTkCleWUlNUi5VJFWTUK2RiSzY/D5sNvFhRn
      yMwSzZhw2x001ASJ6w4QX2ZxMUHA46RlewtLs5Giys6NzlBWU05ZbQNKKlmU5SsVD2P3l+Jw
      lNzE7neGZM6Gw+aiNugmVVQgkbaxBUBoCSYm4pSUlJCcmSnSFcJKfX0FdQ0VjIyHqazQnSgT
      gLrmBupaNjHS2U9lbTG+KVC5qYFAoIJ8dApHsDirhtURpLysglJfnozVQ3FLUQd19RXU1/iZ
      WEgT9Be3+13XVE9dSz0DnSPUNBTX5trNTQSDlcSmRvGWlRe18Hf7ywn4y/E7EihF5AVaxkVd
      fQV1lQ5mkhq+IlRVkDe4CiT78Dn76OjoQNjcbNa9uslz+WwXyRWvSIfHpzMgHkBjvL+H2UgS
      AIvDRcCnf2dzaWaUwYnl3PqyyURFMKi7M2QSIS53D614gsoEKkuLEJ4cnacusOz9I+HyB3Hq
      tgKpDHdfZjGxPGNYnW4CXv2Wr/mJQUZnljcLZZOV0qB+V4hUZI7u/rEVT1CZslr9qeRVJUXX
      2Usrs6SEL1iBXXcfUejvurixF8FCaAz1XiCpleB3Cuoam3SGNAo0Vbs6DS+O97BEFdua9ATi
      CzTt9Q2VfHKW8wNp7jugL7BGaBraSllNydLRcYlDRw7pGmnEGw7LE0Ll3NFTbH/4iM4TYgTq
      1RlSMNF3ES3QTHOVHnv+tW3OxcbpGpM5vLdR35Pf0GY1l6DjwjD33bdPV0e+ps2awtnjp9n1
      0AO6MuK9sSwIBjrP4WveSbWuPZ/lsht6BhBanKWYAlqaXCpFrK6RoKxnTJSQ33AOVElFLRZF
      rzIhIb9ByOzuIE26Uwwu58i58mRZdtDS2qh7RLv2TC4TTe2tRTjDXXueV3ltA4r55trs8FbQ
      WKt/J/iaNtvdtDTX6Q+GeWObTSaa2lp0O8O9+Qyz6sYmzA7dpZFl08afAfounqJ/OIy3vIIH
      792vO9j5mvtoGpoAU5EBNWLl9EZJlosOLnlno6EoAnMRFqSrJTXtHfW+NvgMoFFV10Z5vRW/
      T3+k/5tVoHRshumwlc0telJnC0Z6LjCxmMbvcRCPx6lo3UV7TUDXk6f6Bwi0tzJ0+ijRrIav
      opmd7fpGxKXJAS4MztO6ZSu1FYEihV0w0HWGuWiacDxFTU0Du3dv03mk1LI68PpQGOdiZ5i9
      +/UlFA5N9nNxcIaSQIBkLIqrooE9mxt1tTk0PopcXkNkuIupSAaru5xDezbrKpvPLPDSi51s
      2rGd5rpKzEUNcILpoe6NawXSCmlOHj/GzGKE0Ow4xzouFJFyT+WVZ5/mzLlznDt3jgudPaR1
      n1qishQtcN/hncwupTlyZC9LE/qzScRDYQokSeR8HHngAbKL07oDPJScQtPW7ZizIc6dPcvA
      6HQRJtQkC3ErR47cS1VpFVVewcScXo9OwelXXqRj5X2dO9dJNKM/zeDc1BL7H3yI2PwM++47
      RD68pNsMmo5GySgZ5iLw4ANHsKeW0JvhR2gK5XXtlNrynD97lu7+0SLyEuUYnc1s3BkgGZ7H
      V9nK1rblg5HE6dPEFI2Arp1gM9t2bMZU0kCpx0o6Nst0WO90bsJf4kaVHdyzZwe5eAJ3mb7R
      HyBYXUL/mUs4SmrQlCwml0f3SzZZrFhMJmo2tVPVoBINhVFA5zrAhdMc5cyZ85g8ZVjtEjaX
      3jWAzI4d7SyKMhrLXSzPAPpdwEurAuQyCrv23YNFLWDzuHSrQL6qIH09vUhOL2gFVLtTf0p4
      yYzNKghW1RCorCcRjlDQNJ07wTaCng2cHj25NMmpc0O4PMsfMRFKcOCx91JyE3rpOwUhll0Y
      ig8Mv1JeoGoa5jUOmV6bDJMTSerqi9sLWEZlamKOmvpivWCXmZ+YxF9fp2svYHmNxtUFfHxx
      Hs3lx69zL0CIDewK4Q7W8cj7inNDfqcjSbemkUqSdJOdX2NssJ9U/or6kGQx7NUtAEszo0yH
      0ywHYylMTiep1ikAmdgig+NzVw0UU4MT3KtXANQcvd19YF62/CxNTlF/4D6dAqAy0t+7cQUA
      QCnkSGdyXElzaLmLR/87i0QhsYS5YgtVHgsQIxbVn0RARiGjmtmyqQooEFka0V3WYjOzFE2z
      d/fyuWKZBf3He0uyiWgsxuYdu7GYJKy5dBGzjkwuEdm4KlAhHaXj/GVKAkEkobCwlOLe+w8W
      kRjLoBiEKJDOCFxOK1AgFivgK+Jg8VQmh8vpADTisRQen84TYoBMKoXNtZwMKxWLYfP5dI/M
      uUwak82OWZbJpZIIq0P3CTGamt+4M0AuncDmqWDrllYQKtnjJ8loGlZdG2EGxSJJFlxX+7sF
      n6+IACJJXun8ADJen6eoZztcr280unzFRaLZHK8Lqc1VXDC+bLJuXAFwBaqpiA3QcboDCQg2
      tOItciPLwGAtNmyPEmqehYUlnN4SrJLG7OxskcHSBgZrs2EFIBmewxbYhDM1g6minTKRJXoT
      B04YGKzGhhUAm8ONoEDlpm00lLlICa3IzHAGBmvz/wOCGrSW50f8bwAAAABJRU5ErkJggg==
    </thumbnail>
  </thumbnails>
</workbook>
