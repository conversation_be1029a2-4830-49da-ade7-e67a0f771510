from pydantic import BaseModel, Field
from typing import Optional, List
from app.core.enums import RoleEnum


class AddRoleRequest(BaseModel):
    name: RoleEnum = Field(..., description="Role name (ADMIN, MANAGER, DEVELOPER)")


class RoleResponse(BaseModel):
    id: str
    name: str


class GetRoleResponse(BaseModel):
    data: Optional[RoleResponse] = None
    error: Optional[str] = None


class RoleListResponse(BaseModel):
    data: Optional[List[RoleResponse]] = None
    error: Optional[str] = None 