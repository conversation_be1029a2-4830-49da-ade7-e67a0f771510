import logging
from typing import Dict, Any
from xml.etree.ElementTree import Element
from app.core.enums import ChartType, GeneralKeys as GS, WorkSheet as WS
from app.core.regex_enums import Regex as RE

# Configure logger
logger = logging.getLogger(__name__)

class Map:
    @staticmethod
    def check_map(worksheet: Element) -> Dict[str, Any]:
        try:
            # Retrieve map sources
            mapsources = worksheet.find(WS.MAP_SOURCES.value)
            if mapsources is None:
                logger.warning("No map sources found.")
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            # Retrieve rows and columns
            rows = worksheet.find(WS.ROWS.value)
            cols = worksheet.find(WS.COLS.value)

            if rows is None or cols is None:
                logger.warning("Rows or columns not found.")
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            # Combine rows and columns text
            combined_text = (rows.text or RE.EMPTY.value) + (cols.text or RE.EMPTY.value)

            # Check for latitude and longitude in combined text
            if WS.LATITUDE.value not in combined_text or WS.LONGITUDE.value not in combined_text:
                logger.warning("Latitude or longitude not found in combined text.")
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            # Retrieve panes and check for mark class
            panes = worksheet.findall(WS.PANES_PANE.value)
            if not panes:
                logger.warning("No panes found.")
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            mark_class = panes[0].find(WS.MARK.value).get(WS.CLASS.value, None)
            if not mark_class:
                logger.error("Mark class not found in pane.")
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            # Determine chart type based on mark class and encodings
            if mark_class == WS.AUTOMATIC.value:
                encodings = panes[0].find(WS.ENCODINGS.value)
                if encodings is not None:
                    size_tag = encodings.find(WS.SIZE.value)
                    chart_type = ChartType.SYMBOL_MAP.value if size_tag is not None else ChartType.FILLED_MAP.value
                else:
                    chart_type = ChartType.FILLED_MAP.value
            else:
                chart_type = ChartType.FILLED_MAP.value

            return {GS.STATUS.value: True, GS.CHART_TYPE.value: chart_type}

        except Exception as e:
            logger.exception("Unexpected error in check_map:")
            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None,
                GS.ERROR.value: str(e)
            }
