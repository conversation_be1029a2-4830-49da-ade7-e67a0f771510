"""add organization_id to tableau_server_details

Revision ID: 3c8039e8842a
Revises: 0c76e4c4ef02
Create Date: 2025-07-10 14:14:57.445919

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3c8039e8842a'
down_revision: Union[str, None] = '0c76e4c4ef02'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tableau_server_details', sa.Column('organization_id', sa.UUID(), nullable=False), schema='biport_dev')
    op.create_foreign_key(None, 'tableau_server_details', 'organization_details', ['organization_id'], ['id'], source_schema='biport_dev', referent_schema='biport_dev')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'tableau_server_details', schema='biport_dev', type_='foreignkey')
    op.drop_column('tableau_server_details', 'organization_id', schema='biport_dev')
    

    # ### end Alembic commands ###
