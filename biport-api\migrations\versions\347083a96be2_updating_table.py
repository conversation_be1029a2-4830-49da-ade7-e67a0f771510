"""updating table

Revision ID: 347083a96be2
Revises: 50ad1fba00e5
Create Date: 2025-06-17 16:12:48.860257

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '347083a96be2'
down_revision: Union[str, None] = '50ad1fba00e5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.add_column('server_report_details', sa.Column('dashboard_count', sa.Integer(), nullable=False,server_default='0'))
    op.add_column('server_report_details', sa.Column('worksheet_count', sa.Integer(), nullable=False,server_default='0'))
    op.add_column('server_report_details', sa.Column('calc_count', sa.Integer(), nullable=False,server_default='0'))
    op.add_column('server_report_details', sa.Column('datasource_count', sa.Integer(), nullable=False,server_default='0'))

    op.add_column('upload_files_report_details', sa.Column('dashboard_count', sa.Integer(), nullable=False,server_default='0'))
    op.add_column('upload_files_report_details', sa.Column('worksheet_count', sa.Integer(), nullable=False,server_default='0'))
    op.add_column('upload_files_report_details', sa.Column('calc_count', sa.Integer(), nullable=False,server_default='0'))
    op.add_column('upload_files_report_details', sa.Column('datasource_count', sa.Integer(), nullable=False,server_default='0'))


    op.create_index(op.f('ix_upload_files_report_details_id'), 'upload_files_report_details', ['id'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'password',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1),
               existing_nullable=False,
               autoincrement=True)
    op.drop_index(op.f('ix_upload_files_report_details_id'), table_name='upload_files_report_details')
    op.alter_column('upload_files_report_details', 'is_migrated',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('upload_files_report_details', 'is_converted',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('upload_files_report_details', 'is_analyzed',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_column('upload_files_report_details', 'datasource_count')
    op.drop_column('upload_files_report_details', 'calc_count')
    op.drop_column('upload_files_report_details', 'worksheet_count')
    op.drop_column('upload_files_report_details', 'dashboard_count')
    op.drop_index(op.f('ix_server_report_details_id'), table_name='server_report_details')
    op.create_index('ix_report_details_id', 'server_report_details', ['id'], unique=False)
    op.drop_column('server_report_details', 'datasource_count')
    op.drop_column('server_report_details', 'calc_count')
    op.drop_column('server_report_details', 'worksheet_count')
    op.drop_column('server_report_details', 'dashboard_count')
    op.alter_column('folders', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('files', 'folder_id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    # ### end Alembic commands ###
