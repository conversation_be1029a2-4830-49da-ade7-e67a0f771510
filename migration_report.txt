
# MIGRATION API COMPREHENSIVE REPORT
# =====================================

class MigarationInput(BaseModel):
    """
    Pydantic model to create request object for tableau to powerbi migration.
    """
    
    s3_input_path: str
    local_download_path: str
    logger_id: str = str(uuid.uuid4()).replace("-","")[:20]
    report_type:str = MIGRATE_REPORT_TYPE
    twb_files_count: int
    twb_files: List
    process_id: str
    powerbi_structure: str

## API ENDPOINT: /app_api/migrate/tableau-to-powerbi
## METHOD: POST
## PURPOSE: Migrate Tableau workbooks (.twb files) to Power BI format (.pbip)

---

## FUNCTION FLOW ANALYSIS

### 1. API ENTRY POINT
**Function:** `tableau_to_powerbi_api`
**File:** `biport-api/app/api/migrate.py`
**Dependent Functions:** MigrateProcessor.tableau_to_powerbi()
**Description:** Main API endpoint that receives migration requests and orchestrates the entire migration process
**Input:** 
- MigarationInput (Pydantic model containing migration parameters)
- User authentication via get_current_user dependency
**Output:** 
- JSONResponse with migration results including download links
- Status code and error handling
**Connecting to Next Function:** Calls MigrateProcessor.tableau_to_powerbi()

### 2. MIGRATION PROCESSOR
**Function:** `tableau_to_powerbi`
**File:** `biport-api/app/services/migrate/migration_processor.py`
**Dependent Functions:** MigrationService.tableau_to_powerbi()
**Description:** Processes the migration request and handles user report tracking
**Input:** 
- request: MigarationInput object
- user: User object from authentication
**Output:** 
- ServiceResponse object with success/failure status and data
**Connecting to Next Function:** 
- Calls MigrationService.tableau_to_powerbi()
- Calls add_or_update_userReports() for tracking

### 3. MIGRATION SERVICE
**Function:** `tableau_to_powerbi`
**File:** `biport-api/app/services/migrate/migration_service.py`
**Dependent Functions:** process_files_for_migration()
**Description:** Core service that handles the actual migration logic
**Input:** 
- request: MigarationInput containing twb_files, powerbi_structure, process_id, s3_input_path
- custom_zip_name: Optional custom name for output zip
**Output:** 
- ServiceResponse with download links for migrated files
**Connecting to Next Function:** Calls process_files_for_migration()

### 4. FILE PROCESSING ORCHESTRATOR
**Function:** `process_files_for_migration`
**File:** `biport-api/app/services/migrate/process_files.py`
**Dependent Functions:** 
- prepare_powerbi_structure()
- process_single_twb_file()
**Description:** Main orchestrator that processes multiple TWB files for migration
**Input:** 
- twb_files: List of TWB file paths
- powerbi_structure: S3 path to Power BI template structure
- process_id: Unique identifier for the process
- s3_input_path: S3 path for input files
- custom_zip_name: Optional custom zip name
**Output:** 
- List of download links for each migrated file
**Connecting to Next Function:** 
- Calls prepare_powerbi_structure() for setup
- Calls process_single_twb_file() for each TWB file

### 5. POWER BI STRUCTURE PREPARATION
**Function:** `prepare_powerbi_structure`
**File:** `biport-api/app/services/migrate/process_files.py`
**Dependent Functions:** 
- S3Config.check_file_exists()
- S3Config.download_file()
- validate_powerbi_structure()
**Description:** Downloads and validates Power BI template structure from S3
**Input:** 
- powerbi_structure: S3 path to Power BI template zip
- local_dir: Local directory for extraction
- logger_id: Process identifier for logging
**Output:** 
- Local path to validated Power BI structure folder
**Connecting to Next Function:** Used by process_single_twb_file()

### 6. POWER BI STRUCTURE VALIDATION
**Function:** `validate_powerbi_structure`
**File:** `biport-api/app/services/migrate/process_files.py`
**Dependent Functions:** None (utility function)
**Description:** Validates that Power BI structure contains required components
**Input:** 
- base_dir: Path to extracted Power BI structure
**Output:** 
- Dictionary with validation status and missing components (if any)
**Connecting to Next Function:** Used by prepare_powerbi_structure()

### 7. SINGLE TWB FILE PROCESSING
**Function:** `process_single_twb_file`
**File:** `biport-api/app/services/migrate/process_files.py`
**Dependent Functions:** 
- get_chart_types()
- generate_report_sections()
- modify_pbi_reportfile()
- upload_and_generate_download_link()
**Description:** Processes individual TWB file and converts it to Power BI format
**Input:** 
- twb_file: Path to TWB file
- local_dir: Local working directory
- powerbi_folder_path: Path to Power BI template structure
- s3_input_path: S3 input path
- custom_zip_name: Optional custom zip name
**Output:** 
- Dictionary with file name and download URL
**Connecting to Next Function:** 
- Calls get_chart_types() for chart analysis
- Calls generate_report_sections() for Power BI conversion
- Calls modify_pbi_reportfile() for file modification
- Calls upload_and_generate_download_link() for S3 upload

### 8. CHART TYPES ANALYSIS
**Function:** `get_chart_types`
**File:** `biport-api/app/services/analysis/chart_types.py`
**Dependent Functions:** Various chart analysis functions
**Description:** Analyzes TWB file to identify chart types and visual elements
**Input:** 
- twb_file: Path to TWB file
**Output:** 
- Dictionary containing chart types and visual configurations
**Connecting to Next Function:** Used by generate_report_sections()

### 9. POWER BI REPORT GENERATION
**Function:** `generate_report_sections`
**File:** `biport-api/app/services/migrate/PowerBI/report/page_report.py`
**Dependent Functions:** 
- process_worksheets()
- process_worksheets_in_dashboards()
- process_text_boxes_in_dashboard()
- get_config_json()
- get_dashboard_visual_container_visuals()
**Description:** Converts Tableau worksheets and dashboards to Power BI report sections
**Input:** 
- root: XML root element from TWB file
- chart_types: Chart types analysis from get_chart_types()
**Output:** 
- List of Power BI report sections in JSON format
**Connecting to Next Function:** Used by modify_pbi_reportfile()

### 10. POWER BI FILE MODIFICATION
**Function:** `modify_pbi_reportfile`
**File:** `biport-api/app/services/migrate/process_files.py`
**Dependent Functions:** 
- get_report_filepath()
**Description:** Copies Power BI template and injects generated report sections
**Input:** 
- source_path: Path to Power BI template structure
- dest_path: Destination path for modified structure
- overall_json: Generated report sections JSON
**Output:** 
- Modified Power BI structure with Tableau content
**Connecting to Next Function:** Used by process_single_twb_file()

### 11. REPORT FILE PATH EXTRACTION
**Function:** `get_report_filepath`
**File:** `biport-api/app/services/migrate/process_files.py`
**Dependent Functions:** None (utility function)
**Description:** Finds and returns path to report.json file in Power BI structure
**Input:** 
- folder_path: Path to Power BI structure folder
**Output:** 
- Path to report.json file
**Connecting to Next Function:** Used by modify_pbi_reportfile()

### 12. S3 UPLOAD AND DOWNLOAD LINK GENERATION
**Function:** `upload_and_generate_download_link`
**File:** `biport-api/app/services/migrate/process_files.py`
**Dependent Functions:** 
- S3Config.upload_to_s3()
- S3Config.generate_presigned_url()
**Description:** Creates zip file, uploads to S3, and generates download link
**Input:** 
- powerbi_output_dir: Directory containing migrated Power BI files
- s3_input_path: S3 input path for organizing uploads
- custom_zip_name: Optional custom zip name
**Output:** 
- Dictionary with file name and presigned download URL
**Connecting to Next Function:** Returns to process_single_twb_file()

### 13. USER REPORTS TRACKING
**Function:** `add_or_update_userReports`
**File:** `biport-api/app/services/common.py`
**Dependent Functions:** UserReportsManager.add_or_update()
**Description:** Updates user migration statistics in database
**Input:** 
- organization_name: User's organization
- user_email: User's email
- migrated_files: Number of files migrated (default 0)
- dax_files: Number of DAX files processed (default 0)
- analyzed_files: Number of files analyzed (default 0)
**Output:** 
- Database update (no return value)
**Connecting to Next Function:** Used by MigrateProcessor

---

## DATA FLOW SUMMARY

1. **API Request** → MigarationInput validation
2. **Authentication** → User verification and organization check
3. **Migration Processing** → Core migration logic execution
4. **Power BI Setup** → Template download and validation
5. **TWB Analysis** → Chart types and visual element extraction
6. **Power BI Generation** → Report sections creation
7. **File Modification** → Power BI template customization
8. **S3 Upload** → Zip creation and cloud storage
9. **Download Links** → Presigned URL generation
10. **User Tracking** → Database statistics update
11. **Response** → JSON response with download links

---

## KEY COMPONENTS

### Input Schema (MigarationInput):
- s3_input_path: S3 path for input files
- local_download_path: Local path for downloads
- logger_id: Process tracking identifier
- report_type: Type of report (migration)
- twb_files_count: Number of TWB files
- twb_files: List of TWB file paths
- process_id: Unique process identifier
- powerbi_structure: S3 path to Power BI template

### Output Format:
- file: Name of generated zip file
- download_url: Presigned S3 URL for download
- status: Success/failure indication
- error: Error message (if applicable)

### Error Handling:
- BadRequestError: Invalid input or missing files
- ServerError: Processing failures
- Validation errors: Invalid Power BI structure
- S3 errors: Upload/download failures

---

## DEPENDENCIES

### External Services:
- AWS S3: File storage and retrieval
- Database: User tracking and statistics
- XML Parser: TWB file analysis
- JSON Processing: Power BI report generation

### Internal Modules:
- Chart Analysis: Visual element identification
- Power BI Converters: Format transformation
- S3 Configuration: Cloud storage management
- User Management: Authentication and tracking

---

## DETAILED VISUAL PROCESSING FUNCTIONS

### 14. WORKSHEET PROCESSING
**Function:** `process_worksheets`
**File:** `biport-api/app/services/migrate/Tableau_Analyzer/report/worksheet.py`
**Dependent Functions:** Various visual converter functions
**Description:** Processes individual Tableau worksheets and converts them to Power BI visuals
**Input:**
- root: XML root element from TWB file
- chart_types: Chart types analysis data
**Output:**
- List of processed worksheet data with visual configurations
**Connecting to Next Function:** Used by generate_report_sections()

### 15. DASHBOARD PROCESSING
**Function:** `process_worksheets_in_dashboards`
**File:** `biport-api/app/services/migrate/Tableau_Analyzer/report/dashboard.py`
**Dependent Functions:** Dashboard analysis functions
**Description:** Processes Tableau dashboards and extracts worksheet dimensions and layouts
**Input:**
- root: XML root element from TWB file
**Output:**
- Dictionary mapping dashboard names to worksheet dimension data
**Connecting to Next Function:** Used by generate_report_sections()

### 16. TEXT BOX PROCESSING
**Function:** `process_text_boxes_in_dashboard`
**File:** `biport-api/app/services/migrate/visuals/text_box.py`
**Dependent Functions:** Text box converter functions
**Description:** Processes text boxes within Tableau dashboards for Power BI conversion
**Input:**
- dashboard_elements: List of dashboard XML elements
**Output:**
- Dictionary mapping dashboard names to text box visual configurations
**Connecting to Next Function:** Used by generate_report_sections()

### 17. VISUAL CONFIGURATION GENERATION
**Function:** `get_config_json`
**File:** `biport-api/app/services/migrate/PowerBI/report/get_singlevisual_data.py`
**Dependent Functions:** UUID generation and visual mapping functions
**Description:** Generates Power BI visual configuration JSON from Tableau worksheet data
**Input:**
- visual_details: Processed worksheet/visual data from Tableau
**Output:**
- Power BI visual configuration in JSON format
**Connecting to Next Function:** Used by generate_report_sections()

### 18. DASHBOARD VISUAL CONTAINER GENERATION
**Function:** `get_dashboard_visual_container_visuals`
**File:** `biport-api/app/services/migrate/PowerBI/report/dashboard.py`
**Dependent Functions:** Visual container mapping functions
**Description:** Creates Power BI visual containers for dashboard layouts
**Input:**
- generated_visuals: Dictionary of generated Power BI visuals
- ws_dimensions_data: Worksheet dimension and layout data
**Output:**
- List of visual containers with proper positioning and configuration
**Connecting to Next Function:** Used by generate_report_sections()

---

## VISUAL TYPE CONVERTERS

The migration system includes specialized converters for various Tableau visual types:

### Chart Type Converters (in /visuals/ directory):
- **Bar Charts:** `bar.py`, `bar_or_column_chart.py`, `clustered_column_chart.py`, `column_chart.py`
- **Line Charts:** `line_chart.py`, `line_bar.py`, `line_stacked.py`
- **Area Charts:** `areachart.py`, `area_chart_discrete.py`
- **Pie Charts:** `pie_chart.py`
- **Maps:** `map.py`, `filled_map.py`
- **Tables:** `table.py`, `pivot_table.py`
- **Other Visuals:** `card.py`, `scatterplot.py`, `tree_map.py`, `slicer.py`
- **Interactive Elements:** `drill_throgh.py`, `page_navigation.py`, `tooltips_customized.py`

Each converter function:
- **Input:** Tableau visual XML data and chart type information
- **Output:** Power BI visual configuration JSON
- **Purpose:** Translates Tableau-specific visual properties to Power BI equivalents

---

## ERROR HANDLING AND VALIDATION

### Validation Points:
1. **Input Validation:** MigarationInput schema validation
2. **Authentication:** User and organization verification
3. **S3 File Existence:** Power BI structure and TWB files validation
4. **Power BI Structure:** Required components validation (.Report, .SemanticModel, .pbip)
5. **TWB File Parsing:** XML structure validation
6. **File Permissions:** S3 upload/download permissions

### Error Types:
- **BadRequestError (400):** Invalid input, missing files, validation failures
- **ServerError (500):** Processing failures, S3 errors, file system errors
- **ConflictError (409):** Duplicate resources or conflicts
- **NotFoundError (404):** Missing files or resources

### Recovery Mechanisms:
- Automatic cleanup of temporary directories
- Graceful error propagation with detailed messages
- User report tracking even on partial failures
- S3 presigned URL expiration handling

---

## PERFORMANCE CONSIDERATIONS

### Optimization Features:
- **Parallel Processing:** Multiple TWB files processed concurrently
- **Streaming:** Large file handling with streaming uploads/downloads
- **Caching:** Power BI template structure reuse
- **Cleanup:** Automatic temporary file removal
- **Compression:** Zip file creation for efficient transfer

### Resource Management:
- Local directory creation and cleanup
- S3 bandwidth optimization
- Memory-efficient XML parsing
- Temporary file lifecycle management

---

## SECURITY FEATURES

### Access Control:
- User authentication via JWT tokens
- Organization-based access control
- S3 presigned URLs with expiration
- Input sanitization and validation

### Data Protection:
- Secure file transfer via HTTPS
- Temporary file encryption (if configured)
- Access logging and audit trails
- User activity tracking
++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

## MAJOR ARCHITECTURAL CHANGES - NEW FLOW IMPLEMENTATION

### OVERVIEW OF CHANGES
The migration system has been restructured to use a report-based approach instead of the previous upload_file boolean logic. The new system focuses on individual report processing with proper status tracking and unified S3 path structure.

---

## NEW FLOW ARCHITECTURE

### 1. REPORT-BASED PROCESSING
**Previous Flow:** Used `is_upload_file` boolean to determine processing paths
**New Flow:** Uses `report_id` for individual report processing with migration status checking

### 2. MIGRATION STATUS CHECKING
**Function:** Check `is_migrated` status in `report_details` table
**Logic:**
- If `is_migrated = true` → Generate pre-signed URL for existing migrated file
- If `is_migrated = false` → Perform migration process and update status

### 3. UNIFIED S3 PATH STRUCTURE
**Previous Paths:** Different paths based on `is_upload_file` condition
**New Paths:** Unified structure using constants from lines 3-7 in constants.py:
- `S3_BASE_PATH = "BI-PortV3/{organization_name}/{s3_report_id}"`
- `REPORT_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/tableau_file"`
- `ANALYZED_OUTPUTS_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/analyzed_outputs"`
- `CONVERTED_OUTPUTS_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/converted_outputs"`
- `MIGRATE_OUTPUTS_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/migrate_outputs"`

---

## UPDATED INPUT SCHEMA

### NEW MigarationInput Schema:
```python
class MigarationInput(BaseModel):
    """
    Pydantic model for single report migration request.
    """
    report_id: UUID                    # NEW: Primary identifier for the report
    s3_input_path: str                # Path to input TWB file
    local_download_path: str          # Local working directory
    logger_id: str = str(uuid.uuid4()).replace("-","")[:20]
    report_type: str = MIGRATE_REPORT_TYPE
    twb_files: List                   # List of TWB files to process
    powerbi_structure: str            # S3 path to Power BI template
    # REMOVED: process_id (no longer needed)
    # REMOVED: twb_files_count (derived from twb_files list)
```

### Key Changes:
- **Added:** `report_id` as primary identifier
- **Removed:** `process_id` (no longer used)
- **Removed:** `twb_files_count` (can be derived from list length)
- **Simplified:** Focus on single report processing

---

## UPDATED FUNCTION MODIFICATIONS

### 1. API ENDPOINT CHANGES
**Function:** `tableau_to_powerbi_api`
**New Logic:**
```python
async def tableau_to_powerbi_api(report_id: UUID, user: User):
    # 1. Get report details from database
    report_detail = ReportDetailManager.get_report_by_id(report_id)

    # 2. Check migration status
    if report_detail.is_migrated:
        # Generate pre-signed URL for existing file
        s3_path = MIGRATE_OUTPUTS_PATH.format(
            organization_name=user.organization.name,
            s3_report_id=report_detail.report_id
        )
        download_url = await s3.generate_presigned_url(s3_path)
        return {
            "report_id": str(report_detail.report_id),
            "report_name": report_detail.name,
            "download_url": download_url,
            "migrated_status": report_detail.migrated_status
        }

    # 3. Perform migration if not migrated
    # ... migration logic
```

### 2. S3 PATH CONSTRUCTION CHANGES
**Previous:** Multiple path structures based on conditions
**New:** Unified path construction using constants:
```python
# Input file path
input_path = REPORT_PATH.format(
    organization_name=user.organization.name,
    s3_report_id=report_detail.report_id
) + f"/{report_name}.twb"

# Output file path
output_path = MIGRATE_OUTPUTS_PATH.format(
    organization_name=user.organization.name,
    s3_report_id=report_detail.report_id
) + f"/{report_name}_migrated.zip"
```

### 3. DATABASE STATUS UPDATES
**New Function Usage:** `ReportDetailManager.mark_migrated()`
```python
# On successful migration
ReportDetailManager.mark_migrated(
    report_id=str(report_id),
    status="SUCCESS",
    message="Migration completed successfully"
)

# On migration failure
ReportDetailManager.mark_migrated(
    report_id=str(report_id),
    status="FAILURE",
    message=f"Migration failed: {error_message}"
)
```

### 4. RESPONSE FORMAT CHANGES
**New Response Structure:**
```python
{
    "report_id": str,           # Report's UUID
    "report_name": str,         # Name of the report
    "download_url": str,        # Pre-signed S3 URL
    "report_path": str,         # Hierarchical path
    "migrated_status": str      # JSON string with status and message
}
```

---

## IMPLEMENTATION CHECKLIST

### Functions to Modify:
1. **API Layer (`migrate.py`):**
   - [ ] Update endpoint to accept `report_id` instead of complex request body
   - [ ] Add migration status checking logic
   - [ ] Implement pre-signed URL generation for existing files

2. **Migration Processor (`migration_processor.py`):**
   - [ ] Remove `process_id` usage
   - [ ] Update to work with single report processing
   - [ ] Add database status checking

3. **Migration Service (`migration_service.py`):**
   - [ ] Update input schema handling
   - [ ] Remove `process_id` references
   - [ ] Implement new S3 path structure

4. **File Processing (`process_files.py`):**
   - [ ] Update S3 path construction using new constants
   - [ ] Remove old path logic based on `is_upload_file`
   - [ ] Implement unified path structure

5. **Database Integration:**
   - [ ] Use `ReportDetailManager.mark_migrated()` for status updates
   - [ ] Add migration status checking before processing
   - [ ] Update response format to include status information

### Local Path Changes:
- **No changes required** for local file processing paths
- Local directories remain the same for temporary file handling

### S3 Path Changes:
- **All S3 operations** now use the unified path structure from constants.py
- **Migration outputs** stored in `MIGRATE_OUTPUTS_PATH`
- **Input files** accessed from `REPORT_PATH`

---

## BENEFITS OF NEW ARCHITECTURE

1. **Simplified Logic:** Single report processing eliminates complex conditional paths
2. **Better Tracking:** Individual report status tracking with detailed migration status
3. **Unified Storage:** Consistent S3 path structure across all operations
4. **Improved Performance:** Pre-signed URL generation for already migrated reports
5. **Enhanced Monitoring:** Detailed status tracking with JSON status messages
6. **Scalability:** Report-based processing allows for better resource management