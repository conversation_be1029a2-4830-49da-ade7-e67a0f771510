import uuid

from app.services.migrate import process_tableau_datasources

async def generate_date_relationships(date_columns_data):
    """
    Generates date relationship objects with provided IDs from preprocessed date columns.
    """
    relationships = []
    for entry in date_columns_data:
        relationship_id = entry["relationship_id"]
        table_name = entry["table_name"]
        column_name = entry["column_name"]
        local_date_table = entry["local_date_table_ref"]

        from_column = f"{table_name}.{column_name}" if ' ' not in column_name else f"{table_name}.'{column_name}'"
        to_column = f"{local_date_table}.Date"

        relationships.append({
            "id": relationship_id,
            "joinOnDateBehavior": "datePartOnly",
            "fromColumn": from_column,
            "toColumn": to_column
        })
    return relationships

async def generate_relationships(tableau_relationship_data):
    """
    Generates normal relationship objects from raw Tableau relationship data.
    """
    relationships = []

    for item in tableau_relationship_data:
        from_table = item["from_table"]
        to_table = item["to_table"]
        from_col = item["from_column"]
        to_col = item["to_column"]


        from_column = f"{from_table}.{from_col}" if ' ' not in from_col else f"{from_table}.'{from_col}'"
        to_column = f"{to_table}.{to_col}" if ' ' not in to_col else f"{to_table}.'{to_col}'"


        from_unique = item["from_endpoint"].get("unique-key") == "true"
        to_unique = item["to_endpoint"].get("unique-key") == "true"


        if from_unique and to_unique:
            from_cardinality, to_cardinality = "one", "one"
        elif not from_unique and not to_unique:
            from_cardinality, to_cardinality = "many", "many"
        elif from_unique and not to_unique:
            from_cardinality, to_cardinality = "many", "many"
        elif not from_unique and to_unique:
            from_cardinality, to_cardinality = "many", "one"
        else:
            from_cardinality, to_cardinality = "unknown", "unknown"

        relationships.append({
            "id": str(uuid.uuid4()),
            "crossFilteringBehavior": "bothDirections",
            "fromCardinality": from_cardinality,
            "toCardinality": to_cardinality,
            "fromColumn": from_column,
            "toColumn": to_column
        })

    return relationships


async def generate_all_relationships(twb_file_path, date_columns_data):
    """
    Merges normal and date relationships into a single structure for Power BI relationship.tmdl file.
    """
    table_column_data, tableau_relationship_data = await process_tableau_datasources(twb_file_path)
    relationships = await generate_relationships(tableau_relationship_data)
    date_relationships = await generate_date_relationships(date_columns_data)

    return relationships + date_relationships
