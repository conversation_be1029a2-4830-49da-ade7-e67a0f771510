cultureInfo en-US

	linguisticMetadata =
			{
			  "Version": "3.5.0",
			  "Language": "en-US",
			  "Entities": {
			    "order": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order": {
			            "State": "Generated"
			          }
			        },
			        {
			          "instruction": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "direction": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "edict": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "command": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "directive": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "demand": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "mandate": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "imperative": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "stability": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.466
			          }
			        },
			        {
			          "harmony": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.466
			          }
			        }
			      ]
			    },
			    "order.category": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Category"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "category": {
			            "State": "Generated"
			          }
			        },
			        {
			          "classification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "class": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "grouping": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "kind": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        }
			      ]
			    },
			    "order.category_hierarchy": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "Hierarchy": "Category Hierarchy"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "category hierarchy": {
			            "State": "Generated"
			          }
			        },
			        {
			          "hierarchy": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "classification hierarchy": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "class hierarchy": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        },
			        {
			          "group hierarchy": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        },
			        {
			          "type hierarchy": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        },
			        {
			          "grouping hierarchy": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        },
			        {
			          "kind hierarchy": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        },
			        {
			          "category ladder": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        },
			        {
			          "category grading": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        },
			        {
			          "category order": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        },
			        {
			          "category pyramid": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        }
			      ]
			    },
			    "order.category_hierarchy.category": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "Hierarchy": "Category Hierarchy",
			          "HierarchyLevel": "Category"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "category": {
			            "State": "Generated"
			          }
			        },
			        {
			          "classification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "class": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "grouping": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "kind": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        }
			      ]
			    },
			    "order.category_hierarchy.product_name": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "Hierarchy": "Category Hierarchy",
			          "HierarchyLevel": "Product Name"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "product name": {
			            "State": "Generated"
			          }
			        },
			        {
			          "product": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "artifact": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "product nickname": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "product title": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "product label": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "product tag": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "item": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "merchandise": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "produce": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "artifact name": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "item name": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ]
			    },
			    "order.category_hierarchy.subcategory": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "Hierarchy": "Category Hierarchy",
			          "HierarchyLevel": "Sub-Category"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "sub-category": {
			            "State": "Generated"
			          }
			        },
			        {
			          "sub category": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "sub-classification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.762
			          }
			        },
			        {
			          "sub classification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "sub-class": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-group": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-grouping": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-kind": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub class": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "sub group": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "sub type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ]
			    },
			    "order.city": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "City"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "city": {
			            "State": "Generated"
			          }
			        },
			        {
			          "location": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "metropolis": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "municipality": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "town": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "metropolitan": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        }
			      ]
			    },
			    "order.country_region": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Country/Region"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "country/region": {
			            "State": "Generated"
			          }
			        },
			        {
			          "country": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "country region": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "country/ location": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.846
			          }
			        },
			        {
			          "country location": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.833
			          }
			        },
			        {
			          "country/ province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.762
			          }
			        },
			        {
			          "country province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "country/ area": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "country/ district": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "country/ section": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "country/ zone": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "country/ continent": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "nation": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        }
			      ]
			    },
			    "order.customer_ID": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Customer ID"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "customer ID": {
			            "State": "Generated"
			          }
			        },
			        {
			          "customer": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "customer identification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "customer identity": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "customer identifier": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "client": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "consumer": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "user": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "buyer": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "patron": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "purchaser": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "shopper": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        }
			      ]
			    },
			    "order.customer_name": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Customer Name"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "customer name": {
			            "State": "Generated"
			          }
			        },
			        {
			          "customer": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "client": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "consumer": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "user": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "buyer": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "patron": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "purchaser": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "shopper": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "customer nickname": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "customer title": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "customer label": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ]
			    },
			    "order.discount": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Discount"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "discount": {
			            "State": "Generated"
			          }
			        },
			        {
			          "reduction": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "markdown": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "concession": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "deduction": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "rebate": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        }
			      ]
			    },
			    "order.order_ID": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Order ID"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order ID": {
			            "State": "Generated"
			          }
			        },
			        {
			          "order identification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "order identity": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "order identifier": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "order credential": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ]
			    },
			    "order.order_date": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Order Date"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order date": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date": {
			            "State": "Generated",
			            "Weight": 0.7
			          }
			        },
			        {
			          "order moment": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "order period": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.order_date.variation.date_hierarchy": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Order Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order date hierarchy": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date hierarchy": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.order_date.variation.date_hierarchy.day": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Order Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Day"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order day": {
			            "State": "Generated"
			          }
			        },
			        {
			          "day": {
			            "State": "Generated"
			          }
			        },
			        {
			          "order date day": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date day": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "order moment day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "order period day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.order_date.variation.date_hierarchy.month": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Order Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Month"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order month": {
			            "State": "Generated"
			          }
			        },
			        {
			          "month": {
			            "State": "Generated"
			          }
			        },
			        {
			          "order date month": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date month": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "order date mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.762
			          }
			        },
			        {
			          "order mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "date mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "order moment month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "order period month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.order_date.variation.date_hierarchy.quarter": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Order Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Quarter"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order quarter": {
			            "State": "Generated"
			          }
			        },
			        {
			          "quarter": {
			            "State": "Generated"
			          }
			        },
			        {
			          "order date quarter": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date quarter": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "order date qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.846
			          }
			        },
			        {
			          "order qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.833
			          }
			        },
			        {
			          "date qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.833
			          }
			        },
			        {
			          "qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.818
			          }
			        },
			        {
			          "order moment quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "order period quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.order_date.variation.date_hierarchy.year": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Order Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Year"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "order year": {
			            "State": "Generated"
			          }
			        },
			        {
			          "year": {
			            "State": "Generated"
			          }
			        },
			        {
			          "order date year": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date year": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "order date yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.762
			          }
			        },
			        {
			          "order yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "date yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "order moment year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "order period year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.postal_code": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Postal Code"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "postal code": {
			            "State": "Generated"
			          }
			        },
			        {
			          "postal id": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "postal key": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ]
			    },
			    "order.product_ID": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Product ID"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "product ID": {
			            "State": "Generated"
			          }
			        },
			        {
			          "product": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "product identification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "product identity": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "product identifier": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "artifact": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "product credential": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "item": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "merchandise": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "produce": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "artifact id": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "item id": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ]
			    },
			    "order.product_name": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Product Name"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "product name": {
			            "State": "Generated"
			          }
			        },
			        {
			          "product": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "artifact": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "product nickname": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "product title": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "product label": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "product tag": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "item": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "merchandise": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "produce": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "artifact name": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "item name": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ]
			    },
			    "order.profit": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Profit"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "profit": {
			            "State": "Generated"
			          }
			        },
			        {
			          "income": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "revenue": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "turnover": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "yield": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "advantage": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "gain": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "benefit": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "use": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "reward": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "good": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        }
			      ]
			    },
			    "order.quantity": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Quantity"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "quantity": {
			            "State": "Generated"
			          }
			        },
			        {
			          "extent": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "magnitude": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "size": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "capacity": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "mass": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        }
			      ]
			    },
			    "order.region": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Region"
			        }
			      },
			      "State": "Generated",
			      "Visibility": {
			        "Value": "Hidden"
			      },
			      "Terms": [
			        {
			          "region": {
			            "State": "Generated"
			          }
			        },
			        {
			          "location": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.818
			          }
			        },
			        {
			          "area": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "district": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "section": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "zone": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "continent": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        }
			      ]
			    },
			    "order.row_ID": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Row ID"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "row ID": {
			            "State": "Generated"
			          }
			        },
			        {
			          "row": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "row identification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "row identity": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "row identifier": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "row credential": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "line id": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ]
			    },
			    "order.sale": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Sales"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "sale": {
			            "State": "Generated"
			          }
			        },
			        {
			          "auction": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "transaction": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.466
			          }
			        },
			        {
			          "deal": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "trade": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "vending": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "retailing": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "selling": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        }
			      ]
			    },
			    "order.segment": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Segment"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "segment": {
			            "State": "Generated"
			          }
			        },
			        {
			          "division": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "subdivision": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "section": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "sector": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "bit": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "fragment": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "part": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "piece": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "portion": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "slice": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        }
			      ]
			    },
			    "order.ship_date": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Ship Date"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "ship date": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date": {
			            "State": "Generated",
			            "Weight": 0.7
			          }
			        },
			        {
			          "ship moment": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "ship period": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.ship_date.variation.date_hierarchy": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Ship Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "ship date hierarchy": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date hierarchy": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.ship_date.variation.date_hierarchy.day": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Ship Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Day"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "ship day": {
			            "State": "Generated"
			          }
			        },
			        {
			          "day": {
			            "State": "Generated"
			          }
			        },
			        {
			          "ship date day": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date day": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "ship moment day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "ship period day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period day": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.ship_date.variation.date_hierarchy.month": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Ship Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Month"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "ship month": {
			            "State": "Generated"
			          }
			        },
			        {
			          "month": {
			            "State": "Generated"
			          }
			        },
			        {
			          "ship date month": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date month": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "ship date mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.762
			          }
			        },
			        {
			          "ship mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "date mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "mth": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "ship moment month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "ship period month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period month": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.ship_date.variation.date_hierarchy.quarter": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Ship Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Quarter"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "ship quarter": {
			            "State": "Generated"
			          }
			        },
			        {
			          "quarter": {
			            "State": "Generated"
			          }
			        },
			        {
			          "ship date quarter": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date quarter": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "ship date qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.846
			          }
			        },
			        {
			          "ship qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.833
			          }
			        },
			        {
			          "date qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.833
			          }
			        },
			        {
			          "qtr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.818
			          }
			        },
			        {
			          "ship moment quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "ship period quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period quarter": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.ship_date.variation.date_hierarchy.year": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "VariationSource": "Ship Date",
			          "VariationSet": "Variation",
			          "Hierarchy": "Date Hierarchy",
			          "HierarchyLevel": "Year"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "ship year": {
			            "State": "Generated"
			          }
			        },
			        {
			          "year": {
			            "State": "Generated"
			          }
			        },
			        {
			          "ship date year": {
			            "State": "Generated"
			          }
			        },
			        {
			          "date year": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "ship date yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.762
			          }
			        },
			        {
			          "ship yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "date yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "yr": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "ship moment year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "moment year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "ship period year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.591
			          }
			        },
			        {
			          "period year": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.582
			          }
			        }
			      ],
			      "SemanticType": "Time"
			    },
			    "order.ship_mode": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Ship Mode"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "ship mode": {
			            "State": "Generated"
			          }
			        },
			        {
			          "mode": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "ship manner": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.5
			          }
			        },
			        {
			          "ship method": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.5
			          }
			        },
			        {
			          "ship genre": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.5
			          }
			        },
			        {
			          "manner": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "method": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "genre": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "ship style": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        },
			        {
			          "ship mean": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        },
			        {
			          "ship approach": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        },
			        {
			          "ship type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        }
			      ]
			    },
			    "order.state_province": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "State/Province"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "state/province": {
			            "State": "Generated"
			          }
			        },
			        {
			          "": {}
			        },
			        {
			          "state province": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "province": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "location": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "territory": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "nation": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.736
			          }
			        },
			        {
			          "condition": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "location /province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "province /province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "territory /province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "nation /province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.609
			          }
			        },
			        {
			          "location province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        },
			        {
			          "province province": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.6
			          }
			        }
			      ]
			    },
			    "order.subcategory": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Orders",
			          "ConceptualProperty": "Sub-Category"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "sub-category": {
			            "State": "Generated"
			          }
			        },
			        {
			          "sub category": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "sub-classification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.762
			          }
			        },
			        {
			          "sub classification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "sub-class": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-group": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-grouping": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub-kind": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.739
			          }
			        },
			        {
			          "sub class": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "sub group": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        },
			        {
			          "sub type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ]
			    },
			    "person": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "People"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "person": {
			            "State": "Generated"
			          }
			        },
			        {
			          "creature": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "being": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "individual": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "soul": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "type": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "party": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "human": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "self": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.476
			          }
			        },
			        {
			          "body": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "form": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        }
			      ]
			    },
			    "person.region": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "People",
			          "ConceptualProperty": "Region"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "region": {
			            "State": "Generated"
			          }
			        },
			        {
			          "location": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.818
			          }
			        },
			        {
			          "area": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "district": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "section": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "zone": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        },
			        {
			          "continent": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.714
			          }
			        }
			      ]
			    },
			    "person.regional_manager": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "People",
			          "ConceptualProperty": "Regional Manager"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "regional manager": {
			            "State": "Generated"
			          }
			        },
			        {
			          "manager": {
			            "State": "Generated",
			            "Weight": 0.97
			          }
			        },
			        {
			          "regional director": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.5
			          }
			        },
			        {
			          "regional administrator": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.5
			          }
			        },
			        {
			          "regional supervisor": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.5
			          }
			        },
			        {
			          "regional leader": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.5
			          }
			        },
			        {
			          "director": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "administrator": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "supervisor": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "leader": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "regional boss": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        },
			        {
			          "regional executive": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.485
			          }
			        }
			      ]
			    },
			    "return": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Returns"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "return": {
			            "State": "Generated"
			          }
			        },
			        {
			          "reappearance": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "reoccurrence": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "homecoming": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "arrival": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.491
			          }
			        },
			        {
			          "revenue": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.466
			          }
			        },
			        {
			          "yield": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "gain": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        },
			        {
			          "benefit": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.452
			          }
			        }
			      ]
			    },
			    "return.order_ID": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Returns",
			          "ConceptualProperty": "Order ID"
			        }
			      },
			      "State": "Generated",
			      "Visibility": {
			        "Value": "Hidden"
			      },
			      "Terms": [
			        {
			          "order ID": {
			            "State": "Generated"
			          }
			        },
			        {
			          "order identification": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "order identity": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "order identifier": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.75
			          }
			        },
			        {
			          "order credential": {
			            "Type": "Noun",
			            "State": "Suggested",
			            "Source": {
			              "Agent": "Thesaurus"
			            },
			            "Weight": 0.727
			          }
			        }
			      ]
			    },
			    "return.returned": {
			      "Definition": {
			        "Binding": {
			          "ConceptualEntity": "Returns",
			          "ConceptualProperty": "Returned"
			        }
			      },
			      "State": "Generated",
			      "Terms": [
			        {
			          "returned": {
			            "State": "Generated"
			          }
			        }
			      ]
			    }
			  },
			  "Relationships": {
			    "order_category_hierarchy_category_has_order_category_hierarchy_subcategory": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.category_hierarchy.category": {
			          "Target": {
			            "Entity": "order.category_hierarchy.category"
			          }
			        },
			        "order.category_hierarchy.subcategory": {
			          "Target": {
			            "Entity": "order.category_hierarchy.subcategory"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.category_hierarchy.category"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy.subcategory"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "categories_have_subcategories"
			        },
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.category_hierarchy.subcategory"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy.category"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "subcategories_have_categories"
			        },
			        {
			          "Preposition": {
			            "Subject": {
			              "Role": "order.category_hierarchy.subcategory"
			            },
			            "Prepositions": [
			              {
			                "in": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order.category_hierarchy.category"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "subcategories_are_in_categories"
			        }
			      ]
			    },
			    "order_category_hierarchy_has_category": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.category_hierarchy": {
			          "Target": {
			            "Entity": "order.category_hierarchy"
			          }
			        },
			        "order.category_hierarchy.category": {
			          "Target": {
			            "Entity": "order.category_hierarchy.category"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.category_hierarchy"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy.category"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "category_hierarchies_have_categories"
			        }
			      ]
			    },
			    "order_category_hierarchy_has_product_name": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.category_hierarchy": {
			          "Target": {
			            "Entity": "order.category_hierarchy"
			          }
			        },
			        "order.category_hierarchy.product_name": {
			          "Target": {
			            "Entity": "order.category_hierarchy.product_name"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.category_hierarchy"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy.product_name"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "category_hierarchies_have_product_names"
			        }
			      ]
			    },
			    "order_category_hierarchy_has_subcategory": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.category_hierarchy": {
			          "Target": {
			            "Entity": "order.category_hierarchy"
			          }
			        },
			        "order.category_hierarchy.subcategory": {
			          "Target": {
			            "Entity": "order.category_hierarchy.subcategory"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.category_hierarchy"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy.subcategory"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "category_hierarchies_have_subcategories"
			        }
			      ]
			    },
			    "order_category_hierarchy_subcategory_has_order_category_hierarchy_product_name": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.category_hierarchy.product_name": {
			          "Target": {
			            "Entity": "order.category_hierarchy.product_name"
			          }
			        },
			        "order.category_hierarchy.subcategory": {
			          "Target": {
			            "Entity": "order.category_hierarchy.subcategory"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.category_hierarchy.subcategory"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy.product_name"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "subcategories_have_product_names"
			        },
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.category_hierarchy.product_name"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy.subcategory"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "product_names_have_subcategories"
			        },
			        {
			          "Preposition": {
			            "Subject": {
			              "Role": "order.category_hierarchy.product_name"
			            },
			            "Prepositions": [
			              {
			                "in": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order.category_hierarchy.subcategory"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "product_names_are_in_subcategories"
			        }
			      ]
			    },
			    "order_has_average_discount": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.average_discount": {
			          "Target": {
			            "Entity": "order.average_discount"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.average_discount"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_average_discount"
			        }
			      ]
			    },
			    "order_has_category": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.category": {
			          "Target": {
			            "Entity": "order.category"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.category"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_categories"
			        }
			      ]
			    },
			    "order_has_category_hierarchy": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.category_hierarchy": {
			          "Target": {
			            "Entity": "order.category_hierarchy"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.category_hierarchy"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_category_hierarchies"
			        }
			      ]
			    },
			    "order_has_city": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.city": {
			          "Target": {
			            "Entity": "order.city"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.city"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_cities"
			        }
			      ]
			    },
			    "order_has_country_region": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.country_region": {
			          "Target": {
			            "Entity": "order.country_region"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.country_region"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_country_regions"
			        }
			      ]
			    },
			    "order_has_customer_ID": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.customer_ID": {
			          "Target": {
			            "Entity": "order.customer_ID"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.customer_ID"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_customer_ID"
			        }
			      ]
			    },
			    "order_has_customer_name": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.customer_name": {
			          "Target": {
			            "Entity": "order.customer_name"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.customer_name"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_customer_names"
			        }
			      ]
			    },
			    "order_has_discount": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.discount": {
			          "Target": {
			            "Entity": "order.discount"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.discount"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_discounts"
			        }
			      ]
			    },
			    "order_has_order_ID": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.order_ID": {
			          "Target": {
			            "Entity": "order.order_ID"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.order_ID"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_order_ID"
			        }
			      ]
			    },
			    "order_has_order_date": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.order_date": {
			          "Target": {
			            "Entity": "order.order_date"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.order_date"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_order_dates"
			        }
			      ]
			    },
			    "order_has_person": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "person": {
			          "Target": {
			            "Entity": "person"
			          },
			          "Nouns": [
			            {
			              "region": {
			                "State": "Generated"
			              }
			            }
			          ]
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "person"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_people"
			        },
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "person"
			            },
			            "Object": {
			              "Role": "order"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "people_have_orders"
			        }
			      ]
			    },
			    "order_has_postal_code": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.postal_code": {
			          "Target": {
			            "Entity": "order.postal_code"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.postal_code"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_postal_codes"
			        }
			      ]
			    },
			    "order_has_product_ID": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.product_ID": {
			          "Target": {
			            "Entity": "order.product_ID"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.product_ID"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_product_ID"
			        }
			      ]
			    },
			    "order_has_product_name": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.product_name": {
			          "Target": {
			            "Entity": "order.product_name"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.product_name"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_product_names"
			        }
			      ]
			    },
			    "order_has_profit": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.profit": {
			          "Target": {
			            "Entity": "order.profit"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.profit"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_profits"
			        },
			        {
			          "Adjective": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Adjectives": [
			              {
			                "expensive": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Antonyms": [
			              {
			                "cheap": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Measurement": {
			              "Role": "order.profit"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "profit_indicates_how_expensive_orders_are"
			        }
			      ]
			    },
			    "order_has_profit_measure": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.profit_measure": {
			          "Target": {
			            "Entity": "order.profit_measure"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.profit_measure"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_profit_measure"
			        }
			      ]
			    },
			    "order_has_profit_per_order": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.profit_per_order": {
			          "Target": {
			            "Entity": "order.profit_per_order"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.profit_per_order"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_profit_per_order"
			        }
			      ]
			    },
			    "order_has_profit_ratio": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.profit_ratio": {
			          "Target": {
			            "Entity": "order.profit_ratio"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.profit_ratio"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_profit_ratio"
			        }
			      ]
			    },
			    "order_has_profit_ratio1": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.profit_ratio1": {
			          "Target": {
			            "Entity": "order.profit_ratio1"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.profit_ratio1"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_profit_ratio1"
			        }
			      ]
			    },
			    "order_has_quantity": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.quantity": {
			          "Target": {
			            "Entity": "order.quantity"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.quantity"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_quantities"
			        }
			      ]
			    },
			    "order_has_row_ID": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.row_ID": {
			          "Target": {
			            "Entity": "order.row_ID"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.row_ID"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_row_ID"
			        }
			      ]
			    },
			    "order_has_sale": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.sale": {
			          "Target": {
			            "Entity": "order.sale"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.sale"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_sales"
			        },
			        {
			          "Adjective": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Adjectives": [
			              {
			                "expensive": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Antonyms": [
			              {
			                "cheap": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Measurement": {
			              "Role": "order.sale"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "sale_indicates_how_expensive_orders_are"
			        }
			      ]
			    },
			    "order_has_sales_measure": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.sales_measure": {
			          "Target": {
			            "Entity": "order.sales_measure"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.sales_measure"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_sales_measure"
			        }
			      ]
			    },
			    "order_has_sales_per_customer": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.sales_per_customer": {
			          "Target": {
			            "Entity": "order.sales_per_customer"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.sales_per_customer"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_sales_per_customer"
			        }
			      ]
			    },
			    "order_has_segment": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.segment": {
			          "Target": {
			            "Entity": "order.segment"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.segment"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_segments"
			        }
			      ]
			    },
			    "order_has_ship_date": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.ship_date": {
			          "Target": {
			            "Entity": "order.ship_date"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.ship_date"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_ship_dates"
			        }
			      ]
			    },
			    "order_has_ship_mode": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.ship_mode": {
			          "Target": {
			            "Entity": "order.ship_mode"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.ship_mode"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_ship_modes"
			        }
			      ]
			    },
			    "order_has_state_province": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.state_province": {
			          "Target": {
			            "Entity": "order.state_province"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.state_province"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_state_provinces"
			        }
			      ]
			    },
			    "order_has_subcategory": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.subcategory": {
			          "Target": {
			            "Entity": "order.subcategory"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Object": {
			              "Role": "order.subcategory"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_have_subcategories"
			        }
			      ]
			    },
			    "order_in_city": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.city": {
			          "Target": {
			            "Entity": "order.city"
			          }
			        }
			      },
			      "SemanticSlots": {
			        "Where": {
			          "Role": "order.city"
			        }
			      },
			      "Phrasings": [
			        {
			          "Preposition": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Prepositions": [
			              {
			                "in": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order.city"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_are_in_cities"
			        }
			      ]
			    },
			    "order_in_country_region": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.country_region": {
			          "Target": {
			            "Entity": "order.country_region"
			          }
			        }
			      },
			      "SemanticSlots": {
			        "Where": {
			          "Role": "order.country_region"
			        }
			      },
			      "Phrasings": [
			        {
			          "Preposition": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Prepositions": [
			              {
			                "in": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order.country_region"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_are_in_country_regions"
			        }
			      ]
			    },
			    "order_in_postal_code": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.postal_code": {
			          "Target": {
			            "Entity": "order.postal_code"
			          }
			        }
			      },
			      "SemanticSlots": {
			        "Where": {
			          "Role": "order.postal_code"
			        }
			      },
			      "Phrasings": [
			        {
			          "Preposition": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Prepositions": [
			              {
			                "in": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order.postal_code"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_are_in_postal_codes"
			        }
			      ]
			    },
			    "order_in_state_province": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.state_province": {
			          "Target": {
			            "Entity": "order.state_province"
			          }
			        }
			      },
			      "SemanticSlots": {
			        "Where": {
			          "Role": "order.state_province"
			        }
			      },
			      "Phrasings": [
			        {
			          "Preposition": {
			            "Subject": {
			              "Role": "order"
			            },
			            "Prepositions": [
			              {
			                "in": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order.state_province"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "orders_are_in_state_provinces"
			        }
			      ]
			    },
			    "order_is_ordered_on_order_date": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.order_date": {
			          "Target": {
			            "Entity": "order.order_date"
			          }
			        }
			      },
			      "SemanticSlots": {
			        "When": {
			          "Role": "order.order_date"
			        }
			      },
			      "Phrasings": [
			        {
			          "Verb": {
			            "Verbs": [
			              {
			                "order": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.9,
			          "ID": "orders_are_ordered_on_order_dates"
			        }
			      ]
			    },
			    "order_is_shipped_on_ship_date": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "order.ship_date": {
			          "Target": {
			            "Entity": "order.ship_date"
			          }
			        }
			      },
			      "SemanticSlots": {
			        "When": {
			          "Role": "order.ship_date"
			        }
			      },
			      "Phrasings": [
			        {
			          "Verb": {
			            "Verbs": [
			              {
			                "ship": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "order"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.9,
			          "ID": "orders_are_shipped_on_ship_dates"
			        }
			      ]
			    },
			    "order_order_date_has_variation_date_hierarchy": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.order_date": {
			          "Target": {
			            "Entity": "order.order_date"
			          }
			        },
			        "order.order_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.order_date"
			            },
			            "Object": {
			              "Role": "order.order_date.variation.date_hierarchy"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "order_dates_have_order_date_hierarchies"
			        }
			      ]
			    },
			    "order_order_date_variation_date_hierarchy_has_day": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.order_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy"
			          }
			        },
			        "order.order_date.variation.date_hierarchy.day": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy.day"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.order_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.order_date.variation.date_hierarchy.day"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "order_date_hierarchies_have_order_days"
			        }
			      ]
			    },
			    "order_order_date_variation_date_hierarchy_has_month": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.order_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy"
			          }
			        },
			        "order.order_date.variation.date_hierarchy.month": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy.month"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.order_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.order_date.variation.date_hierarchy.month"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "order_date_hierarchies_have_order_months"
			        }
			      ]
			    },
			    "order_order_date_variation_date_hierarchy_has_quarter": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.order_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy"
			          }
			        },
			        "order.order_date.variation.date_hierarchy.quarter": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy.quarter"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.order_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.order_date.variation.date_hierarchy.quarter"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "order_date_hierarchies_have_order_quarters"
			        }
			      ]
			    },
			    "order_order_date_variation_date_hierarchy_has_year": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.order_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy"
			          }
			        },
			        "order.order_date.variation.date_hierarchy.year": {
			          "Target": {
			            "Entity": "order.order_date.variation.date_hierarchy.year"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.order_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.order_date.variation.date_hierarchy.year"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "order_date_hierarchies_have_order_years"
			        }
			      ]
			    },
			    "order_ship_date_has_variation_date_hierarchy": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.ship_date": {
			          "Target": {
			            "Entity": "order.ship_date"
			          }
			        },
			        "order.ship_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.ship_date"
			            },
			            "Object": {
			              "Role": "order.ship_date.variation.date_hierarchy"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "ship_dates_have_ship_date_hierarchies"
			        }
			      ]
			    },
			    "order_ship_date_variation_date_hierarchy_has_day": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.ship_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy"
			          }
			        },
			        "order.ship_date.variation.date_hierarchy.day": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy.day"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.ship_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.ship_date.variation.date_hierarchy.day"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "ship_date_hierarchies_have_ship_days"
			        }
			      ]
			    },
			    "order_ship_date_variation_date_hierarchy_has_month": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.ship_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy"
			          }
			        },
			        "order.ship_date.variation.date_hierarchy.month": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy.month"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.ship_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.ship_date.variation.date_hierarchy.month"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "ship_date_hierarchies_have_ship_months"
			        }
			      ]
			    },
			    "order_ship_date_variation_date_hierarchy_has_quarter": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.ship_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy"
			          }
			        },
			        "order.ship_date.variation.date_hierarchy.quarter": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy.quarter"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.ship_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.ship_date.variation.date_hierarchy.quarter"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "ship_date_hierarchies_have_ship_quarters"
			        }
			      ]
			    },
			    "order_ship_date_variation_date_hierarchy_has_year": {
			      "Binding": {
			        "ConceptualEntity": "Orders"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order.ship_date.variation.date_hierarchy": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy"
			          }
			        },
			        "order.ship_date.variation.date_hierarchy.year": {
			          "Target": {
			            "Entity": "order.ship_date.variation.date_hierarchy.year"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "order.ship_date.variation.date_hierarchy"
			            },
			            "Object": {
			              "Role": "order.ship_date.variation.date_hierarchy.year"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "ship_date_hierarchies_have_ship_years"
			        }
			      ]
			    },
			    "person_has_region": {
			      "Binding": {
			        "ConceptualEntity": "People"
			      },
			      "State": "Generated",
			      "Roles": {
			        "person": {
			          "Target": {
			            "Entity": "person"
			          }
			        },
			        "person.region": {
			          "Target": {
			            "Entity": "person.region"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "person"
			            },
			            "Object": {
			              "Role": "person.region"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "people_have_regions"
			        }
			      ]
			    },
			    "person_has_regional_manager": {
			      "Binding": {
			        "ConceptualEntity": "People"
			      },
			      "State": "Generated",
			      "Roles": {
			        "person": {
			          "Target": {
			            "Entity": "person"
			          }
			        },
			        "person.regional_manager": {
			          "Target": {
			            "Entity": "person.regional_manager"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "person"
			            },
			            "Object": {
			              "Role": "person.regional_manager"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "people_have_regional_managers"
			        },
			        {
			          "Verb": {
			            "Subject": {
			              "Role": "person.regional_manager"
			            },
			            "Verbs": [
			              {
			                "manage": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "person"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.75,
			          "ID": "regional_managers_manage_people"
			        },
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "person.regional_manager"
			            },
			            "Object": {
			              "Role": "person"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "regional_managers_have_people"
			        }
			      ]
			    },
			    "person_in_region": {
			      "Binding": {
			        "ConceptualEntity": "People"
			      },
			      "State": "Generated",
			      "Roles": {
			        "person": {
			          "Target": {
			            "Entity": "person"
			          }
			        },
			        "person.region": {
			          "Target": {
			            "Entity": "person.region"
			          }
			        }
			      },
			      "SemanticSlots": {
			        "Where": {
			          "Role": "person.region"
			        }
			      },
			      "Phrasings": [
			        {
			          "Preposition": {
			            "Subject": {
			              "Role": "person"
			            },
			            "Prepositions": [
			              {
			                "in": {
			                  "State": "Generated"
			                }
			              }
			            ],
			            "Object": {
			              "Role": "person.region"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "people_are_in_regions"
			        }
			      ]
			    },
			    "return_has_order": {
			      "Binding": {
			        "ConceptualEntity": "Returns"
			      },
			      "State": "Generated",
			      "Roles": {
			        "order": {
			          "Target": {
			            "Entity": "order"
			          }
			        },
			        "return": {
			          "Target": {
			            "Entity": "return"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "return"
			            },
			            "Object": {
			              "Role": "order"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "returns_have_orders"
			        }
			      ]
			    },
			    "return_has_returned": {
			      "Binding": {
			        "ConceptualEntity": "Returns"
			      },
			      "State": "Generated",
			      "Roles": {
			        "return": {
			          "Target": {
			            "Entity": "return"
			          }
			        },
			        "return.returned": {
			          "Target": {
			            "Entity": "return.returned"
			          }
			        }
			      },
			      "Phrasings": [
			        {
			          "Attribute": {
			            "Subject": {
			              "Role": "return"
			            },
			            "Object": {
			              "Role": "return.returned"
			            }
			          },
			          "State": "Generated",
			          "Weight": 0.99,
			          "ID": "returns_have_returned"
			        }
			      ]
			    }
			  }
			}
		contentType: json

