"""added report and project counts in server_details

Revision ID: 8bc3abb72037
Revises: 0c048dfe7ad8
Create Date: 2025-06-19 15:31:08.817072

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8bc3abb72037'
down_revision: Union[str, None] = '0c048dfe7ad8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('server_details', sa.Column('report_count', sa.Integer(), server_default=sa.text("0")))
    op.add_column('server_details', sa.Column('project_count', sa.Integer(), server_default=sa.text("0")))
    


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('server_details', 'project_count')
    op.drop_column('server_details', 'report_count')
