from fastapi import (
    <PERSON><PERSON><PERSON><PERSON>,
    Header,
    Form,
    Query,
    HTTPException,
    Depends
)
from fastapi.responses import JSONResponse
from uuid import UUID
from app.services.migrate.migration_processor import MigrateProcessor
from app.schemas.migrate import WorkbooksRequest
from app.models.users import User
from app.models_old.user import UserOld
from app.models.report_details import ReportDetailManager
from app.core.dependencies import get_current_user
from app.core.constants import MIGRATE_OUTPUTS_PATH
from app.core.config import S3Config
from app.core import logger


migrate_router = APIRouter()

@migrate_router.post("/tableau-to-powerbi/{report_id}")
async def migrate_report_to_powerbi(
    report_id: UUID,
    user: User = Depends(get_current_user)
):
    """
    API for migrating a single report from Tableau to Power BI.

    This endpoint uses the new report-based approach:
    1. Checks if the report is already migrated
    2. If migrated, returns existing download link
    3. If not migrated, performs migration and updates status

    Parameters
    ----------
    report_id : UUID
        The unique identifier of the report to migrate
    user : User
        Authenticated user from dependency injection

    Returns
    -------
    JSONResponse with migration result including download link
    """
    logger.info(f"[Migration API] Starting migration for report_id: {report_id}, user: {user.email}")

    try:
        # Get report details from database
        report_detail = ReportDetailManager.get_report_by_id(report_id)
        if not report_detail:
            logger.error(f"[Migration API] Report {report_id} not found in database")
            raise HTTPException(status_code=404, detail=f"Report {report_id} not found")

        logger.info(f"[Migration API] Report details - name: {report_detail.name}, is_migrated: {getattr(report_detail, 'is_migrated', False)}")

        # Check if already migrated
        if getattr(report_detail, 'is_migrated', False):
            logger.info(f"[Migration API] Report already migrated, generating pre-signed URL")

            # Generate pre-signed URL for existing migrated file
            s3_config = S3Config()
            output_path = MIGRATE_OUTPUTS_PATH.format(
                organization_name=user.organization.name,
                s3_report_id=report_detail.report_id
            ) + f"/{report_detail.name}_migrated.zip"

            download_url = await s3_config.generate_presigned_url(output_path)
            report_path = ReportDetailManager.get_report_hierarchy_path(report_detail.project_id, report_detail.name)

            return {
                "message": "Report already migrated",
                "data": {
                    "report_id": str(report_detail.report_id),
                    "report_name": report_detail.name,
                    "download_url": download_url,
                    "report_path": report_path,
                    "migrated_status": report_detail.migrated_status or '{"status": "SUCCESS", "message": "Previously migrated"}'
                }
            }

        # Perform migration if not migrated
        logger.info(f"[Migration API] Report not migrated, starting migration process")
        response = await MigrateProcessor.migrate_single_report(report_id, user)

        logger.info(f"[Migration API] Migration completed successfully for report_id: {report_id}")
        return {
            "message": "Migration completed successfully",
            "data": response.data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[Migration API] Migration failed for report_id: {report_id}, error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Migration failed: {str(e)}")

@migrate_router.post("/tableau-to-powerbi")
async def convert_tableau_to_powerbi(
    body: WorkbooksRequest,
    is_upload_file: bool = Query(False),
    user: UserOld = Depends(get_current_user)
    #   authorization: str = Header(...),
    #   email: str = Header(...),
):
    """ 
    API for migrating files from tableau to powerBI.

    Parameters
    ----------
    body : WorkbooksRequest
        Contains a list of workbook IDs to be migrated.
    authorization : str
        Authorization token passed in the request header.
    email : str
        UserOld's email passed in the request header.

    Returns
    -------
    A JSON response with the migration result and error information.
    """
    if is_upload_file:
        if not body.s3_paths:
            raise HTTPException(status_code=400, detail="s3_paths are required when is_upload_file is True.")
        response = await MigrateProcessor.tableau_to_powerbi(
            # authorization=authorization,
            # email=email,
            s3_paths=body.s3_paths,
            is_upload=True
        )
    else:
        if not body.workbook_ids:
            raise HTTPException(status_code=400, detail="workbook_ids are required when is_upload_file is False.")
        response = await MigrateProcessor.tableau_to_powerbi(
            # authorization,
            # email,
            workbooks=body.workbook_ids
        )
    return JSONResponse(content = {"data": response.data,"error": response.error},status_code = response.status_code)