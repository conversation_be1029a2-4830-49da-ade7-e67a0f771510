"""
This module defines the S3CredentialModel for SQLAlchemy ORM.
It stores AWS S3 credentials and associates them with a specific organization.
"""

import uuid
from sqlalchemy import Column, String, BigInteger, ForeignKey
from sqlalchemy.orm import relationship
from app.core import Base
from sqlalchemy.dialects.postgresql import UUID

class S3CredentialModel(Base):

    """
    SQLAlchemy ORM model representing AWS S3 credentials for an organization.

    Attributes:
        id (str): Primary key, a UUID string uniquely identifying each S3 credential entry.
        bucket_name (str): The name of the S3 bucket.
        region (str): AWS region where the bucket is hosted.
        access_key (str): AWS access key ID.
        secret_key (str): AWS secret access key.
        organization_id (int): Foreign key referencing the associated organization's ID.
        organization (OrganizationDetails): Relationship to the OrganizationDetails model.
    """
    
    __tablename__ = "s3_credentials"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    bucket_name = Column(String, nullable=False)
    region = Column(String, nullable=False)
    access_key = Column(String, nullable=False)
    secret_key = Column(String, nullable=False)  
    organization_id = Column(UUID, ForeignKey("organization_details.id"), nullable=False)
