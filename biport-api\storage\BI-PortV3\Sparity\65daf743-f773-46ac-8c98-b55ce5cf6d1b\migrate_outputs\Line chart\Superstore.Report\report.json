{"config": "{\"version\":\"5.59\",\"themeCollection\":{\"baseTheme\":{\"name\":\"CY24SU10\",\"version\":\"5.59\",\"type\":2}},\"activeSectionIndex\":0,\"defaultDrillFilterOtherVisuals\":true,\"linguisticSchemaSyncVersion\":2,\"settings\":{\"useNewFilterPaneExperience\":true,\"allowChangeFilterTypes\":true,\"useStylableVisualContainerHeader\":true,\"queryLimitOption\":6,\"exportDataMode\":1,\"useDefaultAggregateDisplayName\":true,\"useEnhancedTooltips\":true},\"objects\":{\"section\":[{\"properties\":{\"verticalAlignment\":{\"expr\":{\"Literal\":{\"Value\":\"'Top'\"}}}}}],\"outspacePane\":[{\"properties\":{\"expanded\":{\"expr\":{\"Literal\":{\"Value\":\"false\"}}}}}]}}", "layoutOptimization": 0, "publicCustomVisuals": ["Gantt1448688115699", "mergedbarchartA4B50B148BA8BEB765A6D146EE4450D5"], "resourcePackages": [{"resourcePackage": {"disabled": false, "items": [{"name": "CY24SU10", "path": "BaseThemes/CY24SU10.json", "type": 202}], "name": "SharedResources", "type": 2}}], "sections": [{"config": "{}", "displayName": "Line chart1(discrete)", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "4bd47c73e16d463dbb7f", "ordinal": 1, "visualContainers": [{"config": "{\"name\": \"f6ee46922b0b483e9e15\", \"layouts\": [{\"id\": 0, \"position\": {\"width\": 1280.0, \"height\": 720.0, \"x\": 0.0, \"y\": 0.0, \"z\": 0.0}}], \"singleVisual\": {\"visualType\": \"lineChart\", \"projections\": {\"Y\": [{\"queryRef\": \"Sum(FactInternetSales.SalesAmount)\", \"active\": true}], \"Category\": [{\"queryRef\": \"FactInternetSales.OrderDate.Variation.Date Hierarchy.Year\", \"active\": true}], \"Tooltips\": [{\"queryRef\": \"Sum(FactInternetSales.SalesAmount)\", \"active\": true}, {\"queryRef\": \"FactInternetSales.OrderDate.Variation.Date Hierarchy.Year\", \"active\": true}]}, \"prototypeQuery\": {\"Version\": 2, \"From\": [{\"Name\": \"FactInternetSales\", \"Entity\": \"FactInternetSales\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"FactInternetSales\"}}, \"Property\": \"SalesAmount\"}}, \"Function\": 0}, \"Name\": \"Sum(FactInternetSales.SalesAmount)\", \"NativeReferenceName\": \"sum of SalesAmount\"}, {\"HierarchyLevel\": {\"Expression\": {\"Hierarchy\": {\"Expression\": {\"PropertyVariationSource\": {\"Expression\": {\"SourceRef\": {\"Source\": \"FactInternetSales\"}}, \"Name\": \"Variation\", \"Property\": \"OrderDate\"}}, \"Hierarchy\": \"Date Hierarchy\"}}, \"Level\": \"Year\"}, \"Name\": \"FactInternetSales.OrderDate.Variation.Date Hierarchy.Year\", \"NativeReferenceName\": \"OrderDate Year\"}]}, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true}}"}], "width": 1280.0}, {"config": "{}", "displayName": "Line chart2(discrete)", "displayOption": 1, "filters": "[]", "height": 720.0, "name": "36c159239f5d44119e2d", "ordinal": 2, "visualContainers": [{"config": "{\"name\": \"382f6533a71841758463\", \"layouts\": [{\"id\": 0, \"position\": {\"width\": 1280.0, \"height\": 720.0, \"x\": 0.0, \"y\": 0.0, \"z\": 0.0}}], \"singleVisual\": {\"visualType\": \"lineChart\", \"projections\": {\"Y\": [{\"queryRef\": \"Sum(FactInternetSales.TotalProductCost)\", \"active\": true}, {\"queryRef\": \"Sum(FactInternetSales.SalesAmount)\", \"active\": true}], \"Category\": [{\"queryRef\": \"FactInternetSales.OrderDate\", \"active\": true}], \"Tooltips\": [{\"queryRef\": \"Sum(FactInternetSales.TotalProductCost)\", \"active\": true}, {\"queryRef\": \"Sum(FactInternetSales.SalesAmount)\", \"active\": true}, {\"queryRef\": \"FactInternetSales.OrderDate\", \"active\": true}]}, \"prototypeQuery\": {\"Version\": 2, \"From\": [{\"Name\": \"FactInternetSales\", \"Entity\": \"FactInternetSales\", \"Type\": 0}], \"Select\": [{\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"FactInternetSales\"}}, \"Property\": \"TotalProductCost\"}}, \"Function\": 0}, \"Name\": \"Sum(FactInternetSales.TotalProductCost)\", \"NativeReferenceName\": \"sum of TotalProductCost\"}, {\"Aggregation\": {\"Expression\": {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"FactInternetSales\"}}, \"Property\": \"SalesAmount\"}}, \"Function\": 0}, \"Name\": \"Sum(FactInternetSales.SalesAmount)\", \"NativeReferenceName\": \"sum of SalesAmount\"}, {\"Column\": {\"Expression\": {\"SourceRef\": {\"Source\": \"FactInternetSales\"}}, \"Property\": \"OrderDate\"}, \"Name\": \"FactInternetSales.OrderDate\", \"NativeReferenceName\": \"OrderDate\"}]}, \"drillFilterOtherVisuals\": true, \"hasDefaultSort\": true}}"}], "width": 1280.0}]}