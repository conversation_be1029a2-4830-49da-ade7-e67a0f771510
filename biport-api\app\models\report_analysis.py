import uuid
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.session import Base
from app.models.base import AuditMixin
from app.core.enums import ComplexityTypeEnum 

class ReportAnalysis(Base, AuditMixin):
    __tablename__ = "report_analysis"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    dashboard_count = Column(Integer, server_default=text("0"))
    worksheet_count = Column(Integer, server_default=text("0"))
    datasource_count = Column(Integer, server_default=text("0"))
    calculation_count = Column(Integer, server_default=text("0"))

    complexity_type = Column(Enum(ComplexityTypeEnum), nullable=True)

    report_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.report_details.id"), nullable=False)

    # Relationship
    report = relationship("ReportDetail", backref="analysis")
