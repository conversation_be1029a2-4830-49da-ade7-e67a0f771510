import re
from app.core import logger
from app.core.enums import (
    ChartType, GeneralKeys as GS, WorkSheet as WS
)

class ScatterPlot:
    @staticmethod
    def check_scatter_plot(worksheet):
        try:
            rows_text = worksheet.find('.//rows').text if worksheet.find('.//rows') is not None else ''
            cols_text = worksheet.find('.//cols').text if worksheet.find('.//cols') is not None else ''
            rows_details = re.findall(r'\[(.*?)\]', rows_text)
            cols_details = re.findall(r'\[(.*?)\]', cols_text)

            column_instances = worksheet.findall(".//column-instance")
            columns = worksheet.findall(".//column")

            column_instance_map = {
                inst.attrib.get("name"): inst.attrib.get("column")
                for inst in column_instances
            }

            column_type_map = {
                col.attrib.get("name"): col.attrib.get("type")
                for col in columns
            }

            column_agg_map = {
                inst.attrib.get("name"): inst.attrib.get("derivation")
                for inst in column_instances
            }

            quantitative_in_rows = []
            for row in rows_details:
                instance_key = f"[{row}]"
                instance_column = column_instance_map.get(instance_key)
                if instance_column and instance_column in column_type_map:
                    if column_type_map[instance_column] == "quantitative" and column_agg_map.get(instance_key) != "None":
                        quantitative_in_rows.append(row)

            quantitative_in_cols = []
            for col in cols_details:
                instance_key = f"[{col}]"
                instance_column = column_instance_map.get(instance_key)
                if instance_column and instance_column in column_type_map:
                    if column_type_map[instance_column] == "quantitative" and column_agg_map.get(instance_key) != "None":
                        quantitative_in_cols.append(col)

            if len(quantitative_in_rows) < 1 or len(quantitative_in_cols) < 1:
                return {"status": False, "chart_type": None}

            if (len(quantitative_in_rows) + len(quantitative_in_cols)) > 4:
                return {"status": False, "chart_type": None}

            return {"status": True, "chart_type": ChartType.SCATTER.value}

        except Exception as e:
            logger.error(f"Error in check_scatter_plot: {e}")
            return {"status": False, "chart_type": None, "error": str(e)}