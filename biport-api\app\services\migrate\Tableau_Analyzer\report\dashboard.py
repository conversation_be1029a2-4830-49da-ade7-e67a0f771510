from app.core import logger
from app.core.enums import GeneralKeys, TableauXMLTags

def calculate_dimensions(x= None, y =None, h= None, w = None):
    return {
        "height": float(h) * 720.00 / 100000.00 if h else 720.0,
        "width": float(w) * 1280.00 / 100000.00 if w else 1280.0,
        "x": float(x) * 1280.00 / 100000.00 if x else 0.00,
        "y": float(y) * 720.00 / 100000.00 if y  else 0.00,
        "z": 0.00
    }

def get_worksheet_names(worksheets):
    """
    Extracts worksheet names from a list of XML <worksheet> elements.
    """
    return [worksheet.attrib.get("name") for worksheet in worksheets]

def find_zones_by_name(zones, names):
    """ Gives Work sheets dimentions in Dashboards  """
    names_wise_zones = {}
    for i in zones:
        zone_dimensions = {}
        attributes = i.attrib
        if not any(
            val in GeneralKeys.NOT_INCLUDING_WHILE_FETCHING_DIMENTIONS.value 
            for val in attributes.keys()) and i.get(TableauXMLTags.NAME.value
        ) in names:
            x = i.get(GeneralKeys.X.value)
            y = i.get(GeneralKeys.Y.value)
            h = i.get(GeneralKeys.H.value)
            w = i.get(GeneralKeys.W.value)
            zone_dimensions = calculate_dimensions(x, y, h, w)
            zone_dimensions[TableauXMLTags.TAB_ORDER.value] = 1
            names_wise_zones[i.get(TableauXMLTags.NAME.value)] = zone_dimensions
    return names_wise_zones

def get_dashboard_visual_container_visuals():
    visual_container =[]
    return visual_container


def process_worksheets_in_dashboards(root):
    """
    Parses Tableau XML root and constructs dashboard visual container JSONs.
    """
    logger.info(f"=== Started dashboard migration =======")
    dashboard_analyzed_data = {}
    dashboards = root.findall(TableauXMLTags.DASHBOARD.value)
    worksheets = root.findall(TableauXMLTags.WORKSHEET.value)
    for dashboard in dashboards:
        zones =  dashboard.findall(TableauXMLTags.ZONE.value)
        dashboard_analyzed_data[dashboard.get(TableauXMLTags.NAME.value)] = find_zones_by_name(zones,get_worksheet_names(worksheets))
    
    return dashboard_analyzed_data
