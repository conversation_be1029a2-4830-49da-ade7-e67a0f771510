from app.core import logger
from app.core.enums import (
    ChartType, GeneralKeys as GS, WorkSheet as WS
)
from app.core.regex_enums import Regex as RE
from .common import get_quantitative_columns

class TreeMap:
    @staticmethod
    def check_tree_map(worksheet):
        try:
            rows = worksheet.find(WS.ROWS.value)
            cols = worksheet.find(WS.COLS.value)

            rows_text = rows.text.strip() if rows is not None and rows.text else None
            cols_text = cols.text.strip() if cols is not None and cols.text else None

            if rows_text or cols_text:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            quantitative_columns = get_quantitative_columns(worksheet)
            if not quantitative_columns:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            column_instances = worksheet.findall(WS.DS_COL_INSTANCES.value)

            measures = [col for col in quantitative_columns]
            dimensions = [
                col for col in column_instances
                if col.attrib[WS.COLUMN.value] not in [
                    m[WS.COLUMN.value][WS.NAME.value] for m in measures
                ]
            ]

            if len(quantitative_columns) < 1 or len(quantitative_columns) > 2:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}
            if not dimensions:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            encodings = worksheet.findall(WS.ALL_ENCODINGS.value)
            if len(encodings) < 2:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            size_tag = next((tag for tag in encodings if tag.tag == GS.SIZE.value), None)
            if size_tag is None:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            size_column_full = size_tag.get(WS.COLUMN.value)
            if not size_column_full:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            size_column_name = size_column_full.split(RE.DOT.value)[-1]
            size_column = next(
                (
                    col for col in measures
                    if col[GS.COLUMN_INSTANCE.value][WS.NAME.value] == size_column_name
                ),
                None
            )

            if not size_column:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            return {
                GS.STATUS.value: True,
                GS.CHART_TYPE.value: ChartType.TREE_MAP.value
            }

        except Exception as e:
            logger.error(f"Error in check_tree_map: {e}")
            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None,
                GS.ERROR.value: str(e)
            }
