from app.services.organization.organization_service import OrganizationDetailsService
from typing import Dict
from app.core import ServiceResponse

class OrganizationProcessor:
    @staticmethod
    def create_organization_details(organization) -> ServiceResponse:
        """Process the creation of a new organization."""
        
        return OrganizationDetailsService.execute(OrganizationDetailsService.create_organization_service, organization)
