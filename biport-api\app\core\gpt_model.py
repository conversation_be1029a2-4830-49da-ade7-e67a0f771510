from app.core.config import OpenAIConfig
from openai import APIConnectionError, AuthenticationError
from app.core.logger_setup import logger

openai_config = OpenAIConfig()
client = openai_config.get_openai_client()

def gpt_model(model, system_message, user_message, response_format):
    try:
        response = client.chat.completions.create(
            model = model,
            messages=[
                {
                    "role" : "system",
                    "content" : [
                            {
                            "type": "text",
                            "text": system_message
                            }
                        ]
                },
                {
                    "role" : "user",
                    "content" : [
                            {
                            "type": "text",
                            "text": user_message
                            }
                        ]
                }
            ],
            response_format={
                "type": response_format
            }
        )

        return response.choices[0].message.content
    except APIConnectionError as e:
        logger.error(f"Issue in connecting to OpenAI API: {str(e)}")
        raise ValueError('Issue in connecting to OpenAI API')
    except AuthenticationError as e:
        logger.error(f"OpenAI key or token was invalid, expired, or revoked.: {str(e)}")
        raise ValueError('OpenAI key or token was invalid, expired, or revoked.')  
