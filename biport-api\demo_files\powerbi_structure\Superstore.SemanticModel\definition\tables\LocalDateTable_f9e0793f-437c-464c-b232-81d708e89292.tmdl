table LocalDateTable_f9e0793f-437c-464c-b232-81d708e89292
	isHidden
	showAsVariationsOnly
	lineageTag: 873589f5-2e7c-4cde-9ffc-80446aa31921

	column Date
		dataType: dateTime
		isHidden
		lineageTag: e7939f24-ce6b-4f40-bf6a-e3ea9e1e61b6
		dataCategory: PaddedDateTableDates
		summarizeBy: none
		isNameInferred
		sourceColumn: [Date]

		annotation SummarizationSetBy = User

	column Year = YEAR([Date])
		dataType: int64
		isHidden
		lineageTag: 8e4f82bc-0e4a-4d0c-886c-a586d20d37e4
		dataCategory: Years
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Year

	column MonthNo = MONTH([Date])
		dataType: int64
		isHidden
		lineageTag: 4dd45e75-281d-4e45-af73-251bd9178ff5
		dataCategory: MonthOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = MonthNumber

	column Month = FORMAT([Date], "MMMM")
		dataType: string
		isHidden
		lineageTag: 7d347a8f-c3f3-4b68-97d9-c178dfc78bc2
		dataCategory: Months
		summarizeBy: none
		sortByColumn: MonthNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Month

	column QuarterNo = INT(([MonthNo] + 2) / 3)
		dataType: int64
		isHidden
		lineageTag: fc07d8e9-0a9f-4d36-b151-2507f9fcf7f7
		dataCategory: QuarterOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = QuarterNumber

	column Quarter = "Qtr " & [QuarterNo]
		dataType: string
		isHidden
		lineageTag: e601d998-6b6a-4d7d-8803-09e7c072820b
		dataCategory: Quarters
		summarizeBy: none
		sortByColumn: QuarterNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Quarter

	column Day = DAY([Date])
		dataType: int64
		isHidden
		lineageTag: cd91f95e-54cd-42c5-ab10-8ee347d98193
		dataCategory: DayOfMonth
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Day

	hierarchy 'Date Hierarchy'
		lineageTag: 87ae1efb-7b86-4f8e-bb48-0599dfaf633d

		level Year
			lineageTag: c6a4e30d-1fa8-4fc8-9b7a-3de80c5c9199
			column: Year

		level Quarter
			lineageTag: df143c71-8fc7-4205-a174-447311e58b2c
			column: Quarter

		level Month
			lineageTag: 9bbf1d8d-52fb-464b-ad7b-1d568f96cb17
			column: Month

		level Day
			lineageTag: 3bb6face-189b-4518-83d1-48f8c943b62a
			column: Day

		annotation TemplateId = DateHierarchy

	partition LocalDateTable_f9e0793f-437c-464c-b232-81d708e89292 = calculated
		mode: import
		source = Calendar(Date(Year(MIN('Orders'[Ship Date])), 1, 1), Date(Year(MAX('Orders'[Ship Date])), 12, 31))

	annotation __PBI_LocalDateTable = true

