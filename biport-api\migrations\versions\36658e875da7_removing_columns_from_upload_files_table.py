"""removing columns from upload files table 

Revision ID: 36658e875da7
Revises: 4ac6f8d5e3fe
Create Date: 2025-06-11 12:52:40.977031

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '36658e875da7'
down_revision: Union[str, None] = '4ac6f8d5e3fe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.drop_column('upload_files_report_details', 'report_name')
    op.drop_column('upload_files_report_details', 'server_id')


def downgrade() -> None:
    op.add_column('upload_files_report_details',sa.Column('server_id', sa.UUID(), sa.<PERSON>ey("server_details.id"), nullable=True))
    op.add_column('upload_files_report_details', sa.Column('report_name', sa.String(), nullable=False))
