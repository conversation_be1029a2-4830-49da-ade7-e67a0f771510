import xml.etree.ElementTree as ET
from .table_columns_handler import fetch_table_columns_data
from .relationships_handler import extract_tableau_relationship_data
from app.core.enums import Datasource

async def parse_xml(twb_file_path: str) -> ET.Element:
    try:
        tree = ET.parse(twb_file_path)
        return tree.getroot()
    except ET.ParseError as e:
        raise ValueError(f"Invalid XML: {str(e)}")

async def process_tableau_datasources(twb_file_path):
    tree = ET.parse(twb_file_path)
    root = tree.getroot()
    datasources = root.find(Datasource.DATASOURCES.value).findall(Datasource.DS.value)
    table_column_data = await fetch_table_columns_data(datasources)
    relationships_data = await extract_tableau_relationship_data(datasources)
    return table_column_data, relationships_data