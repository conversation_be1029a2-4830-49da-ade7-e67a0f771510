import uuid, json
from ..core import process_title_layout, to_list, create_expr
from app.services.migrate.Tableau_Analyzer.report.dashboard import calculate_dimensions
from app.core.templates import title_textbox, text_box_json
from app.core.logger_setup import logger
from app.core.enums import GeneralKeys, PowerBIObjectKeys, PowerBITemplateKeys, TableauXMLTags
from app.services.migrate.PowerBI.report.get_singlevisual_data import get_uuid

def process_style_format_data(format_data):
    background_color, border_color = None, None
    attr_list = []
    for format in format_data:
        if not format:
            continue
        format_attr = format.get("@attr")
        attr_list.append(format_attr)
        if format_attr == "background-color":
            background_color = format.get("@value")
            background_color = background_color[:7] if len(background_color) > 7 else background_color
        if format_attr == "border-color":
            border_color = format.get("@value")
        if "border-style" in attr_list and "border-color" not in attr_list:
            border_color = "#000000"

    return border_color, background_color

def process_style_data(style_data= None, style_type = None, zone_style = None):
    background_color, border_color = None, None
    if style_data:
        style_rule = to_list(style_data.get("style-rule"))
        for rule in style_rule:
            if rule.get("@element") == style_type:
                format_list = to_list(rule.get("format"))
                border_color, background_color = process_style_format_data(format_list)
    if zone_style:
        border_color, background_color = process_style_format_data(zone_style)

    if border_color:
        border_config = [{"properties": {"color": {"solid": {"color": create_expr(f"'{border_color}'")}},"show": create_expr("true")}}]
    else:
        border_config = [{"properties": {"show": create_expr("false")}}]
    if background_color:
        background_config = [{"properties": {"color": {"solid": {"color": create_expr(f"'{background_color}'")}},"show": create_expr("true")}}]
    else:
        background_config = [{"properties": {"show": create_expr("false")}}]

    return border_config, background_config


def get_title_textbox(worksheet_title_layout, style_data = None, zone_style = None, is_dashboard= False):
    overall_result = []
    alignment = "left"
    text_paragraphs = []
    border_config = [{"properties": {"show": create_expr("false")}}]
    background_config = [{"properties": {"show": create_expr("false")}}]
    if isinstance(worksheet_title_layout, str):
        text_value = worksheet_title_layout
        fontSize = "18pt" if is_dashboard else "14pt"
        text_runs = [{PowerBIObjectKeys.VALUE.value: text_value, "textStyle": {"fontSize": fontSize, "fontFamily": "Calibri"}}]
        text_paragraphs.append({"textRuns": text_runs, "horizontalTextAlignment": alignment})

    else:
        worksheet_title_layout = to_list(worksheet_title_layout)
        for title_data in worksheet_title_layout:
            if title_data == "\u00c6":
                continue

            elif isinstance(title_data, str):
                text_value = title_data
                font_size = "18pt" if is_dashboard else "14pt"
                text_runs = [{PowerBIObjectKeys.VALUE.value: text_value, "textStyle": {"fontSize": font_size, "fontFamily": "Calibri"}}]
                text_paragraphs.append({"textRuns": text_runs, "horizontalTextAlignment": alignment})
            
            else:
                if title_data.get('#text') == "\u00c6":
                    continue
                text_value = title_data.get('#text')
                font_size = title_data.get('@fontsize')
                text_color = title_data.get('@fontcolor')
                font_name = title_data.get('@fontname')
                font_weight = title_data.get('@bold')
                font_style = title_data.get('@italic')
                font_decoration = title_data.get('@underline')
                font_alignment = title_data.get('@fontalignment')

                text_runs = [{PowerBIObjectKeys.VALUE.value: text_value, "textStyle": {}}]
                if font_size:
                    text_runs[0]["textStyle"]["fontSize"] = f"{font_size}pt"
                else:
                    text_runs[0]["textStyle"]["fontSize"] = "18pt" if is_dashboard else "15pt"
                if text_color:
                    text_runs[0]["textStyle"]["color"] = text_color
                if font_name:
                    text_runs[0]["textStyle"]["fontFamily"] = font_name if "Tableau" not in font_name else "Calibri"
                if font_weight and font_weight == "true":
                    text_runs[0]["textStyle"]["fontWeight"] = PowerBIObjectKeys.BOLD.value
                if font_style and font_style == "true":
                    text_runs[0]["textStyle"]["fontStyle"] = "italic"
                if font_decoration and font_decoration == "true":
                    text_runs[0]["textStyle"]["textDecoration"] = PowerBIObjectKeys.UNDERLINE.value
                if font_alignment:
                    if font_alignment == PowerBIObjectKeys.ONE.value:
                        alignment = PowerBIObjectKeys.CENTER.value
                    elif font_alignment == PowerBIObjectKeys.TWO.value:
                        alignment = "right"
                    else:
                        alignment = "left"
                text_paragraphs.append({"textRuns": text_runs, "horizontalTextAlignment": alignment})

    style_type = "dash-title" if is_dashboard else "title"

    if style_data:
        border_config, background_config = process_style_data(style_data = style_data, style_type = style_type, zone_style = None)

    if zone_style:
        border_config, background_config = process_style_data(style_data = None, style_type = None, zone_style = zone_style)
    
    title_json = {
        "visual_config_name" : f'{str(uuid.uuid4()).replace("-",PowerBIObjectKeys.EMPTY.value)[:20]}', 
        "text_paragraphs":json.dumps(text_paragraphs),
        "background_list" : json.dumps(background_config),
        "border_list" : json.dumps(border_config)
    }
    if is_dashboard:
        dimension_json = {"height": 50, "width": 1277, "x":0.00, "y" : 0.00, "z": 0}
        result = title_textbox.format(**title_json, **dimension_json)
        overall_result.append({"config":result, **dimension_json})
    else:
        return {"config":title_json, "template":title_textbox}
    return overall_result


def get_objects(text_value, fsize, fdec, fw, fs, tplace):
    data = {"objects": {"general": [{"properties": {"paragraphs": [{"textRuns":[{PowerBIObjectKeys.VALUE.value: text_value,"textStyle": {"fontFamily": "Calibri","fontSize": f"{fsize}"}}]}]}}]}}
    text_style = data["objects"]["general"][0]["properties"]["paragraphs"][0]["textRuns"][0]["textStyle"]
    paragraph = data["objects"]["general"][0]["properties"]["paragraphs"][0]
    if fdec:
        text_style["textDecoration"] = fdec
    if fw:
        text_style["fontWeight"] = fw
    if fs:
        text_style["fontStyle"] = fs
    if tplace != "left":
        paragraph["horizontalTextAlignment"] = tplace
    return data


def get_vc_objects_of_text_box(bg_color=None, border_color=None):

    if bg_color: backgorung_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"true"}}},"color":{"solid":{"color":{"expr":{"Literal":{"Value":f"'{bg_color}'"}}}}}}}]
    else: backgorung_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}]
    if border_color: border_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"true"}}},"color":{"solid":{"color":{"expr":{"Literal":{"Value":f"'{border_color}'"}}}}}}}]
    else: border_config = [{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}]
    data={"vcObjects":{"border":border_config,"background":backgorung_config,"title":[{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}]}}
    return data

def process_text_box_in_worksheets(worksheet = None, text = None):
    text_box_result = {}
    background_color = PowerBIObjectKeys.PURE_WHITE.value
    font_style = PowerBIObjectKeys.ITALIC.value
    text_decoration = PowerBIObjectKeys.UNDERLINE.value
    font_weight = PowerBIObjectKeys.BOLD.value
    tplace = PowerBIObjectKeys.CENTER.value
    font_size = 24
    if worksheet and not text:
        text_value = worksheet.find(TableauXMLTags.TEXT_BOX_TEXT.value).get(PowerBITemplateKeys.FORMULA.value)
        style_rules = worksheet.findall(TableauXMLTags.STYLE_RULES.value)
        for style_rule in style_rules:
            formats = style_rule.findall(TableauXMLTags.FORMAT_TAG.value)
            for each_format in formats:
                if each_format.get(GeneralKeys.ATTR.value) == PowerBIObjectKeys.BG_COLOR.value and style_rule.get(TableauXMLTags.ELEMENT.value) == PowerBIObjectKeys.HEADER.value: background_color = each_format.get(PowerBIObjectKeys.VALUE.value)
                if each_format.get(GeneralKeys.ATTR.value) == PowerBIObjectKeys.FONT_STYLE.value and style_rule.get(TableauXMLTags.ELEMENT.value) == PowerBIObjectKeys.LABEL.value: font_style = each_format.get(PowerBIObjectKeys.VALUE.value)
                if each_format.get(GeneralKeys.ATTR.value) == PowerBIObjectKeys.TEXT_DECORATION.value and style_rule.get(TableauXMLTags.ELEMENT.value) == PowerBIObjectKeys.LABEL.value: text_decoration = each_format.get(PowerBIObjectKeys.VALUE.value)
                if each_format.get(GeneralKeys.ATTR.value) == PowerBIObjectKeys.FONT_WEIGHT.value and style_rule.get(TableauXMLTags.ELEMENT.value) == PowerBIObjectKeys.LABEL.value: font_weight = each_format.get(PowerBIObjectKeys.VALUE.value)
        objects = get_objects(text_value,font_size,text_decoration,font_weight,font_style,tplace)
        vc_objects = get_vc_objects_of_text_box(background_color)
    else:
        text_value = text
        objects = get_objects(text_value,font_size,text_decoration,font_weight,font_style,tplace)
        vc_objects = get_vc_objects_of_text_box(background_color)

    text_box_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = worksheet.get(GeneralKeys.NAME.value)
    text_box_result[GeneralKeys.TEXT_BOX_VISUAL.value] = generate_text_box_visual(objects, vc_objects)

    return text_box_result



def get_objects_text_box(text_value, fsize, fdec, fw, fs, tplace):
    data = {"objects": {"general": [{"properties": {"paragraphs": [{"textRuns":[{PowerBIObjectKeys.VALUE.value: text_value,"textStyle": {"fontFamily": "Calibri","fontSize": f"{fsize}"}}]}]}}]}}
    text_style = data["objects"]["general"][0]["properties"]["paragraphs"][0]["textRuns"][0]["textStyle"]
    paragraph = data["objects"]["general"][0]["properties"]["paragraphs"][0]
    if fdec:
        text_style["textDecoration"] = fdec
    if fw:
        text_style["fontWeight"] = fw
    if fs:
        text_style["fontStyle"] = fs
    if tplace != "left":
        paragraph["horizontalTextAlignment"] = tplace
    return data

def generate_text_box_visual(objects, vc_objects, x=None, y=None, h=None, w=None):
    return {"name":get_uuid(),"layouts":[{"id":0,"position":calculate_dimensions(x,y,h,w)}],"singleVisual":{"visualType":"textbox","drillFilterOtherVisuals":True,"objects":objects.get("objects")if objects else {},"vcObjects":vc_objects.get("vcObjects") if vc_objects else {}}}



def process_text_boxes_in_dashboard(dashboards):
    """
    Return Text Boxes in dictionary Format like
    {
        "Dashboard Name" : [TextBoxes vsiual Jsons],
        ..... : ....
    }
    """
    text_boxes_in_dashboard = {}
    for dashbaord in dashboards:
        text_boxes_in_this_dashboard = []
        zones = dashbaord.findall(TableauXMLTags.ZONE.value)
        for zone in zones:
            zone_details = {}
            if zone.get(GeneralKeys.TYPE2.value) == TableauXMLTags.TEXT.value:
                x = zone.get(GeneralKeys.X.value)
                y = zone.get(GeneralKeys.Y.value)
                width = zone.get(GeneralKeys.W.value)
                height = zone.get(GeneralKeys.H.value)
                dimensions = calculate_dimensions(x, y, height, width)
                formatted_run = zone.findtext(TableauXMLTags.FORMATTED_TEXT_RUN.value)
                if not formatted_run: continue
                text_attributes = zone.find(TableauXMLTags.FORMATTED_TEXT_RUN.value).attrib
                font_weight = PowerBIObjectKeys.BOLD.value if PowerBIObjectKeys.BOLD.value in text_attributes.keys() else PowerBIObjectKeys.EMPTY.value
                font_size = text_attributes.get(PowerBIObjectKeys.FONT_SIZE.value)+PowerBIObjectKeys.PT.va if PowerBIObjectKeys.FONT_SIZE.value in text_attributes.keys() else "14pt"
                text_place_value = (
                    PowerBIObjectKeys.RIGHT.value
                    if text_attributes.get(PowerBIObjectKeys.FONT_ALIGNMENT.value) == PowerBIObjectKeys.TWO.value
                    else PowerBIObjectKeys.CENTER.value if text_attributes.get(PowerBIObjectKeys.FONT_ALIGNMENT.value) == PowerBIObjectKeys.ONE.value
                    else PowerBIObjectKeys.LEFT.value
                )
                font_decoration = True if PowerBIObjectKeys.UNDERLINE.value in text_attributes.keys() else PowerBIObjectKeys.EMPTY.value
                fs = PowerBIObjectKeys.EMPTY.value
                objects = get_objects_text_box(formatted_run,font_size,font_decoration,font_weight,fs,text_place_value)
                vcobjects = get_vc_objects_of_text_box()
                text_box_visual = generate_text_box_visual(objects=objects,vc_objects=vcobjects, x=x,y=y,h=height,w=width)
                text_boxes_in_this_dashboard.append(text_box_visual)
            text_boxes_in_dashboard[dashbaord.get(TableauXMLTags.NAME.value)] = text_boxes_in_this_dashboard

    return text_boxes_in_dashboard
