from ..core import *
from app.core import area_chart_discrete_json
from app.core.logger_setup import logger
import json, uuid

def get_areachart_discrete_report(rows, cols,pane_encodings,table_column_data,datasource_col_list,worksheet_name,logger_id):
    try:
        area_discrete_result=[]
        from_list,select_list,cat_ref_list,y_ref_list,series_ref_list,table_names,select_list_no_doops,column_properties_json=[],[],[],[],[],[],[],{}
        row_list=get_fields_data(rows)
        col_list=get_fields_data(cols)
        series = pane_encodings.get('color', {}).get('@column')
        series_list=get_fields_data(series)

        #CATEGORY REF LIST
        for rows in col_list:
            rows=rows.strip()
            cat_ref_list,cat_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,cat_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            column_properties_json.update(get_column_properties_json(*list(get_calc_filter_column(rows, table_column_data,datasource_col_list))))
            if cat_tab_name not in table_names: table_names.append(cat_tab_name)
        
        for rows in row_list:
            rows=rows.strip()
            y_ref_list,y_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,y_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            column_properties_json.update(get_column_properties_json(*list(get_calc_filter_column(rows, table_column_data,datasource_col_list))))
            if y_tab_name not in table_names: table_names.append(y_tab_name)

        for rows in series_list:
            rows=rows.strip()
            series_ref_list,series_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,series_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            if series_tab_name not in table_names: table_names.append(series_tab_name)

        from_list=get_from_list(table_names)

        for i in select_list:
            if i not in select_list_no_doops:select_list_no_doops.append(i)

        areachart_json = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "cat_ref_list" : json.dumps(cat_ref_list),
            "y_ref_list" : json.dumps(y_ref_list),
            "series_ref_list" : json.dumps(series_ref_list),
            "from_list" : json.dumps(from_list),
            "column_properties_json":json.dumps(column_properties_json),
            "select_list" :json.dumps(select_list_no_doops)
        }
        area_discrete_result.append({"config":areachart_json,"template":area_chart_discrete_json})
        return area_discrete_result
    except Exception as e:
        logger.error(f"{logger_id}---Error in generating Area discrete visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating Area discrete visual for {worksheet_name} - {str(e)}")
