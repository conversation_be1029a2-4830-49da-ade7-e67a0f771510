"""Rename column in data sources table

Revision ID: 4fbb3a4a530c
Revises: 1ec009e80d47
Create Date: 2025-04-04 14:31:17.567563

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4fbb3a4a530c'
down_revision: Union[str, None] = '1ec009e80d47'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.execute("ALTER TABLE data_sources RENAME COLUMN encoded_data TO data_sources")

def downgrade():
    op.execute("ALTER TABLE data_sources RENAME COLUMN data_sources TO encoded_data")
