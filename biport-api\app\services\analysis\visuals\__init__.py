from .map import Map
from .donut import <PERSON><PERSON><PERSON><PERSON>
from .bubble_chart import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .waterfall import Water<PERSON><PERSON><PERSON>
from .tree_map import <PERSON><PERSON>ap
from .cards import <PERSON><PERSON><PERSON>
from .polygon import Polygon
from .table import Table
from .combo_chart import <PERSON><PERSON><PERSON><PERSON>
from .scatter import ScatterPlot
from .bullet import <PERSON><PERSON><PERSON><PERSON>
from .bar import Bar, Direct
from .line import Line

check_functions = [
    Map.check_map,
    DonutChart.check_donut_chart,
    BubbleChart.check_bubble_chart,
    WaterfallChart.check_waterfall_chart,
    TreeMap.check_tree_map,
    CardChart.check_card_chart,
    Polygon.check_polygon,
    Table.check_table,
    ComboChart.check_combo_chart,
    ScatterPlot.check_scatter_plot,
    BulletChart.check_bullet_chart,
    Bar.check_bar_chart,
    Line.check_line_chart,
]

(
    Direct.get_direct_chart_type
)







