from datetime import datetime
from sqlalchemy import Column, DateTime, String, Integer
from app.core import scoped_context, Base
from typing import Optional
from passlib.hash import bcrypt

from app.core.exceptions import AuthenticationError, ConflictError


class UserOld(Base):
    __tablename__ = "users"
    id: int = Column(Integer, primary_key=True, autoincrement=True)
    name: str = Column(String(50), nullable=False)
    email: str = Column(String(50), nullable=False, unique=True)
    organization_name: str = Column(String(30), nullable=False)
    phone_number: str = Column(String(10), nullable=False, unique=True)
    designation: str = Column(String(30), nullable=False)
    reports_number: Optional[str] = Column(String, nullable=True)
    password: str = Column(String(128), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class UserManager:
    def __init__(
        self,
        name: str,
        email: str,
        organization_name: str,
        phone_number: str,
        designation: str,
        reports_number: Optional[str],
        password: str,
    ) -> None:
        self.name = name
        self.email = email
        self.organization_name = organization_name
        self.phone_number = phone_number
        self.designation = designation
        self.reports_number = reports_number
        self.password = password

    def add_user(self) -> None:
        """Add a new user or raise if email/phone already exists."""
        with scoped_context() as session:
            existing_user = (
                session.query(UserOld)
                .filter((UserOld.email == self.email) | (UserOld.phone_number == self.phone_number))
                .first()
            )

            if existing_user:
                if existing_user.email == self.email:
                    raise ConflictError("Email already exists")
                if existing_user.phone_number == self.phone_number:
                    raise ConflictError("Phone number already exists")

            new_user = UserOld(
                name=self.name,
                email=self.email,
                organization_name=self.organization_name,
                phone_number=self.phone_number,
                designation=self.designation,
                reports_number=self.reports_number,
                password=bcrypt.hash(self.password),
            )
            session.add(new_user)
            session.commit()

    @staticmethod
    def get_user_by_email(email: str) -> Optional[UserOld]:
        """Fetch a user by their email."""
        with scoped_context() as session:
            return session.query(UserOld).filter_by(email=email).first()

    @staticmethod
    def login_user(email: str, password: str) -> Optional[UserOld]:
        user = UserManager.get_user_by_email(email)
        if user is None:
            raise AuthenticationError("User not found.")
        if bcrypt.verify(password, user.password):
            return user
        raise AuthenticationError("Incorrect password. Please try again.")

    @staticmethod
    def check_user_exists(email: str) -> Optional[UserOld]:
        return UserManager.get_user_by_email(email)

    @staticmethod
    def update_user_password(email: str, new_password: str) -> UserOld:
        with scoped_context() as session:
            detached_user = UserManager.get_user_by_email(email)
            if not detached_user:
                raise AuthenticationError(f"User with email {email} not found.")
            user = session.merge(detached_user)
            user.password = bcrypt.hash(new_password)
            session.commit()
            session.refresh(user)
            return user

    @staticmethod
    def check_user_existence_by_id(user_id: int) -> Optional[UserOld]:
        with scoped_context() as session:
            return session.query(UserOld).filter_by(id=user_id).first()
