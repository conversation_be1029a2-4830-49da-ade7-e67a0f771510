from app.core.enums import ChartType, VisualRequest
from app.services.migrate.Tableau_Analyzer.report import (
    generate_projections_data, extract_multiple_encodings_data,
    remove_duplicate_fields
)
from app.core.enums import (
    TableauXMLTags, PowerBITemplateKeys,
    PowerBIReportKeys, PowerBIChartTypes
)


def process_bar_chart(request: VisualRequest):
    barchart_result = {}
    panes = request.panes
    rows = request.rows
    cols = request.cols
    calculations_related_data = request.calculations_related_data
    table_column_data = request.table_column_data
    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )
    unique_tooltips = remove_duplicate_fields(
        encodings[TableauXMLTags.TOOLTIP.value],
        encodings[TableauXMLTags.LOD.value],
        encodings[TableauXMLTags.TEXT.value],
        rows,
        cols
    )
    bar_chart_field_mappings = {
        PowerBIReportKeys.Y.value: (
            cols 
            if (request.visual_type in [ChartType.STACKED_HORIZONTAL_BAR.value, ChartType.HORIZONTAL_BAR.value])
            else rows
        ),
        PowerBIReportKeys.CATEGORY.value: (
            rows
            if (request.visual_type in [ChartType.STACKED_HORIZONTAL_BAR.value, ChartType.HORIZONTAL_BAR.value])
            else cols
        ),
    }
    if unique_tooltips: bar_chart_field_mappings[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips

    projections_data = generate_projections_data(table_column_data, calculations_related_data, bar_chart_field_mappings)


    barchart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = (
        PowerBIChartTypes.CLUSTERED_BAR.value 
        if request.visual_type == ChartType.HORIZONTAL_BAR.value else PowerBIChartTypes.CLUSTERED_COLUMN.value 
        if request.visual_type == ChartType.VERTICAL_BAR.value else PowerBIChartTypes.CLUSTERED_BAR.value
        if request.visual_type == ChartType.HORIZONTAL_BAR.value else PowerBIChartTypes.CLUSTERED_COLUMN.value
    )
    barchart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    barchart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data

    return barchart_result
