import os
import re
import shutil
import time
from app.core.config import S3<PERSON>onfig,PathConfig, logger
from app.services.semantic_model.excel.process_calculations_excel import get_calculations_data_excel
import xml.etree.ElementTree as ET

from app.services.semantic_model.excel.relationships_excel import extract_relationships_excel, write_relationships_file_excel
from app.services.semantic_model.excel.tables_excel import update_tmdl_files_from_json_excel
from app.services.semantic_model.excel.tables_excel import create_parameters_model_excel, extract_table_columns_excel, write_table_model_files_excel

s3_config = S3Config()
s3_client = s3_config.get_s3_client()
bucket_name = s3_config.bucket_name

async def semantic_model_generation_excel(organization_name, user_email, twb_files_path, local_dir, process_id, extracted_path):
    if not twb_files_path:
        raise ValueError(f" - No TWB files provided for semantic model generation.")

    logger.info(f" - Starting semantic model generation for {len(twb_files_path)} TWB file(s).")
    
    download_links = []
    for idx, twb_file in enumerate(twb_files_path, start=1):
        try:
            logger.info(f" - Processing file {idx}/{len(twb_files_path)}: {os.path.basename(twb_file)}")
            download_link = await process_single_file_excel(
                twb_file, local_dir, organization_name, user_email, process_id, extracted_path
            )
            if download_link:
                logger.info(f" - Successfully processed {os.path.basename(twb_file)}. Download link: {download_link}")
                download_links.append(download_link)
            else:
                logger.info(f" - WARNING: No download link generated for {os.path.basename(twb_file)}.")
        except Exception as e:
            logger.info(f" - ERROR processing {os.path.basename(twb_file)}: {str(e)}")
            continue

    if not download_links:
        raise Exception(f" - No semantic models were generated successfully.")

    logger.info(f" - Semantic model generation completed for all files.")
    
    return download_links

async def process_single_file_excel(twb_file, local_dir, organization_name, user_email, process_id, extracted_path):
    twb_filename = os.path.basename(twb_file)
    start_time = time.time()

    logger.info(f" - Started processing file: {twb_filename}")
    logger.info(f" - File timestamp: {start_time}")

    try:
        semantic_model_path = extracted_path
        if not os.path.exists(semantic_model_path):
            raise FileNotFoundError(f"Semantic model path does not exist: {semantic_model_path}")
        logger.info(f" - Semantic model base path: {semantic_model_path}")

        short_name = os.path.splitext(twb_filename)[0][:2]
        powerbi_output_dir = os.path.join(local_dir, f'{short_name}_model_v2')
        os.makedirs(powerbi_output_dir, exist_ok=True)
        logger.info(f" - Created output directory: {powerbi_output_dir}")

        definition_folder_path = copy_powerbi_structure_excel(source_path=semantic_model_path, dest_path=powerbi_output_dir)
        logger.info(f" - Copied PowerBI structure. Definition folder path: {definition_folder_path}")

        tables_output_dir = os.path.join(definition_folder_path, 'tables')
        relationships_file = os.path.join(definition_folder_path, 'relationships.tmdl')
        models_file = os.path.join(definition_folder_path, 'models.tmdl')
        template_file_path = PathConfig.dateTable_template_path

        logger.info(f" - Output paths: tables={tables_output_dir}, relationships={relationships_file}, models={models_file}")

        json_data = get_calculations_data_excel(twb_file)
        logger.info(f" - Extracted calculations data from TWB file.")

        extract_models_and_columns_with_lineage_excel(
            twb_file, tables_output_dir, models_file, relationships_file, template_file_path, json_data
        )
        logger.info(f" - Extracted models and columns with lineage successfully.")

        zip_path = shutil.make_archive(powerbi_output_dir, 'zip', powerbi_output_dir)
        zip_filename = os.path.basename(zip_path)
        unique_folder = f'{organization_name}/{user_email}'
        s3_key = f'{unique_folder}/{process_id}/semantic_model/{zip_filename}'

        await s3_config.upload_to_s3(zip_path, s3_key)
        presigned_url = await s3_config.generate_presigned_url(s3_key)

        download_link = {
            "file": zip_filename,
            "download_url": presigned_url
        }

        # download_link = upload_and_generate_download_link_excel(
        #     powerbi_output_dir, organization_name, user_email, process_id
        # )
        logger.info(f" - Uploaded semantic model package. Download link: {download_link}")

        end_time = time.time()
        logger.info(f" - Completed processing {twb_filename} in {end_time - start_time:.2f} seconds.")
        return download_link

    except Exception as e:
        logger.error(f" - Error processing {twb_filename}: {str(e)}", exc_info=True)
        raise

def copy_powerbi_structure_excel(source_path, dest_path):
    logger.info("dest_path_at_beginning "+dest_path)
    start_time = time.time()
    logger.info(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(start_time))}] - Starting copy from {source_path} to {dest_path}")

    if not os.path.exists(source_path):
        raise FileNotFoundError(f"Source path does not exist: {source_path}")

    os.makedirs(dest_path, exist_ok=True)

    file_count = 0
    for root, dirs, files in os.walk(source_path):
        if '__MACOSX' in dirs:
            dirs.remove('__MACOSX')

        for filename in files:
            if filename.endswith('.twb') or filename == 'models.tmdl':
                continue

            src_path = os.path.join(root, filename)
            relative_path = os.path.relpath(src_path, source_path)
            dest_file_path = os.path.join(dest_path, relative_path)

            os.makedirs(os.path.dirname(dest_file_path), exist_ok=True)
            shutil.copy2(src_path, dest_file_path)
            print(f"Copied file: {src_path} -> {dest_file_path}")
            file_count += 1

    print(f"Total files copied (excluding .twb): {file_count}")

    semantic_model_path = None
    for root, dirs, _ in os.walk(dest_path):
        for dir_name in dirs:
            if '.SemanticModel' in dir_name:
                semantic_model_path = os.path.join(root, dir_name)
                print(f"Found SemanticModel folder at: {semantic_model_path}")
                break
        if semantic_model_path:
            break

    if not semantic_model_path:
        error_msg = f"No '.SemanticModel' folder found inside destination path: {dest_path}"
        print(f"ERROR - {error_msg}")
        logger.error(error_msg)
        raise Exception(error_msg)

    definition_folder_path = os.path.join(semantic_model_path, 'definition')
    print(f"Definition folder path set to: {definition_folder_path}")

    end_time = time.time()
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(end_time))}] - Completed copying structure. Time taken: {end_time - start_time:.2f} seconds.")

    return definition_folder_path

def extract_models_and_columns_with_lineage_excel(xml_file_path, tables_output_dir, models_file_path, relationships_output_file_path, date_table_template_file_path, calculated_columns_json_data):
    logger.info(f"Excel TMDL Gen - extract_models_and_columns_with_lineage_excel - Starting for: {xml_file_path}")

    try:
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
    except ET.ParseError as e:
        logger.error(f"Excel TMDL Gen - Failed to parse TWB XML: {xml_file_path}. Error: {e}", exc_info=True)
        raise

    unique_table_lineage_tags = {}
    all_relationships_data = []

    logger.info(f"Excel TMDL Gen - Copying date table template from: {date_table_template_file_path}")
    try:
        with open(date_table_template_file_path, 'r', encoding='utf-8') as tf:
            date_table_template_content = tf.read()
    except IOError as e:
        logger.error(f"Excel TMDL Gen - Failed to read date table template: {date_table_template_file_path}. Error: {e}", exc_info=True)
        date_table_template_content = None

    excel_file_source_path = get_connection_info_excel(root)
    if not excel_file_source_path:
        logger.warning(f"Excel TMDL Gen - Excel file path could not be determined from TWB connection info for {xml_file_path}. M-queries might be incorrect.")
        excel_file_source_path = "C:\\PATH_TO_YOUR_EXCEL_FILE.xlsx"
        logger.warning(f"Excel TMDL Gen - Using placeholder Excel path: {excel_file_source_path}")

    logger.info("Excel TMDL Gen - Extracting relationships from TWB XML (if any).")
    twb_defined_relationships, _ = extract_relationships_excel(root, unique_table_lineage_tags)
    all_relationships_data.extend(twb_defined_relationships)
    logger.info(f"Excel TMDL Gen - Found {len(twb_defined_relationships)} relationships defined in TWB.")

    logger.info("Excel TMDL Gen - Extracting table columns and generating LocalDateTables.")
    table_columns_data_map, _ = extract_table_columns_excel(
        root,
        tables_output_dir,
        date_table_template_content,
        unique_table_lineage_tags,
        all_relationships_data
    )
    logger.info(f"Excel TMDL Gen - Extracted data for {len(table_columns_data_map)} table(s). Total relationships now: {len(all_relationships_data)}")

    logger.info(f"Excel TMDL Gen - Writing all relationships to: {relationships_output_file_path}")
    write_relationships_file_excel(all_relationships_data, relationships_output_file_path)

    logger.info("Excel TMDL Gen - Writing individual table model files for main data tables.")
    write_table_model_files_excel(
        table_columns_data_map,
        unique_table_lineage_tags,
        tables_output_dir,
        excel_file_source_path,
        calculated_columns_json_data
    )

    for datasource_node in root.findall(".//datasource"):
        if datasource_node.get('name') == 'Parameters':
            logger.info("Excel TMDL Gen - Found 'Parameters' datasource. Creating parameters model.")
            create_parameters_model_excel(tables_output_dir, datasource_node)
            break

    logger.info(f"Excel TMDL Gen - Model structure generation completed for: {xml_file_path}")

def copy_template_file_excel(template_file, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    template_filename = os.path.basename(template_file)
    destination_template_path = os.path.join(output_dir, template_filename)
    shutil.copy(template_file, destination_template_path)
    with open(template_file, 'r') as tf:
        template_content = tf.read()
    with open(destination_template_path, 'w') as tf:
        tf.write(template_content)
    return template_content

def get_connection_info_excel(root):
    server_name = None
    for named_connection in root.findall(".//named-connection"):
        server_name = named_connection.get("caption")
        if server_name:
            break
    return server_name

def get_database_name_excel(root):
    database_name = None
    for datasource in root.findall(".//datasource"):
        caption = datasource.get("caption")
        if caption:
            match = re.search(r"\((.*?)\)", caption)
            if match:
                database_name = match.group(1)
                break
    return database_name

# def upload_and_generate_download_link_excel(powerbi_output_dir, organization_name, user_email, process_id):
#     unique_folder = f'{organization_name}/{user_email}'
#     zip_path = shutil.make_archive(powerbi_output_dir, 'zip', powerbi_output_dir)
#     zip_s3_key = f'{unique_folder}/{process_id}/semantic_model/{os.path.basename(zip_path)}'
#     s3_client.upload_file(zip_path, bucket_name, zip_s3_key)
#     return {
#         'file': os.path.basename(zip_path),
#         'download_url': s3_client.generate_presigned_url(
#             'get_object', Params={'Bucket': bucket_name, 'Key': zip_s3_key}, ExpiresIn=3600
#         )
#     }
