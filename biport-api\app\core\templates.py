slicer_template = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 5000 }} }} ], "singleVisual": {{ "visualType": "slicer", "projections": {{ "Values": [ {{ "queryRef": "{value_queryref}", "active": true }} ] }}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "hasDefaultSort": true, "objects": {{ "data": [ {{ "properties": {{ "mode": {{ "expr": {{ "Literal": {{ "Value": "'{mode_value}'" }} }} }} }} }} ], "header": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "items": [ {{ "properties": {{ "outlineStyle": {{ "expr": {{ "Literal": {{ "Value": "0D" }} }} }} }} }} ], "general": [ {{ "properties": {{ "outlineWeight": {{ "expr": {{ "Literal": {{ "Value": "1D" }} }} }} }} }} ] }}, "vcObjects": {{ "title": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "text": {{ "expr": {{ "Literal": {{ "Value": "'{title_text}'" }} }} }} }} }} ], "background": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "color": {{ "solid": {{ "color": {{ "expr": {{ "ThemeDataColor": {{ "ColorId": 0, "Percent": 0 }} }} }} }} }} }} }} ], "visualHeader": [ {{ "properties": {{ "border": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#252423'" }} }} }} }} }}, "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "border": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "color": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'{border_color}'" }} }} }} }} }} }} }} ] }} }} }}'''

singleValue_slicer_template = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 5000 }} }} ], "singleVisual": {{ "visualType": "slicer", "projections": {{ "Values": [ {{ "queryRef": "{value_queryref}", "active": true }} ] }}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "hasDefaultSort": true, "objects": {{ "data": [ {{ "properties": {{ "mode": {{ "expr": {{ "Literal": {{ "Value": "'{mode_value}'" }} }} }} }} }} ], "header": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "items": [ {{ "properties": {{ "outlineStyle": {{ "expr": {{ "Literal": {{ "Value": "0D" }} }} }} }} }} ], "general": [ {{ "properties": {{ "outlineWeight": {{ "expr": {{ "Literal": {{ "Value": "1D" }} }} }}, "orientation": {{ "expr": {{ "Literal": {{ "Value": "0D" }} }} }}, "filter": {{ "filter": {{ "Version": 2, "From": {from_list}, "Where": [ {{ "Condition": {{ "In": {{ "Expressions": [ {{ "Column": {{ "Expression": {{ "SourceRef": {{ "Source": "{filter_source}" }} }}, "Property": "{filter_property}" }} }} ], }} }} }} ] }} }} }} }} ], "selection": {selection_list} }}, "vcObjects": {{ "title": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "text": {{ "expr": {{ "Literal": {{ "Value": "'{title_text}'" }} }} }} }} }} ], "background": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "color": {{ "solid": {{ "color": {{ "expr": {{ "ThemeDataColor": {{ "ColorId": 0, "Percent": 0 }} }} }} }} }} }} }} ], "visualHeader": [ {{ "properties": {{ "border": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#252423'" }} }} }} }} }}, "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "border": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "color": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'{border_color}'" }} }} }} }} }} }} }} ] }} }} }}'''

text_box_json = '''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":0,"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"textbox","drillFilterOtherVisuals":true,{objects},{vc_objects}}}}}'''

title_textbox = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 1 }} }} ], "singleVisual": {{ "visualType": "textbox", "drillFilterOtherVisuals": true, "objects": {{ "general": [ {{ "properties": {{ "paragraphs": {text_paragraphs} }} }} ] }}, "vcObjects": {{ "border": {border_list}, "background": {background_list} }} }} }}'''

pivot_table_json = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 0 }} }} ], "singleVisual": {{ "visualType": "pivotTable", "projections": {{ "Rows": [ {{ "queryRef": "{rows_queryref}", "active": true }} ], "Columns": [ {{ "queryRef": "{cols_queryref}", "active": true }} ], "Values": [ {{ "queryRef": "{value_queryref}" }} ] }}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "objects": {{ "subTotals": [ {{ "properties": {{ "columnSubtotals": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "rowSubtotals": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "columnHeaders": [ {{ "properties": {{ "fontSize": {{ "expr": {{ "Literal": {{ "Value": "13D" }} }} }} }} }} ], "rowHeaders": [ {{ "properties": {{ "fontSize": {{ "expr": {{ "Literal": {{ "Value": "13D" }} }} }} }} }} ], "grid": [ {{ "properties": {{}} }} ] }}, "vcObjects": {{ "stylePreset": [ {{ "properties": {{ "name": {{ "expr": {{ "Literal": {{ "Value": "'Default'" }} }} }} }} }} ], "title": {title_list}, "background": {background_list}, "border": {border_list} }} }} }}'''

pivot_table_filters = {"filters": [], "height": 518.48, "width": 1280.31, "x": 0, "y": 201.77, "z": 0 }

card_json = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 1 }} }} ], "singleVisual": {{ "visualType": "card", "projections": {projection_data}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "objects": {objects_data}, "vcObjects": {{ "title": {title_list} }} }} }}'''

treemap_json = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 0 }} }} ], "singleVisual": {{ "visualType": "treemap", "projections": {{ "Group": [ {{ "queryRef": "{group_queryref}", "active": true }} ], "Values": [ {{ "queryRef": "{values_queryref}" }} ] }}, "prototypeQuery": {{ "Version": 2, "From": [ {{ "Name": "{from_name}", "Entity": "{from_entity}", "Type": 0 }} ], "Select": [ {{ "Column": {{ "Expression": {{ "SourceRef": {{ "Source": "{from_name}" }} }}, "Property": "{select_property}" }}, "Name": "{group_queryref}", "NativeReferenceName": "{select_nativeReferenceName}" }}, {{ "Aggregation": {{ "Expression": {{ "Column": {{ "Expression": {{ "SourceRef": {{ "Source": "{from_name}" }} }}, "Property": "{aggregation_property}" }} }}, "Function": 0 }}, "Name": "{values_queryref}", "NativeReferenceName": "{aggregation_nativeReferenceName}" }} ] }}, "drillFilterOtherVisuals": true, "objects": {{ "dataPoint": [ {{ "properties": {{ "fill": {{ "solid": {{ "color": {{ "expr": {{ "FillRule": {{ "Input": {{ "Aggregation": {{ "Expression": {{ "Column": {{ "Expression": {{ "SourceRef": {{ "Entity": "{from_entity}" }} }}, "Property": "{select_property}" }} }}, "Function": 5 }} }}, "FillRule": {{ "linearGradient2": {{ "min": {{ "color": {{ "Literal": {{ "Value": "'#BCE4D8'" }} }} }}, "max": {{ "color": {{ "Literal": {{ "Value": "'#2C5985'" }} }} }}, "nullColoringStrategy": {{ "strategy": {{ "Literal": {{ "Value": "'asZero'" }} }} }} }} }} }} }} }} }} }} }}, "selector": {{ "data": [ {{ "dataViewWildcard": {{ "matchingOption": 1 }} }} ] }} }} ] }}, "vcObjects": {{ "title": {title_list}, "background": {background_list}, "border": {border_list} }} }} }}'''

treemap_filter_json = {"filters": "[]", "height": 302.04,"width": 1280.31,"x": 0.00,"y": 418.21,"z": 0.00}

filledmap_template = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 1 }} }} ], "singleVisual": {{ "visualType": "filledMap", "projections": {projection_data}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "objects": {objects_data}, "vcObjects": {{ "title": {title_list}, "background": {background_list}, "border": {border_list} }} }} }}'''

piechart_json = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 0 }} }} ], "singleVisual": {{ "visualType": "pieChart", "projections": {{ "Category": [ {{ "queryRef": "{category_queryref}", "active": true }} ], "Y": [ {{ "queryRef": "{y_queryref}" }} ] }}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "hasDefaultSort": true, "objects": {{ "labels": [ {{ "properties": {{ "labelDisplayUnits": {{ "expr": {{ "Literal": {{ "Value": "1D" }} }} }} }} }} ] }},"vcObjects": {{"title": {title_list}, "background": {background_list}, "border": {border_list} }} }} }}'''

table_json = '''{{ "name": "2bb9ed4ae660e7a710b4", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 6000 }} }} ], "singleVisual": {{ "visualType": "tableEx", "projections": {projections_data}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "vcObjects": {{"title": {title_list}, "background": {background_list}, "border": {border_list}}} }} }}'''

line_chart_json = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width":{width}, "height": {height}, "tabOrder": 0 }} }} ], "singleVisual": {{ "visualType": "lineChart", "projections": {projection_data}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "drillFilterOtherVisuals": true, "objects": {objects_data}, "vcObjects": {{ "background": {background_list}, "title": {title_list}, "border": {border_list} }} }} }}'''

map_json_template = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 18000 }} }} ], "singleVisual": {{ "visualType": "map", "projections": {{ "Category": [ {{ "queryRef": "{category_queryref}", "active": true }} ], "Size": [ {{ "queryRef": "{size_queryref}" }} ] }}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list},"OrderBy": [ {{ "Direction": 2, "Expression": {{ "Aggregation": {{ "Expression": {{ "Column": {{ "Expression": {{ "SourceRef": {{ "Source": "{orderby_source}" }} }}, "Property": "{orderby_property}" }} }}, "Function": 0 }} }} }} ] }}, "drillFilterOtherVisuals": true, "hasDefaultSort": true, "objects": {{ "bubbles": [ {{ "properties": {{ "bubbleSize": {{ "expr": {{ "Literal": {{ "Value": "26L" }} }} }} }} }} ], "mapStyles": [ {{ "properties": {{ "showLabels": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "mapTheme": {{ "expr": {{ "Literal": {{ "Value": "'canvasLight'" }} }} }} }} }} ], "categoryLabels": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "enableBackground": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "backgroundTransparency": {{ "expr": {{ "Literal": {{ "Value": "100L" }} }} }}, "color": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#252423'" }} }} }} }} }} }} }} ], "mapControls": [ {{ "properties": {{ "autoZoom": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "showZoomButtons": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "showLassoButton": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "geocodingCulture": {{ "expr": {{ "Literal": {{ "Value": "'en-US'" }} }} }} }} }} ], "dataPoint": [ {{ "properties": {{ "fill": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#4E79A7'" }} }} }} }} }} }} }} ] }}, "vcObjects": {{ "title": {title_list}, "background": {background_list}, "border": {border_list}, "visualHeader": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ] }} }} }}'''

line_stacked_column_json = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 9000 }} }} ], "singleVisual": {{ "visualType": "lineStackedColumnComboChart", "projections": {{ "Y": [ {{ "queryRef": "{y_queryref}" }} ], "Y2": [ {{ "queryRef": "{y2_queryref}" }} ], "Category": [ {{ "queryRef": "{category_queryref}", "active": true }} ] }}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list} }}, "columnProperties": {{ "{y_queryref}": {{ "displayName": "{col_prop_displayname}" }} }}, "drillFilterOtherVisuals": true, "hasDefaultSort": true, "objects": {{ "dataPoint": [ {{ "properties": {{ "fill": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#E15759'" }} }} }} }} }} }} }}, {{ "properties": {{ "fill": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#252423'" }} }} }} }} }} }}, "selector": {{ "metadata": "{y2_queryref}" }} }} ], "lineStyles": [ {{ "properties": {{ "areaShow": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }} }} }} ], "legend": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "valueAxis": [ {{ "properties": {{ "axisStyle": {{ "expr": {{ "Literal": {{ "Value": "'showTitleOnly'" }} }} }}, "showAxisTitle": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "labelColor": {{ "solid": {{ "color": {{ "expr": {{ "ThemeDataColor": {{ "ColorId": 0, "Percent": -0.6 }} }} }} }} }}, "secShow": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "titleItalic": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "italic": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "titleColor": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#E15777'" }} }} }} }} }}, "labelPrecision": {{ "expr": {{ "Literal": {{ "Value": "0L" }} }} }}, "gridlineShow": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "categoryAxis": [ {{ "properties": {{ "showAxisTitle": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "labelColor": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#252423'" }} }} }} }} }}, "gridlineShow": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }} }} }} ], "labels": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "labelDensity": {{ "expr": {{ "Literal": {{ "Value": "100L" }} }} }}, "color": {{ "solid": {{ "color": {{ "expr": {{ "Literal": {{ "Value": "'#252423'" }} }} }} }} }}, "enableBackground": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "labelDisplayUnits": {{ "expr": {{ "Literal": {{ "Value": "1000000D" }} }} }}, "bold": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }}, "labelPrecision": {{ "expr": {{ "Literal": {{ "Value": "2L" }} }} }} }} }} ] }}, "vcObjects": {{ "title": {title_list}, "visualTooltip": [ {{ "properties": {{ "show": {{ "expr": {{ "Literal": {{ "Value": "true" }} }} }} }} }} ], "background": {background_list}, "border": {border_list} }} }} }}'''

bar_chart_template = '''{{ "name": "{visual_config_name}", "layouts": [ {{ "id": 0, "position": {{ "x": {x}, "y": {y}, "z": {z}, "width": {width}, "height": {height}, "tabOrder": 1001 }} }} ], "singleVisual": {{ "visualType": "{visual_type}", "projections": {projections_data}, "prototypeQuery": {{ "Version": 2, "From": {from_list}, "Select": {select_list}}}, "drillFilterOtherVisuals": true, "hasDefaultSort": true, "objects": {objects_data}, "vcObjects": {{ "title": {title_list}, "background": {background_list}, "border": {border_list} }} }} }}'''

scatter_json='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"scatterChart","projections":{projection_data},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list}}},"drillFilterOtherVisuals":true,"objects":{objects_data}, "vcObjects": {{ "title": {title_list}, "background": {background_list}, "border": {border_list} }} }} }}'''

area_chart_json = '''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"areaChart","projections":{{"Category":{category_list},"Y":{y_ref_list},"Series":{series_ref_list}}},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list},\"OrderBy\":{orderby_list}}},"drillFilterOtherVisuals":true,"objects":{{"labels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}],"seriesLabels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}]}}, "vcObjects": {{ "title": {title_list}, "background": {background_list}, "border": {border_list} }}}}}}'''

pivot_table_template = '''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"pivotTable","projections":{projections_dict},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list}}},"drillFilterOtherVisuals":true,"objects":{{"values":{objects_values},"columnHeaders":[{{"properties":{{"wordWrap":{{"expr":{{"Literal":{{"Value":"true"}}}}}},"fontSize":{{"expr":{{"Literal":{{"Value":"10D"}}}}}},"fontFamily":{{"expr":{{"Literal":{{"Value":"'Calibri'"}}}}}}}}}}],"subTotals":[{{"properties":{{"columnSubtotals":{{"expr":{{"Literal":{{"Value":"true"}}}}}},"rowSubtotals":{{"expr":{{"Literal":{{"Value":"false"}}}}}}}}}}],"rowHeaders":[{{"properties":{{"fontSize":{{"expr":{{"Literal":{{"Value":"13D"}}}}}},"fontFamily":{{"expr":{{"Literal":{{"Value":"'Calibri'"}}}}}}}}}}],"grid":[{{"properties":{{"outlineColor":{{"solid":{{"color":{{"expr":{{"Literal":{{"Value":"'#CCCCCC'"}}}}}}}}}}}}}}],"columnTotal":[{{"properties":{{"fontFamily":{{"expr":{{"Literal":{{"Value":"'Calibri'"}}}}}}}}}}],"rowTotal":[{{"properties":{{"fontFamily":{{"expr":{{"Literal":{{"Value":"'Calibri'"}}}}}}}}}}]}}, "vcObjects": {{ "title": {title_list}, "background": {background_list}, "border": {border_list} }}}}}}'''

pivot_table_colors = '''[ {{ "properties": {{ "backColor": {{ "solid": {{ "color": {{ "expr": {{ "FillRule": {{ "Input": {values_input}, "FillRule": {{ "linearGradient3": {{ "min": {{ "color": {{ "Literal": {{ "Value": "'{min_color}'" }} }} }}, "mid": {{ "color": {{ "Literal": {{ "Value": "'{mid_color}'" }} }} }}, "max": {{ "color": {{ "Literal": {{ "Value": "'{max_color}'" }} }} }}, "nullColoringStrategy": {{ "strategy": {{ "Literal": {{ "Value": "'asZero'" }} }} }} }} }} }} }} }} }} }} }}, "selector": {{ "data": [ {{ "dataViewWildcard": {{ "matchingOption": 1 }} }} ], "metadata": "{selector_metadata}" }} }}, {{ "properties": {{ "bandedRowHeaders": {{ "expr": {{ "Literal": {{ "Value": "false" }} }} }}, "fontSize": {{ "expr": {{ "Literal": {{ "Value": "13D" }} }} }} }} }} ]'''

stacked_bar_chart_json='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"columnChart","projections":{{"Category":{category_list},"Y":{y_ref_list},"Series":{series_ref_list}}},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list},"OrderBy":{orderby_list}}},"columnProperties":{column_properties_json},"drillFilterOtherVisuals":true,"hasDefaultSort":true,"objects":{{"labels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}]}}}}}}'''

dual_line_chart_json='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"height":{height},"width":{width},"tabOrder":0}}}}],"singleVisual":{{"visualType":"lineChart","projections":{{"Category":{cat_ref_list},"Y":{y_ref_list},"Y2":{y2_ref_list}}},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list},"OrderBy":{order_list}}},"columnProperties":{columnproperties_json},"drillFilterOtherVisuals":\"true\","hasDefaultSort":\"true\","objects":{{"labels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}]}}}}}}'''

area_chart_discrete_json='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"areaChart","projections":{{"Category":{cat_ref_list},"Y":{y_ref_list},"Series":{series_ref_list}}},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list}}},"columnProperties":{column_properties_json},"drillFilterOtherVisuals":\"true\","objects":{{"labels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}]}}}}}}'''

line_bar_json='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":1}}}}],"singleVisual":{{"visualType":"lineStackedColumnComboChart","projections":{{"Category":{cat_ref_list},"Y":{y1_ref_list},"Y2":{y2_ref_list}}},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list}}},"columnProperties":{columnproperties_json},"drillFilterOtherVisuals":"true","hasDefaultSort":true,"objects":{{"labels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}]}}}}}}'''

line_chart_discrete='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"lineChart","projections":{{"Y":{y_ref_list},"Category":{cat_ref_list},"Series":{series_ref_list}}},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list},"OrderBy":{orderby_list}}},"columnProperties":{column_properties_json},"drillFilterOtherVisuals":true,"hasDefaultSort":true,"objects":{{"labels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}]}}}}}}'''

button_json='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":0,"width":{width},"height":{height},"tabOrder":1000}}}}],"singleVisual":{{"visualType":"actionButton","drillFilterOtherVisuals":true,{objects},{vc_objects}}},"howCreated":"InsertVisualButton"}}'''

horizontal_stacked_bar='''{{"name":"{visual_config_name}","layouts":[{{"id":0,"position":{{"x":{x},"y":{y},"z":{z},"width":{width},"height":{height},"tabOrder":0}}}}],"singleVisual":{{"visualType":"barChart","projections":{{"Category":{category_list},"Series":{series_ref_list},"Y":{y_ref_list}}},"prototypeQuery":{{"Version":2,"From":{from_list},"Select":{select_list},"OrderBy":{orderby_list}}},"drillFilterOtherVisuals":true,"hasDefaultSort":true,"objects":{{"labels":[{{"properties":{{"show":{{"expr":{{"Literal":{{"Value":"true"}}}}}}}}}}]}}}}}}'''


report_template = {
  "config": "{\"version\":\"5.44\",\"themeCollection\":{\"baseTheme\":{\"name\":\"CY24SU06\",\"version\":\"5.56\",\"type\":2}},\"activeSectionIndex\":0,\"defaultDrillFilterOtherVisuals\":true,\"linguisticSchemaSyncVersion\":2,\"settings\":{\"useNewFilterPaneExperience\":true,\"allowChangeFilterTypes\":true,\"useStylableVisualContainerHeader\":true,\"queryLimitOption\":6,\"exportDataMode\":1,\"useDefaultAggregateDisplayName\":true},\"objects\":{\"section\":[{\"properties\":{\"verticalAlignment\":{\"expr\":{\"Literal\":{\"Value\":\"'Top'\"}}}}}],\"outspacePane\":[{\"properties\":{\"expanded\":{\"expr\":{\"Literal\":{\"Value\":\"false\"}}}}}]}}",
  "layoutOptimization": 0,
  "resourcePackages": [
  {
      "resourcePackage": {
        "disabled": False,
        "items": [
          {
            "name": "CY24SU06",
            "path": "BaseThemes/CY24SU06.json",
            "type": 202
          }
        ],
        "name": "SharedResources",
        "type": 2
      }
    }
  ],
  "sections": []
}