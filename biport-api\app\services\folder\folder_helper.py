from uuid import UUID
from requests import Session
from app.models_old.folders import Folder

def extract_s3_key(s3_uri: str) -> str:
    if s3_uri.startswith("s3://"):
        return "/".join(s3_uri.split("/")[3:])
    return s3_uri

def convert_uuid_to_string(data):
    if isinstance(data, dict):
        return {key: convert_uuid_to_string(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_uuid_to_string(item) for item in data]
    elif isinstance(data, UUID):
        return str(data)
    return data


def build_file_info(file_id, filename, s3_key, file_url):
    return {
        "workbook_id": str(file_id),
        "workbook_name": filename,
        "s3_key": s3_key,
        "file_url": file_url or "unavailable"
    }

def build_project_summary(project_id, project_name, files_info):
    return {
        "project_id": str(project_id),
        "project_name": project_name,
        "number_of_workbooks": len(files_info),
        "workbooks": files_info
    }

class FolderHelper:
    @staticmethod
    def recursively_soft_delete(folder: Folder, db: Session):
        folder.is_deleted = 1
        children = db.query(Folder).filter(Folder.parent_id == folder.id, Folder.is_deleted == 0).all()
        for child in children:
            FolderHelper.recursively_soft_delete(child, db)

