"""Make siteId and serverId nullable in project_details

Revision ID: a3ff3c53e38e
Revises: 
Create Date: 2025-07-18 12:08:30.249892

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a3ff3c53e38e'
down_revision = '2507c750e7fb'  
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column(
        'project_details', 
        'site_id',
        existing_type=sa.UUID(),  
        nullable=True,
        schema='biport_dev'
    )

    op.alter_column(
        'project_details', 
        'server_id',
        existing_type=sa.UUID(),
        nullable=True,
        schema='biport_dev'
    )



def downgrade() -> None:
    op.alter_column(
        'project_details', 
        'site_id',
        existing_type=sa.UUID(),  
        nullable=False,
        schema='biport_dev'
    )

    op.alter_column(
        'project_details', 
        'server_id',
        existing_type=sa.UUID(),
        nullable=False,
        schema='biport_dev'
    )
