from ..core import get_background_config, get_border_config, get_filter_column, get_queryref, get_nativeReferenceName, get_title_config
from app.core import treemap_json
import uuid, json
from app.core import logger
from app.core.enums import (
    PowerBITemplateKeys, PowerBIReportKeys, PowerBIChartTypes, VisualRequest, TableauXMLTags
)
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_multiple_encodings_data,
    generate_projections_data
)

def get_treemap_report(pane_encodings,worksheet_name,table_column_data, worksheet_title_layout, style):
    try:
        overall_treemap_result = []
        map_encoding_text = pane_encodings.get('text').get('@column')
        map_encoding_size = pane_encodings.get('size').get('@column')
        group_filter_name, group_column_name = get_filter_column(map_encoding_text)
        values_filter_name, values_column_name = get_filter_column(map_encoding_size)
        group_table_name = next((key for key, value in table_column_data.items() if group_column_name in value), None)
        values_table_name = next((key for key, value in table_column_data.items() if values_column_name in value), None)
        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)
        treemap_json_data = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "group_queryref" : get_queryref(group_filter_name, group_column_name, group_table_name),
            "values_queryref" : get_queryref(values_filter_name, values_column_name, values_table_name),
            "from_name" : group_table_name[0].lower(),
            "from_entity" : group_table_name,
            "select_property" : group_column_name,
            "select_nativeReferenceName" : get_nativeReferenceName(group_filter_name, group_column_name),
            "aggregation_property" : values_column_name,
            "aggregation_nativeReferenceName" : get_nativeReferenceName(values_filter_name, values_column_name),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }
        overall_treemap_result.append({"config":treemap_json_data, "template": treemap_json})
        return overall_treemap_result
    except Exception as e:
        logger.error(f"---Exception in generating treemap visual for {worksheet_name}-----{str(e)}")
        raise ValueError(f"Exception in generating treemap visual for {worksheet_name}- {str(e)}")


def process_tree_map_report(request: VisualRequest):
    """
    Processes the tree map report from the given request.
    Args:
        request (VisualRequest): The request object containing the necessary data.
    Returns:
        dict: A dictionary containing the processed tree map data.
    -----------
    
    """
    tree_map_result = {}

    panes = request.panes
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )

    unique_text = remove_duplicate_fields(encodings[TableauXMLTags.TEXT.value])
    unique_size = remove_duplicate_fields(encodings[TableauXMLTags.SIZE.value])

    tree_map_field_mapping = {
        PowerBIReportKeys.GROUP.value: unique_text,
        PowerBIReportKeys.VALUES.value: unique_size
    }
    projections_data = generate_projections_data(table_column_data, calculations_related_data, tree_map_field_mapping)


    tree_map_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.TREE_MAP.value
    tree_map_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    tree_map_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data

    return tree_map_result
