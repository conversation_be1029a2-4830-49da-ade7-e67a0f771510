"""seperating columns

Revision ID: 5c3d83fe1099
Revises: 03a81c14f05a
Create Date: 2025-04-11 05:01:14.284726

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5c3d83fe1099'
down_revision: Union[str, None] = '03a81c14f05a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade():
    # Drop the column
    op.drop_column('data_sources', 'SQL')
    op.drop_column('data_sources', 'POSTGRESQL')
    op.add_column('data_sources', sa.Column('mssql', sa.String(), nullable=True))
    op.add_column('data_sources', sa.Column('postgresql', sa.String(), nullable=True))
    op.add_column('data_sources', sa.Column('mysql', sa.String(), nullable=True))
    op.add_column('data_sources', sa.Column('oracle', sa.String(), nullable=True))
    op.add_column('data_sources', sa.Column('sqlite', sa.String(), nullable=True))
    

def downgrade():
    op.add_column('data_sources', sa.Column('SQL', sa.String(), nullable=True))
    op.add_column('data_sources', sa.Column('POSTGRESQL', sa.String(), nullable=True))
    op.drop_column('data_sources', 'mssql')
    op.drop_column('data_sources', 'mysql')
    op.drop_column('data_sources', 'sql')
    op.drop_column('data_sources', 'oracle')
    op.drop_column('data_sources', 'sqlite')
    
