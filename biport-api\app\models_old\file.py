from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.orm import relationship
import uuid
from app.core import Base


class FileModel(Base):
    __tablename__ = "files"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    filename = Column(String, nullable=False)
    filepath = Column(String, nullable=False)
    folder_id = Column(String(36), ForeignKey("folders.id"), nullable=False)
    folder = relationship("Folder", back_populates="files")
