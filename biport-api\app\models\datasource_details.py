import uuid
from sqlalchemy import <PERSON>um<PERSON>, String, Foreign<PERSON>ey, JSO<PERSON>
from sqlalchemy.dialects.postgresql import UUID
from app.core.session import Base
from app.models.base import AuditMixin

class DatasourceDetail(Base, AuditMixin):
    __tablename__ = "datasource_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.organization_details.id"), nullable=False)
    credentials = Column(JSON, nullable=True)
