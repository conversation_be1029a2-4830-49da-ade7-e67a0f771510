"""created table

Revision ID: ef0f0355aea5
Revises: 78f0af23cc75
Create Date: 2025-05-05 19:01:16.532198

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ef0f0355aea5'
down_revision: Union[str, None] = '78f0af23cc75'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('site_discovery',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('server_id', sa.UUID(), nullable=False),
    sa.Column('server_type_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.Enum('INITIATED', 'STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='discoversitestatus'), nullable=True),
    sa.Column('server_type', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['server_id'], ['server_details.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_table('site_discovery')
    # ### end Alembic commands ###
