import uuid, json
from ..core import to_list, process_label_data, create_expr, get_projections_data, get_from_list, get_calc_filter_column
from app.core import card_json
from app.core import logger

def get_card_title(text, style):
    title_properties = {}
    style_rule = to_list(style.get("style-rule")) if style else None
    # title_styles = process_label_data(style_rule)
    title_properties["show"] = create_expr("true")
    title_properties["text"] = create_expr(f"'{text}'")
    title_properties["fontFamily"] = create_expr("'Calibri'")
    title_properties["alignment"] = create_expr("'center'")
    # if title_styles: title_properties.update(title_styles)
    return [{"properties":title_properties}]


def get_card_objects_data(style):
    objects = {}
    style_rule = to_list(style.get("style-rule")) if style else None
    label_data = process_label_data(style_rule)
    objects["categoryLabels"] = [{"properties":{"show":create_expr("false")}}]
    if label_data: objects["labels"] = [{"properties":label_data}]
    return objects

def get_card_report(measures_filter, table_column_data, datasource_col_list, worksheet_name, style):
    try:
        overall_card_result = []
        
        measures_filter_list = measures_filter if isinstance(measures_filter, list) else [measures_filter]

        for measure in measures_filter_list:
            if measure and measure.get('@class') == 'categorical' and 'Measure Names' in measure.get('@column', ''):
                projections_data = {}
                group_filter = measure.get('groupfilter', {}).get('groupfilter', [])
                group_filter_list = group_filter if isinstance(group_filter, list) else [group_filter]       

                for filter in group_filter_list:
                    member = filter.get('@member')
                    field_filter, field_column, field_table_name = get_calc_filter_column(member, table_column_data, datasource_col_list)
                    result_queryref, table_list, select_list = get_projections_data([member], table_column_data, datasource_col_list)
                    projections_data["Values"] = result_queryref.get(member)
                    from_list = get_from_list(table_list)
                    objects_data = get_card_objects_data(style)
                    title_list = get_card_title(field_column, style)
                    card_json_data = {
                        "visual_config_name": f'{str(uuid.uuid4()).replace("-", "")[:20]}',
                        "projection_data": json.dumps(projections_data),
                        "from_list": json.dumps(from_list),
                        "select_list": json.dumps(select_list),
                        "objects_data" :json.dumps(objects_data),
                        "title_list": json.dumps(title_list),
                    }
                    overall_card_result.append({"config": card_json_data, "template": card_json})

            else:
                continue

        return overall_card_result
    except Exception as e:
        logger.error(f"---Error in generating card visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating card visual for {worksheet_name} - {str(e)}")
