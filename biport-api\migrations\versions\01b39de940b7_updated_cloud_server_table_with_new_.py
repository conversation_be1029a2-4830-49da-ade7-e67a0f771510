"""Updated cloud_server table with new column called project_summary

Revision ID: 01b39de940b7
Revises: 8bc3abb72037
Create Date: 2025-06-20 19:44:50.430143

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '01b39de940b7'
down_revision: Union[str, None] = '8bc3abb72037'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cloud_server', sa.Column('project_summary', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('cloud_server', 'project_summary')
    # ### end Alembic commands ###
