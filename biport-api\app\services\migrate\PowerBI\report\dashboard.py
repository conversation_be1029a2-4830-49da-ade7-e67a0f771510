import json
from .get_singlevisual_data import get_uuid
from app.core.enums import GeneralKeys, TableauXMLTags
from app.core.enums import PowerBIReportKeys

def get_dashboard_visual_container_visuals(generated_visuals, worksheets_in_dashboards):
    dashboard_visuals = []
    for ws_name, ws_dimensions in worksheets_in_dashboards.items():
        visual_json = generated_visuals.get(ws_name,{})
        if not visual_json: continue  #  safe check.
        visual_json[TableauXMLTags.NAME.value]  = get_uuid()
        visual_json[GeneralKeys.LAYOUTS.value] = generate_layouts(ws_dimensions)
        dashboard_visuals.append({PowerBIReportKeys.CONFIG.value:json.dumps(visual_json)})
    return dashboard_visuals


def generate_layouts(ws_dimensions):
    layouts = [{ GeneralKeys.ID.value: 0, GeneralKeys.POSITION.value:ws_dimensions}]
    return layouts
