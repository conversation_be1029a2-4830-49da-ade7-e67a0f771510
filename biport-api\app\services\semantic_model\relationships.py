import uuid
import re
from app.core.config import logger



def format_column_reference(column: str) -> str:
    """
    Formats a column reference by preserving the original names and adding single quotes
    only if table or column name contains spaces or special characters.
    """
    logger.info(f"Raw column input: {column}")

    table, col = column.rsplit('.', 1)

    if re.search(r"[^\w]", table):
        table = f"'{table}'"
    if re.search(r"[^\w]", col):
        col = f"'{col}'"

    formatted = f"{table}.{col}"
    logger.info(f"Formatted column reference: {formatted}")

    return formatted
    
        
    

def extract_relationships(root, unique_table_lineage_tags):
    """
    Extracts relationships from the XML and updates unique lineage tags for tables.
    Returns a list of relationship dictionaries and an initially empty variant_relationships dict.
    """
    relationships = []
    variant_relationships = {}
    
    for rel_tag in root.findall(".//relationship"):
        relationship_id = str(uuid.uuid4())
        expressions = rel_tag.findall(".//expression")
        from_column = expressions[1].get("op").split("(")[0] if len(expressions) > 1 else None
        to_column = expressions[2].get("op").split("(")[0] if len(expressions) > 2 else None

        first_end_point = rel_tag.find(".//first-end-point")
        second_end_point = rel_tag.find(".//second-end-point")

        from_table = (
            first_end_point.get("object-id").split("_")[0]
            if first_end_point is not None
            else None
        )
        to_table = (
            second_end_point.get("object-id").split("_")[0]
            if second_end_point is not None
            else None
        )

        if from_column and to_column and from_table and to_table:
            from_column = from_column.strip('[]').strip()
            to_column = to_column.strip('[]').strip()
            from_column = f"'{from_column}'" if " " in from_column else from_column
            to_column = f"'{to_column}'" if " " in to_column else to_column
            from_column_full = f"{from_table}.{from_column}"
            to_column_full = f"{to_table}.{to_column}"
            
            if first_end_point.get("unique-key") == "true" and second_end_point.get("unique-key") == "true":
                from_column_updated = from_column_full
                to_column_updated = to_column_full
                fromCardinality = "one"
                toCardinality = "one"
            elif first_end_point.get("unique-key") != "true" and second_end_point.get("unique-key") != "true":
                from_column_updated = from_column_full
                to_column_updated = to_column_full
                fromCardinality = "many"
                toCardinality = "many"
            elif first_end_point.get("unique-key") == "true" and second_end_point.get("unique-key") != "true":
                from_column_updated = to_column_full
                to_column_updated = from_column_full
                fromCardinality = "many"
                toCardinality = "one"
            elif first_end_point.get("unique-key") != "true" and second_end_point.get("unique-key") == "true":
                from_column_updated = from_column_full
                to_column_updated = to_column_full
                fromCardinality = "many"
                toCardinality = "one"

            # Ensure unique lineage tags for both tables
            unique_table_lineage_tags[from_table] = unique_table_lineage_tags.get(from_table, str(uuid.uuid4()))
            unique_table_lineage_tags[to_table] = unique_table_lineage_tags.get(to_table, str(uuid.uuid4()))
            
            relationship_data = {
                "id": relationship_id,
                "crossFilteringBehavior": "bothDirections",
                "fromCardinality": fromCardinality,
                "toCardinality": toCardinality,
                "fromColumn": from_column_updated,
                "toColumn": to_column_updated
            }
            relationships.append(relationship_data)
    return relationships, variant_relationships

def write_relationships_file(relationships, relationships_file):
    """
    Writes the relationship definitions to the specified file with prper formatting.
    """
    logger.info(f"Writing relationships file to : {relationships_file}")
    
    with open(relationships_file, "w") as rel_file:
        for rel in relationships:
            
            logger.info(f"Processing relationship: {rel['id']}")
            
            rel_file.write(f"relationship {rel['id']}\n")
            
            if "annotation" in rel:
                rel_file.write(f"\t{rel['annotation']}\n")
            
            if "crossFilteringBehavior" in rel:
                rel_file.write(f"\tcrossFilteringBehavior: {rel['crossFilteringBehavior']}\n")
            if "fromCardinality" in rel:    
                rel_file.write(f"\tfromCardinality: {rel['fromCardinality']}\n")
            if "toCardinality" in rel:    
                rel_file.write(f"\ttoCardinality: {rel['toCardinality']}\n")
                
            from_col=format_column_reference(rel['fromColumn'])
            to_col = format_column_reference(rel['toColumn'])
                
            rel_file.write(f"\tfromColumn: {from_col}\n")
            rel_file.write(f"\ttoColumn: {to_col}\n")
            rel_file.write("\n")
            
        logger.info("Completed writing all relationship definitions") 
        
        

