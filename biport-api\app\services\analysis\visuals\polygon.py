from app.core.enums import <PERSON><PERSON><PERSON>s as GS, WorkSheet as WS, ChartType
from app.core.enums import GeneralKeys as GS, WorkSheet as WS
from app.core import logger
from .common import check_common_structure 

class Polygon:
    @staticmethod
    def check_polygon(worksheet):
        valid, pane, rows_details, cols_details, quantitative_cols = check_common_structure(
            worksheet,
            require_quantitative=False
        )
        if not valid:
            logger.debug("Common table structure not valid.")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        marks = worksheet.findall(WS.MARK.value)
        if not marks:
            logger.debug("No <mark> tags found.")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        for mark in marks:
            mark_class = mark.get(GS.CLASS.value, '').strip().lower()
            logger.debug(f"Mark class found: '{mark_class}'")
            if mark_class == ChartType.POLYGON.value.lower():
                break
        else:
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        encodings_tag = pane.find(WS.ENCODINGS.value)
        if encodings_tag is None:
            logger.debug("No <encodings> tag found.")
            return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

        has_path = any(child.tag.lower().endswith("path") for child in list(encodings_tag))

        if has_path:
            logger.debug("Valid 'polygon_with_table' detected.")
            return {
                GS.STATUS.value: True,
                GS.CHART_TYPE.value: ChartType.POLYGON_WITH_TABLE.value
            }

        logger.debug("No <path> encoding found in <encodings>.")
        return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}




                                    
                                    