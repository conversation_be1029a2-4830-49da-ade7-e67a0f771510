from fastapi import APIRouter, Query, Depends, Body
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List
from uuid import UUID

from app.models.users import User
from app.schemas import DaxConversionRequest
from app.schemas.dax import WorkbooksRequest
from app.services.dax.dax_processor import DaxProcessor
from app.core.dependencies import get_current_user
from app.schemas.analyse import SuccessResponse

dax_router = APIRouter()

@dax_router.post("/calc-dax/{report_id}", response_model=SuccessResponse)
async def calc_dax_api(
    report_id: UUID,
    user: User = Depends(get_current_user)
):
    """API to perform DAX conversion and generate downloadable PDF."""
    response = await DaxProcessor.convert_dax(report_id, user)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)