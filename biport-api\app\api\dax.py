from fastapi import APIRouter, Query, Depends, Body
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List

from app.models_old.user import UserOld
from app.schemas import DaxConversionRequest
from app.schemas.dax import WorkbooksRequest
from app.services import DaxProcessor
from app.core.dependencies import get_current_user

dax_router = APIRouter()

@dax_router.post("/calc-dax", response_model=dict)
async def calc_dax_api(
    body: WorkbooksRequest,
    is_upload_file: bool = Query(..., description="True if file is uploaded, False if id is used"),
    user: UserOld = Depends(get_current_user)
):
    """API to perform DAX conversion and generate downloadable PDF."""
    request = DaxConversionRequest(
            twb_files=body.s3_paths if is_upload_file else body.workbook_ids,
            is_upload_file=is_upload_file,
            organization_name=user.organization_name,
            user_email=user.email
        )
    response = await DaxProcessor.convert_dax(request)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)