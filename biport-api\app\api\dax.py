from fastapi import APIRouter, Query, Depends, Body
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List
from uuid import UUID

from app.models.users import User
from app.schemas import DaxConversionRequest
from app.schemas.dax import WorkbooksRequest
from app.services.dax.dax_processor import DaxProcessor
from app.core.dependencies import get_current_user
from app.schemas.analyse import SuccessResponse
from app.core import logger

dax_router = APIRouter()

@dax_router.post("/calc-dax/{report_id}", response_model=SuccessResponse)
async def calc_dax_api(
    report_id: UUID,
    user: User = Depends(get_current_user)
):
    """API to perform DAX conversion and generate downloadable PDF."""
    logger.info(f"[DAX API] Starting DAX conversion for report_id: {report_id}, user: {user.email}")

    try:
        response = await DaxProcessor.convert_dax(report_id, user)
        logger.info(f"[DAX API] DAX conversion completed successfully for report_id: {report_id}")
        return {
        "message": response.error or ("Success" if response.status_code == 200 else "Failed"),
        "data": response.data
    }
    except Exception as e:
        logger.error(f"[DAX API] DAX conversion failed for report_id: {report_id}, error: {str(e)}", exc_info=True)
        raise