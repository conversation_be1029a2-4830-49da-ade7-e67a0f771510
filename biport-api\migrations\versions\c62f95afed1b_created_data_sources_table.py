"""created data_sources table

Revision ID: c62f95afed1b
Revises: e62ac6129e99
Create Date: 2025-04-02 18:47:56.462573

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c62f95afed1b'
down_revision: Union[str, None] = 'e62ac6129e99'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'data_sources',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.Integer(), sa.<PERSON>('users.id')),
        sa.Column('key', sa.LargeBinary(), nullable=True),
        sa.Column('encoded_data', sa.LargeBinary(), nullable=True)
    )



def downgrade() -> None:
    op.drop_table("data_sources")