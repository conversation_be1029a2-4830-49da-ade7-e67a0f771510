import re
import logging

from .table_columns_handler import fetch_objects_data


async def clean_column_name(col_expr):
    return re.sub(r'\s*\([^)]+\)', '', col_expr.strip('[]').strip())

async def extract_tableau_relationship_data(datasource_elements):
    """
    Extracts raw relationship data from Tableau XML datasource elements.
    """
    relationships_data = []
    for datasource_element in datasource_elements:
        try:
            relationships_element = datasource_element.find('object-graph/relationships')
            if relationships_element is None:
                logging.info("No <relationships> tag found under <object-graph> in this datasource.")
                continue
            relationship_elements = relationships_element.findall('relationship')
            if not relationship_elements:
                logging.info("No <relationship> tags found under <relationships> in this datasource.")
                continue

            object_id_to_caption = await fetch_objects_data(datasource_element)

            for relationship_element in relationship_elements:
                equality_expression = relationship_element.find("expression")
                if equality_expression is None:
                    logging.info("No <expression> tag found in <relationship>.")
                    continue
                column_expressions = equality_expression.findall('expression')
                if len(column_expressions) < 2:
                    logging.info("Less than two <expression> tags found in expression.")
                    continue

                from_column = await clean_column_name(column_expressions[0].get('op', ''))
                to_column = await clean_column_name(column_expressions[1].get('op', ''))
                from_endpoint = relationship_element.find('first-end-point')
                to_endpoint = relationship_element.find('second-end-point')

                if from_endpoint is None or to_endpoint is None:
                    logging.info("Missing <first-end-point> or <second-end-point> in <relationship>.")
                    continue

                from_table = object_id_to_caption.get(from_endpoint.get('object-id'))
                to_table = object_id_to_caption.get(to_endpoint.get('object-id'))

                if not (from_table and to_table and from_column and to_column):
                    logging.info("Missing table or column name when processing <relationship>.")
                    continue

                relationships_data.append({
                    'from_table': from_table,
                    'to_table': to_table,
                    'from_column': from_column,
                    'to_column': to_column,
                    'from_endpoint': from_endpoint,
                    'to_endpoint': to_endpoint
                })
        except Exception as e:
            raise RuntimeError(f"Failed to extract relationships from datasource: {e}")

    return relationships_data