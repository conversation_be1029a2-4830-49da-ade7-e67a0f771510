"""Add site_count_in_server

Revision ID: 7416d32c6beb
Revises: ade4a6b5485d
Create Date: 2025-05-30 16:43:08.083971

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7416d32c6beb'
down_revision: Union[str, None] = 'ade4a6b5485d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report_details', sa.Column('is_analyzed', sa.<PERSON>(), nullable=True))
    op.alter_column('report_details', 'id',
               existing_type=sa.UUID(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('report_details', 'serverid',
               existing_type=sa.UUID(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.drop_column('report_details', 'is_analised')
    op.alter_column('s3_credentials', 'id',
               existing_type=sa.UUID(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('s3_credentials', 'organization_id',
               existing_type=sa.UUID(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.add_column('server_details', sa.Column('site_count', sa.Integer(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'password',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('server_details', 'site_count')
    op.alter_column('s3_credentials', 'organization_id',
               existing_type=sa.BigInteger(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('s3_credentials', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.add_column('report_details', sa.Column('is_analised', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.alter_column('report_details', 'serverid',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('report_details', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.drop_column('report_details', 'is_analyzed')
    op.alter_column('folders', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('files', 'folder_id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    # ### end Alembic commands ###
