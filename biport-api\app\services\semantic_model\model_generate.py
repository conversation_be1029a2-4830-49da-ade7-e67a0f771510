import time
import os, shutil, json, re, uuid
from app.core.config import PathConfig, logger, S3Config
import xml.etree.ElementTree as ET
from .tables import create_parameters_model, write_table_model_files, extract_table_columns, update_tmdl_files_from_json
from .relationships import extract_relationships, write_relationships_file
from .process_calculations import get_calculations_data

s3_config = S3Config()
s3_client = s3_config.get_s3_client()
bucket_name = s3_config.bucket_name

def copy_powerbi_structure(source_path, dest_path):
    """Copies and modifies the Power BI structure."""
    print(f"copy_powerbi_structure: Copying files from {source_path} to {dest_path}")

    for root, dirs, files in os.walk(source_path):
        for filename in files:
            src_path = os.path.join(root, filename)
            relative_path = os.path.relpath(src_path, source_path)
            dest_file_path = os.path.join(dest_path, relative_path)
            os.makedirs(os.path.dirname(dest_file_path), exist_ok=True)
            shutil.copy2(src_path, dest_file_path)
            print(f"copy_powerbi_structure: Copied file {src_path} to {dest_file_path}")

    powerbi_folder_list = os.listdir(dest_path)
    print(f"copy_powerbi_structure: Folders/files inside dest_path = {powerbi_folder_list}")

    powerbi_report_folder = [path for path in powerbi_folder_list if '.SemanticModel' in path]

    if not powerbi_report_folder:
        print(f"copy_powerbi_structure: ERROR - No '.SemanticModel' folder found inside {dest_path}")
        raise Exception(f"No '.SemanticModel' folder found inside {dest_path}")
    else:
        print(f"copy_powerbi_structure: Found SemanticModel folder: {powerbi_report_folder[0]}")

    definition_folder_path = os.path.join(dest_path, powerbi_report_folder[0], 'definition')
    print(f"copy_powerbi_structure: Definition folder path set to {definition_folder_path}")

    return definition_folder_path

def upload_and_generate_download_link(powerbi_output_dir, organization_name, user_email, process_id):
    """Uploads files to S3 and generates a pre-signed download link."""
    unique_folder = f'{organization_name}/{user_email}'

    # Create ZIP and upload
    zip_path = shutil.make_archive(powerbi_output_dir, 'zip', powerbi_output_dir)
    zip_s3_key = f'{unique_folder}/{process_id}/semantic_model/{os.path.basename(zip_path)}'
    s3_client.upload_file(zip_path, bucket_name, zip_s3_key)

    # Generate pre-signed URL
    return {
        'file': os.path.basename(zip_path),
        'download_url': s3_client.generate_presigned_url(
            'get_object', Params={'Bucket': bucket_name, 'Key': zip_s3_key}, ExpiresIn=3600
        )
    }

async def process_single_file(twb_file, local_dir, organization_name, user_email, process_id):

    twb_filename = os.path.basename(twb_file)
    logger.info(f" ---Started {twb_filename} for semantic model generation-------")

    semantic_model_path = PathConfig.semantic_model_path
    powerbi_output_dir = os.path.join(local_dir, f'{os.path.splitext(twb_filename)[0][:2]}_model')
    os.makedirs(powerbi_output_dir, exist_ok=True)

    definition_folder_path = copy_powerbi_structure(source_path=semantic_model_path, dest_path= powerbi_output_dir)

    tables_output_dir = os.path.join(definition_folder_path, 'tables')
    relationships_file = os.path.join(definition_folder_path, 'relationships.tmdl')
    models_file = os.path.join(definition_folder_path, 'models.tmdl')
    template_file_path = PathConfig.dateTable_template_path

    json_data = get_calculations_data(twb_file)

    extract_models_and_columns_with_lineage(twb_file, tables_output_dir, models_file, relationships_file, template_file_path, json_data)

    logger.info(f" ---completed {twb_filename} for semantic model generation-------")
    zip_path = shutil.make_archive(powerbi_output_dir, 'zip', powerbi_output_dir)
    zip_filename = os.path.basename(zip_path)
    unique_folder = f'{organization_name}/{user_email}'
    s3_key = f'{unique_folder}/{process_id}/semantic_model/{zip_filename}'

    await s3_config.upload_to_s3(zip_path, s3_key)
    presigned_url = await s3_config.generate_presigned_url(s3_key)

    return {
        "file": zip_filename,
        "download_url": presigned_url
    }

async def semantic_model_generation(organization_name, user_email, twb_files_path, local_dir, process_id):
    
    download_links = []
    for twb_file in twb_files_path:
        download_link = await process_single_file(
            twb_file, local_dir, organization_name, user_email,process_id
        )
        download_links.append(download_link)

    return download_links

def copy_template_file(template_file, output_dir):
    """
    Copies the template file to the output directory and returns its content.
    """
    os.makedirs(output_dir, exist_ok=True)
    template_filename = os.path.basename(template_file)
    destination_template_path = os.path.join(output_dir, template_filename)
    shutil.copy(template_file, destination_template_path)
    
    with open(template_file, 'r') as tf:
        template_content = tf.read()

    with open(destination_template_path, 'w') as tf:
        tf.write(template_content)
    return template_content

def get_connection_info(root):
    """
    Iterates through the XML to get connection info: server name, connection type, and file system type.
    """
    server_name = None
    for named_connection in root.findall(".//named-connection"):
        server_name = named_connection.get("caption")
       
        if server_name:
            break

    return server_name

def get_database_name(root):
    """
    Searches for the datasource caption and extracts the database name using a regex.
    """
    database_name = None
    for datasource in root.findall(".//datasource"):
        caption = datasource.get("caption")
        if caption:
            match = re.search(r"\((.*?)\)", caption)
            if match:
                database_name = match.group(1)
                break
    return database_name

def extract_models_and_columns_with_lineage(
    xml_file, output_dir, models_file, relationships_file, template_file, json_file
):
    """
    Main function that:
      - Parses the XML file,
      - Copies the template file,
      - Extracts connection info and database name,
      - Extracts relationships and table columns (including local date tables),
      - Writes the relationships, models file, and table model files.
    """
    logger.info(f"Starting extraction for file: {xml_file}")

    tree = ET.parse(xml_file)
    root = tree.getroot()
    
    unique_table_lineage_tags = {}
    relationships = []

    logger.info(f"Copying template file: {template_file}")
    template_content = copy_template_file(template_file, output_dir)
    
    logger.info(f"Extracting connection info from XML: {xml_file}")
    server_name = get_connection_info(root)

    logger.info(f"Extracting database name from XML: {xml_file}")
    database_name = get_database_name(root)
    
    logger.info("Extracting relationships from XML")
    rels, variant_relationships = extract_relationships(root, unique_table_lineage_tags)
    relationships.extend(rels)
    
    logger.info("Extracting table columns and writing local date tables")
    table_columns, variant_relationships = extract_table_columns(
        root, output_dir, template_content, unique_table_lineage_tags, relationships
    )
    
    logger.info(f"Writing relationships to file: {relationships_file}")
    write_relationships_file(relationships, relationships_file)
    
    
    
    logger.info("Writing individual table model files")
    write_table_model_files(table_columns, unique_table_lineage_tags, output_dir, server_name, database_name)

    for datasource in root.findall(".//datasource"):
        if datasource.get('name') == 'Parameters':
            logger.info("Creating parameters model")
            create_parameters_model(output_dir, datasource)
            break

    logger.info(f"Updating TMDL files from JSON: {json_file}")
    update_tmdl_files_from_json(output_dir, json_file)

    logger.info(f"Models written successfully to: {models_file}")
    logger.info(f"Relationships written successfully to: {relationships_file}")
    print(f"Relationships written to: {relationships_file}")

async def semantic_model_generation_v2(organization_name, user_email, twb_files_path, local_dir, process_id, extracted_path):
    """
    Orchestrates the semantic model generation for a list of TWB files.
    
    Args:
        organization_name (str): Organization name.
        user_email (str): User's email address.
        twb_files_path (List[str]): List of local paths to TWB files.
        local_dir (str): Local base directory for processing.
        process_id (str): Process identifier.
        extracted_path (str): Path where extracted assets (if any) are located.
    
    Returns:
        List[str]: List of download links for the generated models.
    """
    if not twb_files_path:
        raise ValueError(f" - No TWB files provided for semantic model generation.")

    print(f" - Starting semantic model generation for {len(twb_files_path)} TWB file(s).")
    
    download_links = []
    for idx, twb_file in enumerate(twb_files_path, start=1):
        try:
            print(f" - Processing file {idx}/{len(twb_files_path)}: {os.path.basename(twb_file)}")
            
            download_link = await process_single_file_v2(
                twb_file, local_dir, organization_name, user_email, process_id, extracted_path
            )
            
            if download_link:
                print(f" - Successfully processed {os.path.basename(twb_file)}. Download link: {download_link}")
                download_links.append(download_link)
            else:
                print(f" - WARNING: No download link generated for {os.path.basename(twb_file)}.")
        
        except Exception as e:
            print(f" - ERROR processing {os.path.basename(twb_file)}: {str(e)}")
            # Optionally: continue with next file instead of full failure
            continue

    if not download_links:
        raise Exception(f" - No semantic models were generated successfully.")

    print(f" - Semantic model generation completed for all files.")
    
    return download_links



async def process_single_file_v2(twb_file, local_dir, organization_name, user_email, process_id, extracted_path):
    twb_filename = os.path.basename(twb_file)
    start_time = time.time()

    logger.info(f" - Started processing file: {twb_filename}")
    logger.info(f" - File timestamp: {start_time}")

    try:
        semantic_model_path = extracted_path
        if not os.path.exists(semantic_model_path):
            raise FileNotFoundError(f"Semantic model path does not exist: {semantic_model_path}")
        logger.info(f" - Semantic model base path: {semantic_model_path}")

        # Prepare output directory
        short_name = os.path.splitext(twb_filename)[0][:2]
        powerbi_output_dir = os.path.join(local_dir, f'{short_name}_model_v2')
        os.makedirs(powerbi_output_dir, exist_ok=True)
        logger.info(f" - Created output directory: {powerbi_output_dir}")

        # Copy PowerBI structure
        definition_folder_path = copy_powerbi_structure_v2(source_path=semantic_model_path, dest_path=powerbi_output_dir)
        logger.info(f" - Copied PowerBI structure. Definition folder path: {definition_folder_path}")

        # Prepare important output paths
        tables_output_dir = os.path.join(definition_folder_path, 'tables')
        relationships_file = os.path.join(definition_folder_path, 'relationships.tmdl')
        models_file = os.path.join(definition_folder_path, 'models.tmdl')
        template_file_path = PathConfig.dateTable_template_path

        logger.info(f" - Output paths: tables={tables_output_dir}, relationships={relationships_file}, models={models_file}")

        # Extract and generate model
        json_data = get_calculations_data(twb_file)
        logger.info(f" - Extracted calculations data from TWB file.")

        extract_models_and_columns_with_lineage(
            twb_file, tables_output_dir, models_file, relationships_file, template_file_path, json_data
        )
        logger.info(f" - Extracted models and columns with lineage successfully.")

        zip_path = shutil.make_archive(powerbi_output_dir, 'zip', powerbi_output_dir)
        zip_filename = os.path.basename(zip_path)
        unique_folder = f'{organization_name}/{user_email}'
        s3_pdf_key = f'{unique_folder}/{process_id}/semantic_model/{zip_filename}'

        await s3_config.upload_to_s3(zip_path, s3_pdf_key)
        presigned_url = await s3_config.generate_presigned_url(s3_pdf_key)

        download_link = {
            "file": zip_filename,
            "download_url": presigned_url
        }

        # # Upload and return download link
        # download_link = upload_and_generate_download_link(
        #     powerbi_output_dir, organization_name, user_email, process_id
        # )
        logger.info(f" - Uploaded semantic model package. Download link: {download_link}")

        end_time = time.time()
        logger.info(f" - Completed processing {twb_filename} in {end_time - start_time:.2f} seconds.")
        return download_link

    except Exception as e:
        logger.error(f" - Error processing {twb_filename}: {str(e)}", exc_info=True)
        raise

def copy_powerbi_structure_v2(source_path, dest_path):
    start_time = time.time()
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(start_time))}] - Starting copy from {source_path} to {dest_path}")

    if not os.path.exists(source_path):
        raise FileNotFoundError(f"Source path does not exist: {source_path}")

    os.makedirs(dest_path, exist_ok=True)

    file_count = 0
    for root, dirs, files in os.walk(source_path):
        for filename in files:
            if filename.endswith('.twb') or filename == 'models.tmdl':
                continue  # Skip copying .twb files and models.tmdl file

            src_path = os.path.join(root, filename)
            relative_path = os.path.relpath(src_path, source_path)
            dest_file_path = os.path.join(dest_path, relative_path)

            os.makedirs(os.path.dirname(dest_file_path), exist_ok=True)
            shutil.copy2(src_path, dest_file_path)
            print(f"Copied file: {src_path} -> {dest_file_path}")
            file_count += 1

    print(f"Total files copied (excluding .twb): {file_count}")

    semantic_model_path = None
    for root, dirs, _ in os.walk(dest_path):
        for dir_name in dirs:
            if '.SemanticModel' in dir_name:
                semantic_model_path = os.path.join(root, dir_name)
                print(f"Found SemanticModel folder at: {semantic_model_path}")
                break
        if semantic_model_path:
            break

    if not semantic_model_path:
        error_msg = f"No '.SemanticModel' folder found inside destination path: {dest_path}"
        print(f"ERROR - {error_msg}")
        raise Exception(error_msg)

    definition_folder_path = os.path.join(semantic_model_path, 'definition')
    print(f"Definition folder path set to: {definition_folder_path}")

    end_time = time.time()
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(end_time))}] - Completed copying structure. Time taken: {end_time - start_time:.2f} seconds.")

    return definition_folder_path