from sqlalchemy.orm import Session
from app.models.organization_details import OrganizationDetail, OrganizationDetailManager
from app.core import scoped_context
from app.core.exceptions import ConflictError
from app.core import BaseService, ServiceResponse

class OrganizationDetailsService(BaseService):
    @staticmethod
    def create_organization(organization) -> ServiceResponse:
        name = organization.name
        credits = organization.credits
        contact_person_name = organization.contact_person_name
        mobile_number = organization.mobile_number
        address = organization.address
        service_type = organization.service_type

        
        existing_org = OrganizationDetailManager.check_existing_mobile_or_name(mobile_number, name)
        if existing_org:
            if existing_org.mobile_number == mobile_number:
                raise ConflictError("An organization with this mobile number already exists.")
            if existing_org.name.lower() == name.lower():
                raise ConflictError("An organization with this name already exists.")

        org_detail = OrganizationDetail(
            name=name,
            credits=credits,
            contact_person_name=contact_person_name,
            mobile_number=mobile_number,
            address=address,
            service_type=service_type
        )
        new_org = OrganizationDetailManager.create_new_organization(org_detail)
        org_data = {
            "name": new_org.name,
            "credits": new_org.credits,
            "contact_person_name": new_org.contact_person_name,
            "mobile_number": new_org.mobile_number,
            "address": new_org.address,
            "service_type": new_org.service_type
        }
        return org_data


    @staticmethod
    def create_organization_service(organization) -> ServiceResponse:
        response = OrganizationDetailsService.create_organization(organization)
        return ServiceResponse.success(data=response)
