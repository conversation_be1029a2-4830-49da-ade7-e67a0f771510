import json,re
from services.migrate.core import target_source_in_tooltips,get_report_name_id_inpbi,get_tooltip_targets
def get_customized_tool_tips(data,section_list):
    worksheets_tooltips=target_source_in_tooltips(data)
    report_name_id_in_pbi=get_report_name_id_inpbi(section_list)
    work_sheets=data.get("workbook",{}).get("worksheets").get("worksheet",{})
    work_sheets=work_sheets if isinstance(work_sheets,list) else [work_sheets]
    tooltip_targets=get_tooltip_targets(worksheets_tooltips)
    for report in section_list:
        if report.get("displayName") in tooltip_targets:
            report["config"]=json.dumps(get_config_for_target())
            

    return section_list

def get_source_json(report,source_name,bold):
    source_json={"vcObjects":{"visualHeader":[{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}],"visualTooltip":[{"properties":{"show":{"expr":{"Literal":{"Value":"false"}}}}}],"title":[{"properties":{"show":{"expr":{"Literal":{"Value":"true"}}},"text":{"expr":{"Literal":{"Value":f"'{source_name}'"}}},"fontSize":{"expr":{"Literal":{"Value":"8D"}}},"fontFamily":{"expr":{"Literal":{"Value":"'Calibri'"}}},"fontColor":{"solid":{"color":{"expr":{"ThemeDataColor":{"ColorId":1,"Percent":0}}}}},"alignment":{"expr":{"Literal":{"Value":"'center'"}}},"bold":{"expr":{"Literal":{"Value":"true" if bold else"false"}}}}}]}}
    return report

def get_target_json(target_name,source_id,title_name,bold):
    target_json={"vcObjects":{"visualTooltip":[{"properties":{"type":{"expr":{"Literal":{"Value":"'Canvas'"}}},"section":{"expr":{"Literal":{"Value":f"'{source_id}'"}}},"fontSize":{"expr":{"Literal":{"Value":"8D"}}}}}],"title":[{"properties":{"text":{"expr":{"Literal":{"Value":f"'{title_name}'"}}},"show":{"expr":{"Literal":{"Value":"true"}}},"alignment":{"expr":{"Literal":{"Value":"'center'"}}},"fontFamily":{"expr":{"Literal":{"Value":"'Calibri'"}}},"fontSize":{"expr":{"Literal":{"Value":"10D"}}},"bold":{"expr":{"Literal":{"Value":"true" if bold else "false"}}}}}]}}
    return target_json

def get_config_for_target():
    return {"objects":{"displayArea":[{"properties":{"verticalAlignment":{"expr":{"Literal":{"Value":"'Top'"}}}}}]},"type":1}