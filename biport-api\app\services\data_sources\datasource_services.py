from app.models_old import DataSourceDetailsManager
from app.core import *
from app.core.enums import *
from app.models_old.user import UserManager
from app.schemas.datasources import DataSourceFormation
from app.services.data_sources.data_sources_core import DatasourcesDetailsHandling
from app.services.data_sources.datasource_messages import Messages as msg

class DSServices(BaseService):
    """All operations related to Data Sources."""

    @staticmethod
    def checking_foreignkey_relation(func):
        def wrapper(request, *args, **kwargs):
            record = UserManager.check_user_existence_by_id(request)
            if record is None:
                raise NotFoundError(detail = msg.USER_NOT_EXISTED.value)
            return func(request, *args, **kwargs)
        return wrapper

    @staticmethod
    def checking_user_existance(func):
        def wrapper(request, *args, **kwargs):

            record = DataSourceDetailsManager.get_user_data(request)
            if record is None:
                return ConflictError(detail = msg.USER_EXISTED_ERROR.value)
            return func(request, *args, **kwargs)
        return wrapper

    @staticmethod
    def checking_ds_type(func):
        def wrapper(request, *args, **kwargs):
            ds_type = getattr(request, "ds_type", None)
            if ds_type is None:
                return func(request, *args, **kwargs)
            if ds_type not in Datasource_types.ALLOWED_TYPES.value:
                raise NotFoundError(detail = msg.DATA_SOURCE_NOT_EXISTED.value)
            return func(request, *args, **kwargs)
        return wrapper

    @staticmethod
    @checking_foreignkey_relation
    def add_new_users_data_sources(request):
        """Adding a new user's data source."""
        ds_details_list = {}
        record = DataSourceFormation(user_id = request.user_id)
        new_ds = DatasourcesDetailsHandling.get_ds_format(request.ds_type, request.ds_details)
        ds_details_list[request.ds_type] = encoding([new_ds])
        record = DatasourcesDetailsHandling.get_record(ds_details_list, record)
        DataSourceDetailsManager.add_new_users_ds(record)
        return ServiceResponse.success({"message": msg.USER_DETAILS_ADDED.value})

    @staticmethod
    @checking_foreignkey_relation
    @checking_ds_type
    def add_data_source_details(request):
        """Appending details to an existing user's data sources."""
        record = DataSourceDetailsManager.get_user_data(request)
        if record is None:
            return DSServices.add_new_users_data_sources(request)
        new_ds = DatasourcesDetailsHandling.get_ds_format(request.ds_type, request.ds_details)
        data_sources_list = decoding(record.__dict__.get(request.ds_type, "")) or []
        if new_ds in data_sources_list:
            raise NotFoundError(detail = msg.USER_EXISTED_ERROR.value)
        data_sources_list.insert(0, new_ds)
        record = DatasourcesDetailsHandling.get_record(
            {request.ds_type: encoding(data_sources_list)}, record
        )
        DataSourceDetailsManager.update_data_source_details(record)
        return ServiceResponse.success({"message": msg.USER_DETAILS_ADDED.value})

    @staticmethod
    def remove_user_details(request):
        """Removing user details."""
        ds_type = getattr(request, "ds_type", None)
        ds_details = getattr(request, "ds_details", None)
        record = DataSourceDetailsManager.get_user_data(request)
        if record is None:
            raise NotFoundError(detail = msg.USER_DETAILS_NOT_EXISTED.value)
        if ds_type is None or ds_details is None:
            DataSourceDetailsManager.remove_user(request)
            return ServiceResponse.success({"message": msg.USER_DETAILS_REMOVED.value})
        else:
            record_dict = record.__dict__
            ds_details_list = decoding(record_dict.get(request.ds_type, []))
            deleting_ds = DatasourcesDetailsHandling.get_ds_format(request.ds_type, request.ds_details)
            if deleting_ds not in ds_details_list:
                raise NotFoundError(detail = msg.DATA_SOURCE_NOT_EXISTED.value)
            ds_details_list.remove(deleting_ds)
            record_dict[request.ds_type] = encoding(ds_details_list)
            record = DatasourcesDetailsHandling.get_record(record_dict, record)
            DataSourceDetailsManager.update_data_source_details(record)
            return ServiceResponse.success({"message": msg.DATASOURCE_DETAILS_REMOVED.value})

    @staticmethod
    @checking_foreignkey_relation
    @checking_user_existance
    @checking_ds_type
    def get_user_details(request):
        """Getting data sources of a user."""
        ds_type = getattr(request, "ds_type", None)
        record = DataSourceDetailsManager.get_user_data(request)
        for key, values in record.__dict__.items():
            if key in Datasource_types.ALLOWED_TYPES.value:
                record.__dict__[key] = decoding(values)
            if key in ImportantKeys.KEYS_TO_CHANGE_DTYPES_TO_STR.value:
                record.__dict__[key] = str(values)

        if ds_type is None:
            return ServiceResponse.success({"message": record.__dict__})
        else:
            return ServiceResponse.success({"message": record.__dict__.get(ds_type, {})})

    @staticmethod
    @checking_foreignkey_relation
    @checking_user_existance
    @checking_ds_type
    def update_the_existing_datasources(request):
        """Updating existing data sources for a user."""
        record = DataSourceDetailsManager.get_user_data(request)
        record_dict = record.__dict__
        ds_details_list = decoding(record_dict.get(request.ds_type, [])) or []
        old_ds = DatasourcesDetailsHandling.get_ds_format(request.ds_type, request.old_ds)
        new_ds = DatasourcesDetailsHandling.get_ds_format(request.ds_type, request.new_ds)
        if old_ds not in ds_details_list:
            raise NotFoundError(detail = msg.DATA_SOURCE_NOT_EXISTED.value)
        if new_ds in ds_details_list:
            raise NotFoundError(detail = msg.DATA_SOURCE_EXISTED.value)
        index = ds_details_list.index(old_ds)
        ds_details_list[index] = new_ds
        record_dict[request.ds_type] = encoding(ds_details_list)
        record = DatasourcesDetailsHandling.get_record(record_dict, record)
        DataSourceDetailsManager.update_data_source_details(record)
        return ServiceResponse.success({"message": msg.USER_DETAILS_UPDATED.value})