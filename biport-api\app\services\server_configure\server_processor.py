from app.models_old.user import UserOld
from app.schemas import AddServerRequest, UpdateServerRequest, UpdateServerStatusRequest
from .server_service import ServerService
from app.core import ServiceResponse
from fastapi import BackgroundTasks,HTTPException
import uuid
import os
from app.schemas import DeleteServerRequest
from app.schemas import AddTableauServerRequest
from app.models.users import User

        

class ServerProcessor:
    """Handles validation and processing of server requests."""

    @staticmethod
    def process_add_server(request: AddServerRequest, user: UserOld, background_tasks: BackgroundTasks) -> ServiceResponse:
        """Processes the add server request with generic exception handling."""
        return ServerService.execute(ServerService().add_server, request, user, background_tasks)

    @staticmethod
    async def process_delete_server(server_id) -> ServiceResponse:
        """Processes the delete server request with generic exception handling."""
        return await ServerService.execute(ServerService().delete_server, server_id)

    @staticmethod
    def process_update_server(request: UpdateServerRequest, user: UserOld) -> ServiceResponse:
        """Processes the update server request with generic exception handling."""
        return ServerService.execute(ServerService().update_server, request, user)

    @staticmethod
    def process_get_server(server_id: uuid.UUID) -> ServiceResponse:
        """Processes the get server request with generic exception handling."""
        return ServerService.execute(ServerService().get_server, server_id)

    @staticmethod
    def process_get_servers(page: int, page_size: int) -> ServiceResponse:
        """Processes the get servers request with generic exception handling."""
        return ServerService.execute(ServerService().get_servers, page, page_size)

    @staticmethod
    def process_get_org_servers(organization_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Processes the get organisation servers request with generic exception handling."""
        return ServerService.execute(ServerService().get_org_servers, organization_id, page, page_size)

    @staticmethod
    def process_update_server_status(request: UpdateServerStatusRequest) -> ServiceResponse:
        """Processes the update server status request with generic exception handling."""
        return ServerService.execute(ServerService().update_server_status, request)

    @staticmethod
    def update_tableau_server_status( request: UpdateServerStatusRequest) -> ServiceResponse:
        """Process update TableauServerDetail status request."""
        return ServerService.execute(ServerService().update_tableau_server_status, request)

    @staticmethod
    def process_soft_delete_tableau_server(request: DeleteServerRequest) -> ServiceResponse:
        """Process soft delete TableauServerDetail request."""
        return ServerService.execute(ServerService().soft_delete_tableau_server, request)

    @staticmethod
    def process_add_tableau_server(request: AddTableauServerRequest, user: User, background_tasks: BackgroundTasks) -> ServiceResponse:
        """Processes the add TableauServerDetail request with generic exception handling."""
        return ServerService.execute(ServerService().add_tableau_server, request, user, background_tasks)

    @staticmethod
    def process_get_user_projects(user_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Processes the get user projects request with generic exception handling."""
        return ServerService().get_user_projects(user_id, page, page_size)

    @staticmethod
    def process_get_root_projects(user_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Processes the get root projects request with generic exception handling."""
        return ServerService().get_root_projects_for_user(user_id, page, page_size)

    @staticmethod
    def process_get_projects_by_parent(parent_id: uuid.UUID, user_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Processes the get projects by parent request with generic exception handling."""
        return ServerService().get_projects_by_parent_for_user(parent_id, user_id, page, page_size)

    @staticmethod
    def process_create_project(request, user) -> 'ServiceResponse':
        """Processes the create project request with generic exception handling."""
        return ServerService().create_project(request, user, is_upload=True)

    @staticmethod
    def process_update_project_name(request, user) -> 'ServiceResponse':
        """Processes the update project name request with generic exception handling."""
        return ServerService().update_project_name(request, user)

    @staticmethod
    def process_delete_project(project_id) -> 'ServiceResponse':
        return ServerService().delete_project(project_id)

    @staticmethod
    def process_delete_report(report_id) -> 'ServiceResponse':
        return ServerService().delete_report(report_id)

    @staticmethod
    async def process_edit_report_name(report_id, new_name, user) -> 'ServiceResponse':
        return await ServerService().edit_report_name(report_id, new_name, user)

    @staticmethod
    async def process_upload_file(file, project_id, user):
        """Orchestrates file upload: validation, duplicate checks, and calls the service for DB/S3 operations."""
        ext = os.path.splitext(file.filename)[1].lower()
        allowed_exts = ['.twb', '.twbx', '.zip']
        if ext not in allowed_exts:
            return type('Resp', (), {'data': None, 'error': 'Only .twb, .twbx, or .zip files are allowed.', 'status_code': 400})()
        return await ServerService().upload_file(file, project_id, user)
