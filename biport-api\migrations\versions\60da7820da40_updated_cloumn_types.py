"""updated cloumn types

Revision ID: 60da7820da40
Revises: 7416d32c6beb
Create Date: 2025-06-10 15:47:32.374367

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '60da7820da40'
down_revision: Union[str, None] = '7416d32c6beb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
   
    op.add_column('report_details', sa.Column('server_id', sa.UUID(), nullable=True))
    op.alter_column('report_details', 'id',
               existing_type=sa.UUID(),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.drop_constraint('report_details_serverid_fkey', 'report_details', type_='foreignkey')
    op.create_foreign_key(None, 'report_details', 'server_details', ['server_id'], ['id'])
    op.drop_column('report_details', 'serverid')
    op.alter_column('s3_credentials', 'id',
               existing_type=sa.UUID(),
               type_=sa.String(length=36),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('s3_credentials', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.add_column('report_details', sa.Column('serverid', sa.UUID(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'report_details', type_='foreignkey')
    op.create_foreign_key('report_details_serverid_fkey', 'report_details', 'server_details', ['serverid'], ['id'])
    op.alter_column('report_details', 'id',
               existing_type=sa.String(length=36),
               type_=sa.UUID(),
               existing_nullable=False)
    op.drop_column('report_details', 'server_id')
    # ### end Alembic commands ###
