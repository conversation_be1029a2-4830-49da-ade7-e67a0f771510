import asyncio
import os
import tempfile
import zipfile
from typing import List, Optional
from uuid import UUID

from app.services.folder.folder_helper import build_file_info,build_project_summary
from botocore.exceptions import NoCredentialsError
from fastapi import UploadFile
from starlette.concurrency import run_in_threadpool

from app.core.exceptions import BadRequestError, ServerError
from app.core.base_service import BaseService
from app.core.response import ServiceResponse
from app.core.session import scoped_context
from app.core.config import logger, S3Config
from app.models_old.folders import FolderManager
from app.schemas.folders import FolderCreate, FolderResponse, FolderTree, FolderUpdate
from app.models_old.upload_file_report_details import UploadFilesReportManager

s3_config = S3Config()

class FolderService(BaseService):

    @staticmethod
    def get_folder_path(folder_id: int) -> str:
        """
        Retrieves the full folder path for a given folder by traversing up its parent hierarchy.
        Constructs the path from the folder's root to the current folder.
        """
        if folder_id is None:
            logger.info("No parent folder ID provided. Returning empty path.")
            return ""
        logger.info(f"Fetching folder path for folder_id: {folder_id}")
        folder = FolderManager.get_folder_by_id(folder_id)
        if not folder:
            logger.warning(f"Folder with ID {folder_id} not found")
            return ""
        
        path = folder.name
        logger.debug(f"Starting path: {path}")

        while folder.parent_id:
            folder = FolderManager.get_folder_by_id(folder.parent_id)
            if not folder:
                logger.error(f"Parent folder with ID {folder.parent_id} not found")
                break
            path = f"{folder.name}/{path}"
            logger.debug(f"Updated path: {path}")

        logger.info(f"Final folder path for folder_id {folder_id}: {path}")
        return path

    @staticmethod
    async def build_folder_tree(folders, parent_id=None, path_segments=None):
        """
        Builds the folder tree structure for the given folders, including the necessary file data.
        It recursively constructs folder paths and retrieves file URLs if the folder is a file.
        """
        from app.services.folder.folder_helper import extract_s3_key
        logger.info(f"Building folder tree for parent_id: {parent_id}")
        
        if path_segments is None:
            path_segments = {}
        
        tree = []

        def get_tree_part():
            """Helper function to gather folder tree parts with file information."""
            local_tree = []
            logger.info(f"Fetching folders for parent_id {parent_id}")
            with scoped_context() as session:
                folder_items = FolderManager.get_folders_by_parent(parent_id)

                for item in folder_items:
                    current_path = path_segments.get(parent_id, "")
                    node_path = f"{current_path}/{item.name}" if current_path else item.name
                    path_segments[item.id] = node_path.lstrip('/')

                    node = FolderTree(
                        id=item.id,
                        name=item.name,
                        parent_id=item.parent_id,
                        is_file=item.is_file,
                        is_deleted=item.is_deleted,
                        created_at=item.created_at,
                        updated_at=item.updated_at,
                        folder_path=node_path.lstrip('/')
                    )
                    logger.info(f"Created FolderTree node for {node.name} with path {node.folder_path}")
                    
                    if item.is_file:
                        file = FolderManager.get_file_for_folder(item.id)
                        if file and file.filepath:
                            s3_key = extract_s3_key(file.filepath)
                            node.file_url = s3_key
                            logger.info(f"File found for {node.name}, S3 key: {s3_key}")
                        else:
                            node.file_url = None
                            logger.warning(f"No file found for folder {node.name}")

                    local_tree.append((node, item))

            return local_tree

        try:
            tree_parts = await run_in_threadpool(get_tree_part)
            logger.info(f"Tree parts retrieved: {len(tree_parts)}")
            for node, item in tree_parts:
                if item.is_file:
                    if node.file_url:
                        node.file_url = await s3_config.generate_presigned_url(node.file_url)
                        logger.info(f"Generated pre-signed URL for file {node.name}: {node.file_url}")
                    else:
                        node.file_url = None
                else:
                    node.children = await FolderService.build_folder_tree(folders, item.id, path_segments)
                tree.append(node)
            logger.info(f"Successfully built folder tree with {len(tree)} nodes")
        except Exception as e:
            logger.error(f"Error building folder tree: {e}", exc_info=True)

        return tree

    @staticmethod
    async def get_all_folders() -> ServiceResponse:
        """
        Retrieves all active folders and builds their folder tree structure.
        """
        logger.info("Fetching all active folders")
        folders = FolderManager.get_all_active_folders()
        if not folders:
            logger.warning("No active folders found")
            return []
        folder_tree = await FolderService.build_folder_tree(folders)
        logger.info(f"Successfully retrieved {len(folder_tree)} folder(s)")
        # return folder_tree
        return ServiceResponse.success(data=[folder.dict() for folder in folder_tree])

    @staticmethod
    async def main_create_folder(folder: FolderCreate) -> ServiceResponse:
        """
        Creates a new folder and uploads an empty folder to S3.
        """
        logger.info(f"Creating folder with name: {folder.name}")
        new_folder, parent_s3_path = await FolderManager.main_create_folder(folder)
        s3_config = S3Config()

        async with s3_config.get_s3_client() as s3_client:
            s3_key = f"{parent_s3_path}/{new_folder.name}/".lstrip('/')
            await s3_client.put_object(Bucket=s3_config.bucket_name, Key=s3_key)
            logger.info(f"Uploaded empty folder to S3 at key: {s3_key}")

        folder_response = FolderResponse(
            id=new_folder.id,
            name=new_folder.name,
            parent_id=new_folder.parent_id,
            is_file=new_folder.is_file,
            is_deleted=new_folder.is_deleted,
            created_at=new_folder.created_at,
            updated_at=new_folder.updated_at,
            filepath=None,
            file_url=None
        )

        logger.info(f"Folder created successfully with ID: {new_folder.id}")
        return ServiceResponse.success(data=folder_response.dict())

    @staticmethod
    async def get_folders_by_parent(parent_id: Optional[UUID]) -> ServiceResponse:
        """
        Retrieves all folders by a given parent folder ID, including their file URLs if applicable.
        """
        logger.info(f"Fetching folders for parent ID: {parent_id}")
        folders = FolderManager.get_folders_by_parent(parent_id)
        if not folders:
            logger.warning(f"No folders found for parent ID: {parent_id}")
        
        s3_config = S3Config()
        results = []

        async with s3_config.get_s3_client() as s3_client:
            for folder in folders:
                folder_path = FolderService.get_folder_path(folder.id)

                file_url = None
                filepath = None
                if folder.is_file:
                    file = FolderManager.get_file_for_folder(folder.id)
                    if file:
                        filepath = file.filepath
                        s3_key = file.filepath.replace("s3://my-folder-project/", "")
                        file_url = await s3_client.generate_presigned_url(
                            'get_object',
                            Params={'Bucket': 'my-folder-project', 'Key': s3_key},
                            ExpiresIn=3600
                        )
                        logger.info(f"Generated pre-signed URL for file: {file_url}")
                    else:
                        logger.warning(f"No file associated with folder ID {folder.id}")
                
                folder_response = FolderResponse(
                    id=folder.id,
                    name=folder.name,
                    parent_id=folder.parent_id,
                    is_file=folder.is_file,
                    is_deleted=folder.is_deleted,
                    created_at=folder.created_at,
                    updated_at=folder.updated_at,
                    filepath=filepath,
                    file_url=file_url,
                    folder_path=folder_path
                )
                results.append(folder_response.dict())

        logger.info(f"Fetched {len(results)} folder(s) under parent ID {parent_id}")
        # return results
        return ServiceResponse.success(data=results)


    @staticmethod
    async def get_null_folders() -> ServiceResponse:
        """
        Retrieves all root folders that do not have any files or children.
        """
        logger.info("Fetching all root folders that do not have files or children.")
        
        def _get_null_folders_sync():
            with scoped_context() as db:
                root_folders = FolderManager.get_root_folders()
                if not root_folders:
                    logger.warning("No root folders found")
                
                result = []
                for folder in root_folders:
                    folder_path = FolderService.get_folder_path(folder.id)
                    folder_data = FolderResponse(
                        id=folder.id,
                        uuid=folder.id,
                        name=folder.name,
                        parent_id=folder.parent_id,
                        is_file=folder.is_file,
                        is_deleted=folder.is_deleted,
                        created_at=folder.created_at,
                        updated_at=folder.updated_at,
                        filepath=None,
                        file_url=None,
                        folder_path=folder_path
                    )

                    if folder.is_file:
                        file = FolderManager.get_file_for_folder(folder.id)
                        if file:
                            folder_data.filepath = file.filepath

                    result.append(folder_data)
                return result

        folders = await run_in_threadpool(_get_null_folders_sync)

        logger.info(f"Fetched {len(folders)} null root folder(s)")
        return ServiceResponse.success(data=[folder.dict() for folder in folders])

    @staticmethod
    async def soft_delete_folder(folder_id: str) -> ServiceResponse:
        """
        Soft deletes a folder by marking it as deleted in the database and removing its corresponding
        files and folder data from S3.
        """
        logger.info(f"Initiating soft delete for folder with ID: {folder_id}")

        def _soft_delete_db():
            with scoped_context() as db:
                s3_path_to_delete = FolderManager.soft_delete_folder_from_db(folder_id, db)
                db_report = UploadFilesReportManager.delete_by_folder_id(folder_id, db)
                return s3_path_to_delete

        s3_path_to_delete = await run_in_threadpool(_soft_delete_db)
        s3_config = S3Config()

        async with s3_config.get_s3_client() as s3_client:
            s3_prefix = f"{s3_path_to_delete}/"
            logger.info(f"Attempting to delete S3 objects under prefix: {s3_prefix}")

            objects_to_delete = await s3_client.list_objects_v2(Bucket=s3_config.bucket_name, Prefix=s3_prefix)

            if 'Contents' in objects_to_delete:
                delete_keys = {'Objects': [{'Key': obj['Key']} for obj in objects_to_delete['Contents']]}
                if delete_keys['Objects']:
                    response = await s3_client.delete_objects(Bucket=s3_config.bucket_name, Delete=delete_keys)
                    if 'Errors' in response:
                        logger.error(f"S3 Deletion Errors: {response['Errors']}")
                    else:
                        logger.info(f"Deleted {len(delete_keys['Objects'])} objects from S3: {s3_prefix}")
            else:
                logger.warning(f"No S3 objects found for prefix: {s3_prefix}")

            await s3_client.delete_object(Bucket=s3_config.bucket_name, Key=s3_prefix[:-1])
            logger.info(f"Deleted S3 folder object: {s3_prefix[:-1]}")

        return ServiceResponse.success(data={"message": "Folder deleted successfully", "folder_id": folder_id})

    @staticmethod
    async def update_folder(folder_id: UUID, folder_update: FolderUpdate) -> ServiceResponse:
        """
        Updates the details of an existing folder.
        """
        logger.info(f"Updating folder with ID: {folder_id}")

        def _update_folder_sync():
            with scoped_context() as db:
                updated_folder = FolderManager.update_folder_in_db(folder_id, folder_update, db)
                return updated_folder

        updated_folder = await run_in_threadpool(_update_folder_sync)

        logger.info(f"Folder with ID {folder_id} updated successfully.")
        
        return ServiceResponse.success(data={
            "id": str(updated_folder.id),
            "name": updated_folder.name,
            "parent_id": str(updated_folder.parent_id) if updated_folder.parent_id else None,
            "is_file": updated_folder.is_file,
            "is_deleted": updated_folder.is_deleted,
            "created_at": updated_folder.created_at.isoformat() if updated_folder.created_at else None,
            "updated_at": updated_folder.updated_at.isoformat() if updated_folder.updated_at else None,
            "filepath": None,
            "file_url": None
        })



    @staticmethod
    async def upload_file(uploaded_file: List[UploadFile], parent_id: Optional[str]) -> ServiceResponse:
        upload_results = []
        s3_config = S3Config()
        if parent_id is not None:
            try:
                parent_id = UUID(str(parent_id))
            except ValueError:
                raise BadRequestError(detail="Invalid parent_id format. Expected UUID.")

        try:
            logger.info("Started the file upload process.")
            with scoped_context() as db:
                async with s3_config.get_s3_client() as s3_client:

                    async def save_directory_contents_s3(directory, s3_prefix, db_session, parent_id):
                        for item_name in os.listdir(directory):
                            item_path = os.path.join(directory, item_name)

                            if not os.path.isdir(item_path) and not os.path.isfile(item_path):
                                continue

                            is_dir = os.path.isdir(item_path)
                            is_file_item = os.path.isfile(item_path)
                            s3_key_name = f"{s3_prefix}/{item_name}".lstrip('/')

                            new_folder = FolderManager.create_folder(item_name, parent_id, 0 if is_dir else 1)
                            new_folder_id = new_folder.id

                            if is_dir:
                                await save_directory_contents_s3(item_path, s3_key_name, db_session, new_folder_id)
                            elif is_file_item:
                                try:
                                    logger.info(f"Uploading file: {item_name} to S3.")
                                    with open(item_path, 'rb') as f:
                                        await s3_client.upload_fileobj(f, s3_config.bucket_name, s3_key_name)
                                    FolderManager.add_file_record(db_session, item_name, s3_key_name, new_folder_id)
                                    FolderManager.add_path_upload_file_reportdetails(db_session, s3_key_name)
                                    logger.info(f"File {item_name} uploaded successfully.")
                                except Exception as e:
                                    logger.error(f"Invalid ZIP file: {filename}")
                                    raise ServerError(detail=f"Error uploading {item_name} to S3: {str(e)}")
 


                    for file in uploaded_file:
                        filename = file.filename
                        try:
                            logger.info(f"Processing file: {filename}")
                            is_zip = filename.endswith(".zip")

                            if is_zip:
                                with tempfile.TemporaryDirectory() as tmpdir:
                                    zip_path = os.path.join(tmpdir, filename)
                                    with open(zip_path, "wb") as f:
                                        f.write(await file.read())

                                    try:
                                        logger.info(f"Extracting ZIP file: {filename}")
                                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                            zip_ref.extractall(tmpdir)
                                    except zipfile.BadZipFile:
                                        logger.error(f"Invalid ZIP file: {filename}")
                                        raise BadRequestError(detail=f"Invalid ZIP file: {filename}")

                                    upload_root = tmpdir
                                    current_parent_id = parent_id
                                    parent_s3_path = FolderService.get_folder_path(current_parent_id) if current_parent_id else ""

                                    for item_name in os.listdir(upload_root):
                                        item_path = os.path.join(upload_root, item_name)
                                        if item_path == zip_path:
                                            continue

                                        s3_root_key = f"{parent_s3_path}/{item_name}".lstrip('/')
                                        main_folder = FolderManager.create_folder(item_name, current_parent_id, 0)
                                        main_folder_id = main_folder.id
                                        await save_directory_contents_s3(item_path, s3_root_key, db, main_folder_id)

                                    upload_results.append({
                                        "filename": filename,
                                        "message": "Zip extracted and files/folders saved to S3 with hierarchy."
                                    })

                            else:
                                logger.info(f"Uploading file to S3: {filename}")
                                s3_key = f"{FolderService.get_folder_path(parent_id)}/{filename}".lstrip('/')
                                await s3_client.upload_fileobj(file.file, s3_config.bucket_name, s3_key)

                                existing_folder = FolderManager.get_existing_folder(filename, parent_id)

                                if not existing_folder:
                                    folder = FolderManager.create_folder(filename, parent_id, 1)
                                    folder_id = folder.id
                                else:
                                    folder_id = existing_folder.id

                                FolderManager.add_file_record(db, filename, s3_key, folder_id)
                                parent_id = FolderManager.get_parent_id_from_folder(folder_id)
                                FolderManager.add_path_upload_file_reportdetails(db, s3_key, parent_id)

                                upload_results.append({
                                    "filename": filename,
                                    "s3_key": s3_key,
                                    "folder_id": folder_id,
                                    "message": "File uploaded successfully to S3"
                                })

                        except NoCredentialsError:
                            logger.error("AWS credentials not properly configured")
                            raise ServerError(detail="AWS credentials not properly configured")
                        except Exception as e:
                            logger.error(f"Upload failed for {filename}: {str(e)}")
                            raise BadRequestError(detail=f"Upload failed for {filename}: {str(e)}")
                        
            logger.info("File upload process completed successfully.")
            return ServiceResponse.success(upload_results)

        except Exception as e:
            logger.error(f"Error during upload process: {str(e)}")
            return ServiceResponse.failure(str(e), 500)


    @staticmethod
    async def get_folder_tree(folder_id: UUID) -> ServiceResponse:
        logger.info(f"Fetching folder tree for ID: {folder_id}")
        root_folder = FolderManager.get_folder_by_id(folder_id)
        if not root_folder:
            logger.info(f"Folder not found with ID: {folder_id}")
            return ServiceResponse.error(message="Folder not found", status_code=404)

        s3_config = S3Config()

        async def build_tree(current_folder):
            folder_path = FolderService.get_folder_path(current_folder.id)
            filepath = None
            file_url = "unavailable"
            usage = 0

            if current_folder.is_file:
                file = FolderManager.get_file_for_folder(current_folder.id)
                if file and file.filepath:
                    usage = 1
                    filepath = file.filepath
                    # s3_key = extract_s3_key(file.filepath, s3_config.bucket_name)
                    s3_key = S3Config.extract_s3_key(file.filepath, s3_config.bucket_name)
                    try:
                        file_url = await s3_config.generate_presigned_url(s3_key)
                    except Exception as e:
                        logger.warning(f"Failed to generate URL for file {file.id}: {e}")
                else:
                    logger.warning(f"No file associated with folder ID {current_folder.id}")

            folder_tree = FolderTree(
                id=current_folder.id,
                name=current_folder.name,
                parent_id=current_folder.parent_id,
                is_file=current_folder.is_file,
                is_deleted=current_folder.is_deleted,
                created_at=current_folder.created_at,
                updated_at=current_folder.updated_at,
                filepath=filepath,
                file_url=file_url,
                folder_path=folder_path,
                children=[],
                usage=usage
            )

            children = FolderManager.get_folders_by_parent(current_folder.id)
            for child in children:
                child_tree = await build_tree(child)
                folder_tree.children.append(child_tree)
                folder_tree.usage += child_tree.usage

            return folder_tree

        tree = await build_tree(root_folder)
        logger.info(f"Fetched folder tree for ID: {folder_id}")
        return ServiceResponse.success(data={"content": tree.dict()})



    @staticmethod
    async def get_folder_summary() -> ServiceResponse:
        folders = FolderManager.fetch_active_folders_with_files()
        if not folders:
            return ServiceResponse.success(data=[])

        s3_config = S3Config()
        children_map = {}

        for folder in folders:
            if folder.parent_id:
                children_map.setdefault(folder.parent_id, []).append(folder)

        async def build_tree(folder):
            node = {
                "project_id": str(folder.id),
                "project_name": folder.name,
                "is_file": folder.is_file,
                "number_of_workbooks": 0,
            }

            if folder.is_file:
                valid_files = [file for file in folder.files if file and file.id and file.filename]
                node["number_of_workbooks"] = len(valid_files)
                if valid_files:
                    s3_keys = [
                        S3Config.extract_s3_key(file.filepath, s3_config.bucket_name)
                        for file in valid_files
                    ]
                    file_urls = await asyncio.gather(
                        *[s3_config.generate_presigned_url(s3_key) for s3_key in s3_keys],
                        return_exceptions=True
                    )

                    node["files"] = []
                    for file, file_url, s3_key in zip(valid_files, file_urls, s3_keys):
                        if isinstance(file_url, Exception):
                            logger.warning(f"Failed to generate presigned URL for {file.filename}: {file_url}")
                            file_url = "unavailable"
                        node["files"].append(build_file_info(file.id, file.filename, s3_key, file_url))
            else:
                child_nodes = await asyncio.gather(
                    *[build_tree(child) for child in children_map.get(folder.id, [])]
                )
                node["children"] = child_nodes
                node["number_of_workbooks"] = sum(child["number_of_workbooks"] for child in child_nodes)
            return node

        async with s3_config.get_s3_client() as _:
            root_folders = [f for f in folders if not f.parent_id]
            tree = await asyncio.gather(*[build_tree(folder) for folder in root_folders])

        return ServiceResponse.success(data=tree)
    

    @staticmethod
    async def get_folder_summary_by_project_id(project_id: UUID) -> ServiceResponse:
        folder = FolderManager.get_folder_with_files_by_id(project_id)
        if not folder:
            return ServiceResponse.success(data=[])

        s3_config = S3Config()
        valid_files = []
        s3_keys = []

        for file in folder.files:
            if not file.filename or not file.id:
                continue
            s3_key = S3Config.extract_s3_key(file.filepath, s3_config.bucket_name)
            s3_keys.append(s3_key)
            valid_files.append(file)

        file_url_map = {}

        async with s3_config.get_s3_client() as s3_client:
            for file, s3_key in zip(valid_files, s3_keys):
                try:
                    file_url = await s3_config.generate_presigned_url(s3_key)
                except Exception as e:
                    logger.warning(f"Failed to generate presigned URL for {s3_key}: {e}")
                    file_url = "unavailable"
                file_url_map[file.id] = file_url

        files_info = [
            build_file_info(file.id, file.filename, s3_key, file_url_map.get(file.id))
            for file in valid_files
        ]
        for files in files_info:
            uploaded_path = files.get("s3_key")
            report = None
            if uploaded_path:
                report = await run_in_threadpool(UploadFilesReportManager.get_by_s3_path, uploaded_path)
            if report:
                files["is_migrated"] = report.is_migrated
                files["is_converted"] = report.is_converted
                files["is_analyzed"] = report.is_analyzed
            else:
                files["is_migrated"] = None
                files["is_converted"] = None
                files["is_analyzed"] = None
        project_summary = build_project_summary(folder.id, folder.name, files_info)

        return ServiceResponse.success(data=[project_summary])


