<?xml version='1.0' encoding='utf-8' ?>

<!-- build 20242.24.0807.0327                               -->
<workbook include-phone-layouts='false' original-version='18.1' source-build='2024.2.2 (20242.24.0807.0327)' source-platform='win' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <document-format-change-manifest>
    <_.fcp.AccessibleZoneTabOrder.true...AccessibleZoneTabOrder />
    <_.fcp.AnimationOnByDefault.true...AnimationOnByDefault />
    <AutoCreateAndUpdateDSDPhoneLayouts />
    <IncludePhoneLayoutsOptOut />
    <_.fcp.MarkAnimation.true...MarkAnimation />
    <_.fcp.ObjectModelEncapsulateLegacy.true...ObjectModelEncapsulateLegacy />
    <_.fcp.ObjectModelRelationshipPerfOptions.true...ObjectModelRelationshipPerfOptions />
    <_.fcp.ObjectModelTableType.true...ObjectModelTableType />
    <_.fcp.SchemaViewerObjectModel.true...SchemaViewerObjectModel />
    <SetMembershipControl />
    <SheetIdentifierTracking />
    <WindowsPersistSimpleIdentifiers />
    <WorksheetBackgroundTransparency />
  </document-format-change-manifest>
  <preferences>
    <preference name='ui.encoding.shelf.height' value='24' />
    <preference name='ui.shelf.height' value='26' />
  </preferences>
  <_.fcp.AnimationOnByDefault.false...style>
    <_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule element='animation'>
      <_.fcp.AnimationOnByDefault.false...format attr='animation-on' value='ao-on' />
    </_.fcp.AnimationOnByDefault.false..._.fcp.MarkAnimation.true...style-rule>
  </_.fcp.AnimationOnByDefault.false...style>
  <datasources>
    <datasource hasconnection='false' inline='true' name='Parameters' version='18.1'>
      <aliases enabled='yes' />
      <column caption='Parameter 1' datatype='integer' name='[Parameter 1]' param-domain-type='list' role='measure' type='quantitative' value='10'>
        <calculation class='tableau' formula='10' />
        <members />
      </column>
      <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
        <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        <members>
          <member value='&quot;PROFIT&quot;' />
          <member value='&quot;SALES&quot;' />
          <member value='&quot;COST&quot;' />
        </members>
      </column>
    </datasource>
    <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' inline='true' name='federated.1nl426t13auwkc10rmst40m1iuwe' version='18.1'>
      <connection class='federated'>
        <named-connections>
          <named-connection caption='SPARITY-SRIKRIS\SQL_SERVER_1' name='sqlserver.0k11eaw1owt4cc1b7523912w0z0e'>
            <connection authentication='sspi' class='sqlserver' dbname='AdventureWorksDW2022' minimum-driver-version='SQL Server Native Client 10.0' odbc-native-protocol='yes' one-time-sql='' server='localhost' />
          </named-connection>
        </named-connections>
        <_.fcp.ObjectModelEncapsulateLegacy.false...relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
        <_.fcp.ObjectModelEncapsulateLegacy.true...relation type='collection'>
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
          <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
        </_.fcp.ObjectModelEncapsulateLegacy.true...relation>
        <cols>
          <map key='[ArabicDescription]' value='[DimProduct].[ArabicDescription]' />
          <map key='[CalendarQuarter]' value='[DimDate].[CalendarQuarter]' />
          <map key='[CalendarSemester]' value='[DimDate].[CalendarSemester]' />
          <map key='[CalendarYear]' value='[DimDate].[CalendarYear]' />
          <map key='[CarrierTrackingNumber]' value='[FactInternetSales].[CarrierTrackingNumber]' />
          <map key='[ChineseDescription]' value='[DimProduct].[ChineseDescription]' />
          <map key='[Class]' value='[DimProduct].[Class]' />
          <map key='[Color]' value='[DimProduct].[Color]' />
          <map key='[CurrencyKey]' value='[FactInternetSales].[CurrencyKey]' />
          <map key='[CustomerKey]' value='[FactInternetSales].[CustomerKey]' />
          <map key='[CustomerPONumber]' value='[FactInternetSales].[CustomerPONumber]' />
          <map key='[DateKey]' value='[DimDate].[DateKey]' />
          <map key='[DayNumberOfMonth]' value='[DimDate].[DayNumberOfMonth]' />
          <map key='[DayNumberOfWeek]' value='[DimDate].[DayNumberOfWeek]' />
          <map key='[DayNumberOfYear]' value='[DimDate].[DayNumberOfYear]' />
          <map key='[DaysToManufacture]' value='[DimProduct].[DaysToManufacture]' />
          <map key='[DealerPrice]' value='[DimProduct].[DealerPrice]' />
          <map key='[DiscountAmount]' value='[FactInternetSales].[DiscountAmount]' />
          <map key='[DueDateKey]' value='[FactInternetSales].[DueDateKey]' />
          <map key='[DueDate]' value='[FactInternetSales].[DueDate]' />
          <map key='[EndDate]' value='[DimProduct].[EndDate]' />
          <map key='[EnglishDayNameOfWeek]' value='[DimDate].[EnglishDayNameOfWeek]' />
          <map key='[EnglishDescription]' value='[DimProduct].[EnglishDescription]' />
          <map key='[EnglishMonthName]' value='[DimDate].[EnglishMonthName]' />
          <map key='[EnglishProductCategoryName]' value='[DimProductCategory].[EnglishProductCategoryName]' />
          <map key='[EnglishProductName]' value='[DimProduct].[EnglishProductName]' />
          <map key='[EnglishProductSubcategoryName]' value='[DimProductSubcategory].[EnglishProductSubcategoryName]' />
          <map key='[ExtendedAmount]' value='[FactInternetSales].[ExtendedAmount]' />
          <map key='[FinishedGoodsFlag]' value='[DimProduct].[FinishedGoodsFlag]' />
          <map key='[FiscalQuarter]' value='[DimDate].[FiscalQuarter]' />
          <map key='[FiscalSemester]' value='[DimDate].[FiscalSemester]' />
          <map key='[FiscalYear]' value='[DimDate].[FiscalYear]' />
          <map key='[Freight]' value='[FactInternetSales].[Freight]' />
          <map key='[FrenchDayNameOfWeek]' value='[DimDate].[FrenchDayNameOfWeek]' />
          <map key='[FrenchDescription]' value='[DimProduct].[FrenchDescription]' />
          <map key='[FrenchMonthName]' value='[DimDate].[FrenchMonthName]' />
          <map key='[FrenchProductCategoryName]' value='[DimProductCategory].[FrenchProductCategoryName]' />
          <map key='[FrenchProductName]' value='[DimProduct].[FrenchProductName]' />
          <map key='[FrenchProductSubcategoryName]' value='[DimProductSubcategory].[FrenchProductSubcategoryName]' />
          <map key='[FullDateAlternateKey]' value='[DimDate].[FullDateAlternateKey]' />
          <map key='[GermanDescription]' value='[DimProduct].[GermanDescription]' />
          <map key='[HebrewDescription]' value='[DimProduct].[HebrewDescription]' />
          <map key='[JapaneseDescription]' value='[DimProduct].[JapaneseDescription]' />
          <map key='[LargePhoto]' value='[DimProduct].[LargePhoto]' />
          <map key='[ListPrice]' value='[DimProduct].[ListPrice]' />
          <map key='[ModelName]' value='[DimProduct].[ModelName]' />
          <map key='[MonthNumberOfYear]' value='[DimDate].[MonthNumberOfYear]' />
          <map key='[OrderDateKey]' value='[FactInternetSales].[OrderDateKey]' />
          <map key='[OrderDate]' value='[FactInternetSales].[OrderDate]' />
          <map key='[OrderQuantity]' value='[FactInternetSales].[OrderQuantity]' />
          <map key='[ProductAlternateKey]' value='[DimProduct].[ProductAlternateKey]' />
          <map key='[ProductCategoryAlternateKey]' value='[DimProductCategory].[ProductCategoryAlternateKey]' />
          <map key='[ProductCategoryKey (DimProductSubcategory)]' value='[DimProductSubcategory].[ProductCategoryKey]' />
          <map key='[ProductCategoryKey]' value='[DimProductCategory].[ProductCategoryKey]' />
          <map key='[ProductKey (DimProduct)]' value='[DimProduct].[ProductKey]' />
          <map key='[ProductKey]' value='[FactInternetSales].[ProductKey]' />
          <map key='[ProductLine]' value='[DimProduct].[ProductLine]' />
          <map key='[ProductStandardCost]' value='[FactInternetSales].[ProductStandardCost]' />
          <map key='[ProductSubcategoryAlternateKey]' value='[DimProductSubcategory].[ProductSubcategoryAlternateKey]' />
          <map key='[ProductSubcategoryKey (DimProduct)]' value='[DimProduct].[ProductSubcategoryKey]' />
          <map key='[ProductSubcategoryKey]' value='[DimProductSubcategory].[ProductSubcategoryKey]' />
          <map key='[PromotionKey]' value='[FactInternetSales].[PromotionKey]' />
          <map key='[ReorderPoint]' value='[DimProduct].[ReorderPoint]' />
          <map key='[RevisionNumber]' value='[FactInternetSales].[RevisionNumber]' />
          <map key='[SafetyStockLevel]' value='[DimProduct].[SafetyStockLevel]' />
          <map key='[SalesAmount]' value='[FactInternetSales].[SalesAmount]' />
          <map key='[SalesOrderLineNumber]' value='[FactInternetSales].[SalesOrderLineNumber]' />
          <map key='[SalesOrderNumber]' value='[FactInternetSales].[SalesOrderNumber]' />
          <map key='[SalesTerritoryAlternateKey]' value='[DimSalesTerritory].[SalesTerritoryAlternateKey]' />
          <map key='[SalesTerritoryCountry]' value='[DimSalesTerritory].[SalesTerritoryCountry]' />
          <map key='[SalesTerritoryGroup]' value='[DimSalesTerritory].[SalesTerritoryGroup]' />
          <map key='[SalesTerritoryImage]' value='[DimSalesTerritory].[SalesTerritoryImage]' />
          <map key='[SalesTerritoryKey (DimSalesTerritory)]' value='[DimSalesTerritory].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryKey]' value='[FactInternetSales].[SalesTerritoryKey]' />
          <map key='[SalesTerritoryRegion]' value='[DimSalesTerritory].[SalesTerritoryRegion]' />
          <map key='[ShipDateKey]' value='[FactInternetSales].[ShipDateKey]' />
          <map key='[ShipDate]' value='[FactInternetSales].[ShipDate]' />
          <map key='[SizeRange]' value='[DimProduct].[SizeRange]' />
          <map key='[SizeUnitMeasureCode]' value='[DimProduct].[SizeUnitMeasureCode]' />
          <map key='[Size]' value='[DimProduct].[Size]' />
          <map key='[SpanishDayNameOfWeek]' value='[DimDate].[SpanishDayNameOfWeek]' />
          <map key='[SpanishMonthName]' value='[DimDate].[SpanishMonthName]' />
          <map key='[SpanishProductCategoryName]' value='[DimProductCategory].[SpanishProductCategoryName]' />
          <map key='[SpanishProductName]' value='[DimProduct].[SpanishProductName]' />
          <map key='[SpanishProductSubcategoryName]' value='[DimProductSubcategory].[SpanishProductSubcategoryName]' />
          <map key='[StandardCost]' value='[DimProduct].[StandardCost]' />
          <map key='[StartDate]' value='[DimProduct].[StartDate]' />
          <map key='[Status]' value='[DimProduct].[Status]' />
          <map key='[Style]' value='[DimProduct].[Style]' />
          <map key='[TaxAmt]' value='[FactInternetSales].[TaxAmt]' />
          <map key='[ThaiDescription]' value='[DimProduct].[ThaiDescription]' />
          <map key='[TotalProductCost]' value='[FactInternetSales].[TotalProductCost]' />
          <map key='[TurkishDescription]' value='[DimProduct].[TurkishDescription]' />
          <map key='[UnitPriceDiscountPct]' value='[FactInternetSales].[UnitPriceDiscountPct]' />
          <map key='[UnitPrice]' value='[FactInternetSales].[UnitPrice]' />
          <map key='[WeekNumberOfYear]' value='[DimDate].[WeekNumberOfYear]' />
          <map key='[WeightUnitMeasureCode]' value='[DimProduct].[WeightUnitMeasureCode]' />
          <map key='[Weight]' value='[DimProduct].[Weight]' />
        </cols>
        <metadata-records>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>1</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryAlternateKey]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>ProductCategoryAlternateKey</remote-alias>
            <ordinal>2</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>EnglishProductCategoryName</remote-alias>
            <ordinal>3</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>SpanishProductCategoryName</remote-alias>
            <ordinal>4</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductCategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductCategoryName]</local-name>
            <parent-name>[DimProductCategory]</parent-name>
            <remote-alias>FrenchProductCategoryName</remote-alias>
            <ordinal>5</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>7</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryAlternateKey]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductSubcategoryAlternateKey</remote-alias>
            <ordinal>8</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>EnglishProductSubcategoryName</remote-alias>
            <ordinal>9</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>SpanishProductSubcategoryName</remote-alias>
            <ordinal>10</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductSubcategoryName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductSubcategoryName]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>FrenchProductSubcategoryName</remote-alias>
            <ordinal>11</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductCategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductCategoryKey (DimProductSubcategory)]</local-name>
            <parent-name>[DimProductSubcategory]</parent-name>
            <remote-alias>ProductCategoryKey</remote-alias>
            <ordinal>12</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>14</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductAlternateKey</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductAlternateKey]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductAlternateKey</remote-alias>
            <ordinal>15</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductSubcategoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductSubcategoryKey (DimProduct)]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductSubcategoryKey</remote-alias>
            <ordinal>16</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeightUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[WeightUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>WeightUnitMeasureCode</remote-alias>
            <ordinal>17</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeUnitMeasureCode</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeUnitMeasureCode]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeUnitMeasureCode</remote-alias>
            <ordinal>18</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>3</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishProductName</remote-alias>
            <ordinal>19</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SpanishProductName</remote-alias>
            <ordinal>20</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchProductName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchProductName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchProductName</remote-alias>
            <ordinal>21</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[StandardCost]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StandardCost</remote-alias>
            <ordinal>22</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FinishedGoodsFlag</remote-name>
            <remote-type>11</remote-type>
            <local-name>[FinishedGoodsFlag]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FinishedGoodsFlag</remote-alias>
            <ordinal>23</ordinal>
            <local-type>boolean</local-type>
            <aggregation>Count</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_BIT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BIT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Color</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Color]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Color</remote-alias>
            <ordinal>24</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>15</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SafetyStockLevel</remote-name>
            <remote-type>2</remote-type>
            <local-name>[SafetyStockLevel]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SafetyStockLevel</remote-alias>
            <ordinal>25</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ReorderPoint</remote-name>
            <remote-type>2</remote-type>
            <local-name>[ReorderPoint]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ReorderPoint</remote-alias>
            <ordinal>26</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ListPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ListPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ListPrice</remote-alias>
            <ordinal>27</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Size</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Size]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Size</remote-alias>
            <ordinal>28</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SizeRange</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SizeRange]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>SizeRange</remote-alias>
            <ordinal>29</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Weight</remote-name>
            <remote-type>5</remote-type>
            <local-name>[Weight]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Weight</remote-alias>
            <ordinal>30</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DaysToManufacture</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DaysToManufacture]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DaysToManufacture</remote-alias>
            <ordinal>31</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductLine</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ProductLine]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ProductLine</remote-alias>
            <ordinal>32</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DealerPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[DealerPrice]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>DealerPrice</remote-alias>
            <ordinal>33</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Class</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Class]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Class</remote-alias>
            <ordinal>34</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Style</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Style]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Style</remote-alias>
            <ordinal>35</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>2</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ModelName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ModelName]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ModelName</remote-alias>
            <ordinal>36</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>LargePhoto</remote-name>
            <remote-type>128</remote-type>
            <local-name>[LargePhoto]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>LargePhoto</remote-alias>
            <ordinal>37</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EnglishDescription</remote-alias>
            <ordinal>38</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>FrenchDescription</remote-alias>
            <ordinal>39</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ChineseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ChineseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ChineseDescription</remote-alias>
            <ordinal>40</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ArabicDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ArabicDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ArabicDescription</remote-alias>
            <ordinal>41</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>HebrewDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[HebrewDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>HebrewDescription</remote-alias>
            <ordinal>42</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ThaiDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[ThaiDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>ThaiDescription</remote-alias>
            <ordinal>43</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>GermanDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[GermanDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>GermanDescription</remote-alias>
            <ordinal>44</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>JapaneseDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[JapaneseDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>JapaneseDescription</remote-alias>
            <ordinal>45</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TurkishDescription</remote-name>
            <remote-type>130</remote-type>
            <local-name>[TurkishDescription]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>TurkishDescription</remote-alias>
            <ordinal>46</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>400</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>StartDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[StartDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>StartDate</remote-alias>
            <ordinal>47</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EndDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[EndDate]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>EndDate</remote-alias>
            <ordinal>48</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Status</remote-name>
            <remote-type>130</remote-type>
            <local-name>[Status]</local-name>
            <parent-name>[DimProduct]</parent-name>
            <remote-alias>Status</remote-alias>
            <ordinal>49</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>7</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ProductKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductKey</remote-alias>
            <ordinal>51</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[OrderDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDateKey</remote-alias>
            <ordinal>52</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DueDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDateKey</remote-alias>
            <ordinal>53</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[ShipDateKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDateKey</remote-alias>
            <ordinal>54</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CustomerKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerKey</remote-alias>
            <ordinal>55</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>PromotionKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[PromotionKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>PromotionKey</remote-alias>
            <ordinal>56</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CurrencyKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[CurrencyKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CurrencyKey</remote-alias>
            <ordinal>57</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>58</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesOrderNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderNumber</remote-alias>
            <ordinal>59</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>20</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesOrderLineNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[SalesOrderLineNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesOrderLineNumber</remote-alias>
            <ordinal>60</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>RevisionNumber</remote-name>
            <remote-type>17</remote-type>
            <local-name>[RevisionNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>RevisionNumber</remote-alias>
            <ordinal>61</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderQuantity</remote-name>
            <remote-type>2</remote-type>
            <local-name>[OrderQuantity]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderQuantity</remote-alias>
            <ordinal>62</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPrice</remote-name>
            <remote-type>131</remote-type>
            <local-name>[UnitPrice]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPrice</remote-alias>
            <ordinal>63</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ExtendedAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ExtendedAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ExtendedAmount</remote-alias>
            <ordinal>64</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>UnitPriceDiscountPct</remote-name>
            <remote-type>5</remote-type>
            <local-name>[UnitPriceDiscountPct]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>UnitPriceDiscountPct</remote-alias>
            <ordinal>65</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DiscountAmount</remote-name>
            <remote-type>5</remote-type>
            <local-name>[DiscountAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DiscountAmount</remote-alias>
            <ordinal>66</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>15</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_FLOAT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_DOUBLE&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ProductStandardCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[ProductStandardCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ProductStandardCost</remote-alias>
            <ordinal>67</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TotalProductCost</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TotalProductCost]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TotalProductCost</remote-alias>
            <ordinal>68</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesAmount</remote-name>
            <remote-type>131</remote-type>
            <local-name>[SalesAmount]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>SalesAmount</remote-alias>
            <ordinal>69</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>TaxAmt</remote-name>
            <remote-type>131</remote-type>
            <local-name>[TaxAmt]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>TaxAmt</remote-alias>
            <ordinal>70</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>Freight</remote-name>
            <remote-type>131</remote-type>
            <local-name>[Freight]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>Freight</remote-alias>
            <ordinal>71</ordinal>
            <local-type>real</local-type>
            <aggregation>Sum</aggregation>
            <precision>19</precision>
            <scale>4</scale>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_DECIMAL&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_NUMERIC&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CarrierTrackingNumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CarrierTrackingNumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CarrierTrackingNumber</remote-alias>
            <ordinal>72</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CustomerPONumber</remote-name>
            <remote-type>130</remote-type>
            <local-name>[CustomerPONumber]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>CustomerPONumber</remote-alias>
            <ordinal>73</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>25</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>OrderDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[OrderDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>OrderDate</remote-alias>
            <ordinal>74</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DueDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[DueDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>DueDate</remote-alias>
            <ordinal>75</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>ShipDate</remote-name>
            <remote-type>7</remote-type>
            <local-name>[ShipDate]</local-name>
            <parent-name>[FactInternetSales]</parent-name>
            <remote-alias>ShipDate</remote-alias>
            <ordinal>76</ordinal>
            <local-type>datetime</local-type>
            <aggregation>Year</aggregation>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_TIMESTAMP&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_TIMESTAMP&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[DateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DateKey</remote-alias>
            <ordinal>78</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FullDateAlternateKey</remote-name>
            <remote-type>7</remote-type>
            <local-name>[FullDateAlternateKey]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FullDateAlternateKey</remote-alias>
            <ordinal>79</ordinal>
            <local-type>date</local-type>
            <aggregation>Year</aggregation>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TYPE_DATE&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_TYPE_DATE&quot;</attribute>
              <attribute datatype='boolean' name='TypeIsDateTime2orDate'>true</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfWeek</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfWeek</remote-alias>
            <ordinal>80</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishDayNameOfWeek</remote-alias>
            <ordinal>81</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishDayNameOfWeek</remote-alias>
            <ordinal>82</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchDayNameOfWeek</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchDayNameOfWeek]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchDayNameOfWeek</remote-alias>
            <ordinal>83</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfMonth</remote-name>
            <remote-type>17</remote-type>
            <local-name>[DayNumberOfMonth]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfMonth</remote-alias>
            <ordinal>84</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>DayNumberOfYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[DayNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>DayNumberOfYear</remote-alias>
            <ordinal>85</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>WeekNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[WeekNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>WeekNumberOfYear</remote-alias>
            <ordinal>86</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>EnglishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[EnglishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>EnglishMonthName</remote-alias>
            <ordinal>87</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SpanishMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SpanishMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>SpanishMonthName</remote-alias>
            <ordinal>88</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FrenchMonthName</remote-name>
            <remote-type>130</remote-type>
            <local-name>[FrenchMonthName]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FrenchMonthName</remote-alias>
            <ordinal>89</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>10</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>MonthNumberOfYear</remote-name>
            <remote-type>17</remote-type>
            <local-name>[MonthNumberOfYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>MonthNumberOfYear</remote-alias>
            <ordinal>90</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarQuarter</remote-alias>
            <ordinal>91</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[CalendarYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarYear</remote-alias>
            <ordinal>92</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>CalendarSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[CalendarSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>CalendarSemester</remote-alias>
            <ordinal>93</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalQuarter</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalQuarter]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalQuarter</remote-alias>
            <ordinal>94</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalYear</remote-name>
            <remote-type>2</remote-type>
            <local-name>[FiscalYear]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalYear</remote-alias>
            <ordinal>95</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>5</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_SMALLINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SSHORT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>FiscalSemester</remote-name>
            <remote-type>17</remote-type>
            <local-name>[FiscalSemester]</local-name>
            <parent-name>[DimDate]</parent-name>
            <remote-alias>FiscalSemester</remote-alias>
            <ordinal>96</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>3</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_TINYINT&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_UTINYINT&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryKey (DimSalesTerritory)]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryKey</remote-alias>
            <ordinal>98</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>false</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryAlternateKey</remote-name>
            <remote-type>3</remote-type>
            <local-name>[SalesTerritoryAlternateKey]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryAlternateKey</remote-alias>
            <ordinal>99</ordinal>
            <local-type>integer</local-type>
            <aggregation>Sum</aggregation>
            <precision>10</precision>
            <contains-null>true</contains-null>
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_INTEGER&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_SLONG&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryRegion</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryRegion]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryRegion</remote-alias>
            <ordinal>100</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryCountry</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryCountry]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryCountry</remote-alias>
            <ordinal>101</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>false</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryGroup</remote-name>
            <remote-type>130</remote-type>
            <local-name>[SalesTerritoryGroup]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryGroup</remote-alias>
            <ordinal>102</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <width>50</width>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='2147483649' name='LEN_RUS_S2_VWIN' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_WVARCHAR&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_WCHAR&quot;</attribute>
              <attribute datatype='string' name='TypeIsVarchar'>&quot;true&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
          <metadata-record class='column'>
            <remote-name>SalesTerritoryImage</remote-name>
            <remote-type>128</remote-type>
            <local-name>[SalesTerritoryImage]</local-name>
            <parent-name>[DimSalesTerritory]</parent-name>
            <remote-alias>SalesTerritoryImage</remote-alias>
            <ordinal>103</ordinal>
            <local-type>string</local-type>
            <aggregation>Count</aggregation>
            <contains-null>true</contains-null>
            <padded-semantics>true</padded-semantics>
            <collation flag='0' name='LROOT' />
            <attributes>
              <attribute datatype='string' name='DebugRemoteType'>&quot;SQL_VARBINARY&quot;</attribute>
              <attribute datatype='string' name='DebugWireType'>&quot;SQL_C_BINARY&quot;</attribute>
            </attributes>
            <_.fcp.ObjectModelEncapsulateLegacy.true...object-id>[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]</_.fcp.ObjectModelEncapsulateLegacy.true...object-id>
          </metadata-record>
        </metadata-records>
      </connection>
      <aliases enabled='yes' />
      <column caption='Arabic Description' datatype='string' name='[ArabicDescription]' role='dimension' type='nominal' />
      <column caption='CLEAR FILTERS' datatype='string' name='[Calculation_295830217743659008]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Clear All&apos;' />
      </column>
      <column aggregation='Sum' caption='Quarter' datatype='integer' name='[Calculation_447545216406446083]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='QUARTER([OrderDate])' />
      </column>
      <column aggregation='Sum' caption='Month' datatype='integer' name='[Calculation_447545216414334984]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='MONTH([OrderDate])' />
      </column>
      <column caption='MonthName' datatype='string' name='[Calculation_447545216416518153]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='MonthShortName' datatype='string' name='[Calculation_447545216416817162]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='LEFT(DATENAME(&apos;month&apos;,[OrderDate]),3)' />
      </column>
      <column aggregation='Sum' caption='Weekday' datatype='integer' name='[Calculation_447545216417103883]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekdayName' datatype='string' name='[Calculation_447545216417656844]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;weekday&apos;,[OrderDate])' />
      </column>
      <column caption='WeekEnd/WeekDay' datatype='string' name='[Calculation_447545216417820685]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='IF [Calculation_447545216417103883]=1 or [Calculation_447545216417103883]=7 THEN &quot;WeekEnd&quot; &#13;&#10;ELSE &quot;WeekDay&quot;&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='FinacialMonth' datatype='integer' name='[Calculation_447545216418512910]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='IF MONTH([OrderDate])-3&gt;0&#13;&#10;THEN MONTH([OrderDate])-3&#13;&#10;ELSE MONTH([OrderDate])+12-3&#13;&#10;END' />
      </column>
      <column aggregation='Sum' caption='Finacial Quarter' datatype='integer' name='[Calculation_447545216418717711]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATEPART(&apos;quarter&apos;,DATEADD(&apos;month&apos;,-3,[OrderDate]))' />
      </column>
      <column caption='YearMonth' datatype='string' name='[Calculation_447545216419405840]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='DATENAME(&apos;year&apos;,[OrderDate])+&quot;-&quot;+DATENAME(&apos;month&apos;,[OrderDate])' />
      </column>
      <column caption='Month,Day,Year' datatype='integer' name='[Calculation_447545216421498898]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='(DATEPART(&apos;year&apos;, [OrderDate])*10000 + DATEPART(&apos;month&apos;, [OrderDate])*100 + DATEPART(&apos;day&apos;, [OrderDate]))' />
      </column>
      <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
      </column>
      <column caption='DM' datatype='real' name='[Calculation_447545216423968789]' role='measure' type='quantitative'>
        <calculation class='tableau' formula='IF [Parameters].[Parameter 2]==&quot;PROFIT&quot; THEN [Calculation_447545216423890964]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;SALES&quot; THEN [SalesAmount]&#13;&#10;ELSEIF [Parameters].[Parameter 2]==&quot;COST&quot; THEN [TotalProductCost]&#13;&#10;ELSE 0&#13;&#10;END' />
      </column>
      <column caption='OrderDate' datatype='date' name='[Calculation_474848296898699264]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='DATE(LEFT(STR([OrderDateKey]),4)+&quot;-&quot;+MID(STR([OrderDateKey]),5,2)+&quot;-&quot;+RIGHT(STR([OrderDateKey]),2))' />
      </column>
      <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
        <calculation class='tableau' formula='YEAR([OrderDate])' />
      </column>
      <column caption='Reset Filters' datatype='string' name='[Calculation_778278323468275715]' role='dimension' type='nominal'>
        <calculation class='tableau' formula='&apos;Reset Filters&apos;' />
      </column>
      <column caption='Calendar Quarter' datatype='integer' name='[CalendarQuarter]' role='dimension' type='quantitative' />
      <column caption='Calendar Semester' datatype='integer' name='[CalendarSemester]' role='measure' type='quantitative' />
      <column caption='Calendar Year' datatype='integer' name='[CalendarYear]' role='dimension' type='quantitative' />
      <column caption='Carrier Tracking Number' datatype='string' name='[CarrierTrackingNumber]' role='dimension' type='nominal' />
      <column caption='Chinese Description' datatype='string' name='[ChineseDescription]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Currency Key' datatype='integer' name='[CurrencyKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Customer Key' datatype='integer' name='[CustomerKey]' role='dimension' type='ordinal' />
      <column caption='Customer PO Number' datatype='string' name='[CustomerPONumber]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Date Key' datatype='integer' name='[DateKey]' role='dimension' type='ordinal' />
      <column caption='Day Number Of Month' datatype='integer' name='[DayNumberOfMonth]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Week' datatype='integer' name='[DayNumberOfWeek]' role='dimension' type='quantitative' />
      <column caption='Day Number Of Year' datatype='integer' name='[DayNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Days To Manufacture' datatype='integer' name='[DaysToManufacture]' role='measure' type='quantitative' />
      <column caption='Dealer Price' datatype='real' name='[DealerPrice]' role='measure' type='quantitative' />
      <column caption='Discount Amount' datatype='real' name='[DiscountAmount]' role='measure' type='quantitative' />
      <column aggregation='Sum' caption='Due Date Key' datatype='integer' name='[DueDateKey]' role='dimension' type='ordinal' />
      <column caption='Due Date' datatype='datetime' name='[DueDate]' role='dimension' type='ordinal' />
      <column caption='End Date' datatype='datetime' name='[EndDate]' role='dimension' type='ordinal' />
      <column caption='English Day Name Of Week' datatype='string' name='[EnglishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='English Description' datatype='string' name='[EnglishDescription]' role='dimension' type='nominal' />
      <column caption='English Month Name' datatype='string' name='[EnglishMonthName]' role='dimension' type='nominal' />
      <column caption='English Product Category Name' datatype='string' name='[EnglishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='English Product Name' datatype='string' name='[EnglishProductName]' role='dimension' type='nominal' />
      <column caption='English Product Subcategory Name' datatype='string' name='[EnglishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Extended Amount' datatype='real' name='[ExtendedAmount]' role='measure' type='quantitative' />
      <column caption='Finished Goods Flag' datatype='boolean' name='[FinishedGoodsFlag]' role='dimension' type='nominal' />
      <column caption='Fiscal Quarter' datatype='integer' name='[FiscalQuarter]' role='dimension' type='quantitative' />
      <column caption='Fiscal Semester' datatype='integer' name='[FiscalSemester]' role='measure' type='quantitative' />
      <column caption='Fiscal Year' datatype='integer' name='[FiscalYear]' role='dimension' type='quantitative' />
      <column caption='French Day Name Of Week' datatype='string' name='[FrenchDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='French Description' datatype='string' name='[FrenchDescription]' role='dimension' type='nominal' />
      <column caption='French Month Name' datatype='string' name='[FrenchMonthName]' role='dimension' type='nominal' />
      <column caption='French Product Category Name' datatype='string' name='[FrenchProductCategoryName]' role='dimension' type='nominal' />
      <column caption='French Product Name' datatype='string' name='[FrenchProductName]' role='dimension' type='nominal' />
      <column caption='French Product Subcategory Name' datatype='string' name='[FrenchProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Full Date Alternate Key' datatype='date' name='[FullDateAlternateKey]' role='dimension' type='ordinal' />
      <column caption='German Description' datatype='string' name='[GermanDescription]' role='dimension' type='nominal' />
      <column caption='Hebrew Description' datatype='string' name='[HebrewDescription]' role='dimension' type='nominal' />
      <column caption='Japanese Description' datatype='string' name='[JapaneseDescription]' role='dimension' type='nominal' />
      <column caption='Large Photo' datatype='string' name='[LargePhoto]' role='dimension' type='nominal' />
      <column caption='List Price' datatype='real' name='[ListPrice]' role='measure' type='quantitative' />
      <column caption='Model Name' datatype='string' name='[ModelName]' role='dimension' type='nominal' />
      <column caption='Month Number Of Year' datatype='integer' name='[MonthNumberOfYear]' role='dimension' type='quantitative' />
      <column aggregation='Sum' caption='Order Date Key' datatype='integer' name='[OrderDateKey]' role='dimension' type='ordinal' />
      <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
      <column caption='Order Quantity' datatype='integer' name='[OrderQuantity]' role='measure' type='quantitative' />
      <column caption='Product Alternate Key' datatype='string' name='[ProductAlternateKey]' role='dimension' type='nominal' />
      <column caption='Product Category Alternate Key' datatype='integer' name='[ProductCategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductCategoryKey (DimProductSubcategory)]' role='dimension' type='ordinal' />
      <column caption='Product Category Key' datatype='integer' name='[ProductCategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductKey (DimProduct)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Product Key' datatype='integer' name='[ProductKey]' role='dimension' type='ordinal' />
      <column caption='Product Line' datatype='string' name='[ProductLine]' role='dimension' type='nominal' />
      <column caption='Product Standard Cost' datatype='real' name='[ProductStandardCost]' role='measure' type='quantitative' />
      <column caption='Product Subcategory Alternate Key' datatype='integer' name='[ProductSubcategoryAlternateKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' datatype='integer' name='[ProductSubcategoryKey (DimProduct)]' role='dimension' type='ordinal' />
      <column caption='Product Subcategory Key' datatype='integer' name='[ProductSubcategoryKey]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Promotion Key' datatype='integer' name='[PromotionKey]' role='dimension' type='ordinal' />
      <column caption='Reorder Point' datatype='integer' name='[ReorderPoint]' role='measure' type='quantitative' />
      <column caption='Revision Number' datatype='integer' name='[RevisionNumber]' role='dimension' type='ordinal' />
      <column caption='Safety Stock Level' datatype='integer' name='[SafetyStockLevel]' role='measure' type='quantitative' />
      <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
      <column caption='Sales Order Line Number' datatype='integer' name='[SalesOrderLineNumber]' role='dimension' type='ordinal' />
      <column caption='Sales Order Number' datatype='string' name='[SalesOrderNumber]' role='dimension' type='nominal' />
      <column caption='Sales Territory Alternate Key' datatype='integer' name='[SalesTerritoryAlternateKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Country' datatype='string' name='[SalesTerritoryCountry]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
      <column caption='Sales Territory Group' datatype='string' name='[SalesTerritoryGroup]' role='dimension' type='nominal' />
      <column caption='Sales Territory Image' datatype='string' name='[SalesTerritoryImage]' role='dimension' type='nominal' />
      <column aggregation='Sum' datatype='integer' name='[SalesTerritoryKey (DimSalesTerritory)]' role='dimension' type='ordinal' />
      <column aggregation='Sum' caption='Sales Territory Key' datatype='integer' name='[SalesTerritoryKey]' role='dimension' type='ordinal' />
      <column caption='Sales Territory Region' datatype='string' name='[SalesTerritoryRegion]' role='dimension' type='nominal' />
      <column aggregation='Sum' caption='Ship Date Key' datatype='integer' name='[ShipDateKey]' role='dimension' type='ordinal' />
      <column caption='Ship Date' datatype='datetime' name='[ShipDate]' role='dimension' type='ordinal' />
      <column caption='Size Range' datatype='string' name='[SizeRange]' role='dimension' type='nominal' />
      <column caption='Size Unit Measure Code' datatype='string' name='[SizeUnitMeasureCode]' role='dimension' type='nominal' />
      <column caption='Spanish Day Name Of Week' datatype='string' name='[SpanishDayNameOfWeek]' role='dimension' type='nominal' />
      <column caption='Spanish Month Name' datatype='string' name='[SpanishMonthName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Category Name' datatype='string' name='[SpanishProductCategoryName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Name' datatype='string' name='[SpanishProductName]' role='dimension' type='nominal' />
      <column caption='Spanish Product Subcategory Name' datatype='string' name='[SpanishProductSubcategoryName]' role='dimension' type='nominal' />
      <column caption='Standard Cost' datatype='real' name='[StandardCost]' role='measure' type='quantitative' />
      <column caption='Start Date' datatype='datetime' name='[StartDate]' role='dimension' type='ordinal' />
      <column caption='Tax Amt' datatype='real' name='[TaxAmt]' role='measure' type='quantitative' />
      <column caption='Thai Description' datatype='string' name='[ThaiDescription]' role='dimension' type='nominal' />
      <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
      <column caption='Turkish Description' datatype='string' name='[TurkishDescription]' role='dimension' type='nominal' />
      <column caption='Unit Price Discount Pct' datatype='real' name='[UnitPriceDiscountPct]' role='measure' type='quantitative' />
      <column caption='Unit Price' datatype='real' name='[UnitPrice]' role='measure' type='quantitative' />
      <column caption='Week Number Of Year' datatype='integer' name='[WeekNumberOfYear]' role='dimension' type='quantitative' />
      <column caption='Weight Unit Measure Code' datatype='string' name='[WeightUnitMeasureCode]' role='dimension' type='nominal' />
      <_.fcp.ObjectModelTableType.true...column caption='DimDate' datatype='table' name='[__tableau_internal_object_id__].[DimDate_9EC66DEF2B65424CB216051BCC33CAC1]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProductCategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProductSubcategory' datatype='table' name='[__tableau_internal_object_id__].[DimProductSubcategory_AD2268E558794BFCA99892F6E071319B]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimProduct' datatype='table' name='[__tableau_internal_object_id__].[DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='DimSalesTerritory' datatype='table' name='[__tableau_internal_object_id__].[DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949]' role='measure' type='quantitative' />
      <_.fcp.ObjectModelTableType.true...column caption='Sales' datatype='table' name='[__tableau_internal_object_id__].[FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321]' role='measure' type='quantitative' />
      <column-instance column='[SalesTerritoryCountry]' derivation='Attribute' name='[attr:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[diff:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc diff-options='Relative' ordering-type='Rows' type='Difference'>
          <address>
            <value>-1</value>
          </address>
        </table-calc>
      </column-instance>
      <column-instance column='[SalesTerritoryCountry]' derivation='Max' name='[max:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[Calculation_447545216406446083]' derivation='None' name='[none:Calculation_447545216406446083:ok]' pivot='key' type='ordinal' />
      <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
      <column-instance column='[EnglishProductCategoryName]' derivation='None' name='[none:EnglishProductCategoryName:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesTerritoryCountry]' derivation='None' name='[none:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:cum:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc aggregation='Sum' ordering-type='Rows' type='CumTotal' />
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[pcto:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' type='PctTotal' />
      </column-instance>
      <column-instance column='[Calculation_474848296898699264]' derivation='Quarter' name='[qr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:2]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[EnglishMonthName]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:3]' pivot='key' type='quantitative'>
        <table-calc ordering-field='[federated.1nl426t13auwkc10rmst40m1iuwe].[SalesTerritoryCountry]' ordering-type='Field' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk:4]' pivot='key' type='quantitative'>
        <table-calc ordering-type='CellInPane' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[rank:sum:SalesAmount:qk]' pivot='key' type='quantitative'>
        <table-calc ordering-type='Rows' rank-options='Competition,Descending' type='Rank' />
      </column-instance>
      <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
      <column-instance column='[TotalProductCost]' derivation='Sum' name='[sum:TotalProductCost:qk]' pivot='key' type='quantitative' />
      <column-instance column='[Calculation_474848296898699264]' derivation='Year-Trunc' name='[tyr:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
      <group caption='Action (CLEAR FILTERS)' hidden='true' name='[Action (CLEAR FILTERS)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_295830217743659008]' />
        </groupfilter>
      </group>
      <group caption='Action (Clear All)' hidden='true' name='[Action (Clear All)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323462979586]' />
        </groupfilter>
      </group>
      <group caption='Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))' hidden='true' name='[Action (MonthName,Sales Territory Country,QUARTER(OrderDate),YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
          <groupfilter function='level-members' level='[qr:Calculation_474848296898699264:ok]' />
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset Filters)' hidden='true' name='[Action (Reset Filters)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323468275715]' />
        </groupfilter>
      </group>
      <group caption='Action (Reset)' hidden='true' name='[Action (Reset)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_778278323455827969]' />
        </groupfilter>
      </group>
      <group caption='Action (Sales Territory Country)' hidden='true' name='[Action (Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (YEAR(OrderDate))' hidden='true' name='[Action (YEAR(OrderDate))]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[tyr:Calculation_474848296898699264:ok]' />
        </groupfilter>
      </group>
      <group caption='Action (Year)' hidden='true' name='[Action (Year)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Month,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,Month,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115184209922]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,MonthName,Quarter,Sales Territory Country)' hidden='true' name='[Action (Year,MonthName,Quarter,Sales Territory Country)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115185774595]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
          <groupfilter function='level-members' level='[SalesTerritoryCountry]' />
        </groupfilter>
      </group>
      <group caption='Action (Year,Quarter)' hidden='true' name='[Action (Year,Quarter)]' name-style='unqualified' user:auto-column='sheet_link'>
        <groupfilter function='crossjoin'>
          <groupfilter function='level-members' level='[Calculation_561261115181088768]' />
          <groupfilter function='level-members' level='[Calculation_561261115188973574]' />
        </groupfilter>
      </group>
      <drill-paths>
        <drill-path name='Sales Territory Region, Sales Territory Country'>
          <field>[SalesTerritoryRegion]</field>
        </drill-path>
      </drill-paths>
      <layout _.fcp.SchemaViewerObjectModel.false...dim-percentage='0.5' _.fcp.SchemaViewerObjectModel.false...measure-percentage='0.4' dim-ordering='alphabetic' measure-ordering='alphabetic' rowDisplayCount='300' show-aliased-fields='true' show-structure='true' />
      <style>
        <style-rule element='mark'>
          <encoding attr='color' field='[max:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[attr:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[:Measure Names]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409763848:qk]&quot;</bucket>
            </map>
            <map to='#9c755f'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:TotalProductCost:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[:Measure Names]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216406446083:ok]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407900165:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407973894:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216407998471:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418045966:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[usr:Calculation_447545216407670788:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[diff:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:cum:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[pcto:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:2]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:3]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk:4]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[rank:sum:SalesAmount:qk]&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912410996746:qk]&quot;</bucket>
            </map>
            <map to='#ff5500'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912409743366:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912411025420:qk]&quot;</bucket>
            </map>
            <map to='#ffffff'>
              <bucket>&quot;[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_597289912418058256:qk]&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_447545216406446083:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>1</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>4</bucket>
            </map>
            <map to='#e15759'>
              <bucket>3</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:EnglishProductCategoryName:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Accessories&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>%null%</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Components&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;Clothing&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Bikes&quot;</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:Calculation_561261115181088768:ok]' type='palette'>
            <map to='#4e79a7'>
              <bucket>2010</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>2014</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>2013</bucket>
            </map>
            <map to='#e15759'>
              <bucket>2012</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>2011</bucket>
            </map>
          </encoding>
          <encoding attr='color' field='[none:SalesTerritoryCountry:nk]' type='palette'>
            <map to='#4e79a7'>
              <bucket>&quot;Australia&quot;</bucket>
            </map>
            <map to='#59a14f'>
              <bucket>&quot;United Kingdom&quot;</bucket>
            </map>
            <map to='#76b7b2'>
              <bucket>&quot;Germany&quot;</bucket>
            </map>
            <map to='#b07aa1'>
              <bucket>&quot;NA&quot;</bucket>
            </map>
            <map to='#e15759'>
              <bucket>&quot;France&quot;</bucket>
            </map>
            <map to='#edc948'>
              <bucket>&quot;United States&quot;</bucket>
            </map>
            <map to='#f28e2b'>
              <bucket>&quot;Canada&quot;</bucket>
            </map>
          </encoding>
        </style-rule>
      </style>
      <semantic-values>
        <semantic-value key='[Country].[Name]' value='&quot;India&quot;' />
      </semantic-values>
      <field-sort-info field-sort-order-type='custom-order'>
        <field-sort-custom-order field='DateKey' />
        <field-sort-custom-order field='FullDateAlternateKey' />
        <field-sort-custom-order field='DayNumberOfWeek' />
        <field-sort-custom-order field='EnglishDayNameOfWeek' />
        <field-sort-custom-order field='SpanishDayNameOfWeek' />
        <field-sort-custom-order field='FrenchDayNameOfWeek' />
        <field-sort-custom-order field='DayNumberOfMonth' />
        <field-sort-custom-order field='DayNumberOfYear' />
        <field-sort-custom-order field='WeekNumberOfYear' />
        <field-sort-custom-order field='EnglishMonthName' />
        <field-sort-custom-order field='SpanishMonthName' />
        <field-sort-custom-order field='FrenchMonthName' />
        <field-sort-custom-order field='MonthNumberOfYear' />
        <field-sort-custom-order field='CalendarQuarter' />
        <field-sort-custom-order field='CalendarYear' />
        <field-sort-custom-order field='CalendarSemester' />
        <field-sort-custom-order field='FiscalQuarter' />
        <field-sort-custom-order field='FiscalYear' />
        <field-sort-custom-order field='FiscalSemester' />
      </field-sort-info>
      <datasource-dependencies datasource='Parameters'>
        <column caption='DynamicValue' datatype='string' name='[Parameter 2]' param-domain-type='list' role='measure' type='nominal' value='&quot;PROFIT&quot;'>
          <calculation class='tableau' formula='&quot;PROFIT&quot;' />
        </column>
      </datasource-dependencies>
      <_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
        <objects>
          <object caption='DimDate' id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimDate' table='[dbo].[DimDate]' type='table' />
            </properties>
          </object>
          <object caption='DimProductCategory' id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductCategory' table='[dbo].[DimProductCategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProductSubcategory' id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProductSubcategory' table='[dbo].[DimProductSubcategory]' type='table' />
            </properties>
          </object>
          <object caption='DimProduct' id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimProduct' table='[dbo].[DimProduct]' type='table' />
            </properties>
          </object>
          <object caption='DimSalesTerritory' id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='DimSalesTerritory' table='[dbo].[DimSalesTerritory]' type='table' />
            </properties>
          </object>
          <object caption='Sales' id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321'>
            <properties context=''>
              <relation connection='sqlserver.0k11eaw1owt4cc1b7523912w0z0e' name='FactInternetSales' table='[dbo].[FactInternetSales]' type='table' />
            </properties>
          </object>
        </objects>
        <relationships>
          <relationship>
            <expression op='='>
              <expression op='[OrderDateKey]' />
              <expression op='[DateKey]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimDate_9EC66DEF2B65424CB216051BCC33CAC1' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductCategoryKey]' />
              <expression op='[ProductCategoryKey (DimProductSubcategory)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProductCategory_B7603872C0DE49DBAB7949F0D2E44B7A' unique-key='true' />
            <second-end-point object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductSubcategoryKey]' />
              <expression op='[ProductSubcategoryKey (DimProduct)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProductSubcategory_AD2268E558794BFCA99892F6E071319B' unique-key='true' />
            <second-end-point object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[SalesTerritoryKey]' />
              <expression op='[SalesTerritoryKey (DimSalesTerritory)]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimSalesTerritory_0A9EF51FD1BA4B7BB96B82AF53275949' unique-key='true' />
          </relationship>
          <relationship>
            <expression op='='>
              <expression op='[ProductKey (DimProduct)]' />
              <expression op='[ProductKey]' />
            </expression>
            <first-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-unique-key='true' object-id='DimProduct_A4F40BE1B93E4B548BA67D5EA79D7763' unique-key='true' />
            <second-end-point _.fcp.ObjectModelRelationshipPerfOptions.true...is-db-set-guaranteed-value='true' guaranteed-value='true' object-id='FactInternetSales_7A4EB2F047E7498A8D0D39005C59D321' />
          </relationship>
        </relationships>
      </_.fcp.ObjectModelEncapsulateLegacy.true...object-graph>
    </datasource>
  </datasources>
  <worksheets>
    <worksheet name=' Sales By Month'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#9d7660' fontname='Tableau Semibold' underline='true'>Month wise Sales</run>
          </formatted-text>
        </title>
      </layout-options>
      <repository-location derived-from='https://public.tableau.com/workbooks/Adventure_Works_16636772947660/SalesByMonth?rev=' id='65282670' path='/workbooks/Adventure_Works_16636772947660' revision='' />
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='CLEAR FILTERS' datatype='string' name='[Calculation_295830217743659008]' role='dimension' type='nominal'>
              <calculation class='tableau' formula='&apos;Clear All&apos;' />
            </column>
            <column caption='MonthName' datatype='string' name='[Calculation_447545216416518153]' role='dimension' type='nominal'>
              <calculation class='tableau' formula='DATENAME(&apos;month&apos;,[OrderDate])' />
            </column>
            <column caption='OrderDate' datatype='date' name='[Calculation_474848296898699264]' role='dimension' type='ordinal'>
              <calculation class='tableau' formula='DATE(LEFT(STR([OrderDateKey]),4)+&quot;-&quot;+MID(STR([OrderDateKey]),5,2)+&quot;-&quot;+RIGHT(STR([OrderDateKey]),2))' />
            </column>
            <column caption='Year' datatype='integer' name='[Calculation_561261115181088768]' role='dimension' type='ordinal'>
              <calculation class='tableau' formula='YEAR([OrderDate])' />
            </column>
            <column caption='Reset Filters' datatype='string' name='[Calculation_778278323468275715]' role='dimension' type='nominal'>
              <calculation class='tableau' formula='&apos;Reset Filters&apos;' />
            </column>
            <column aggregation='Sum' caption='Order Date Key' datatype='integer' name='[OrderDateKey]' role='dimension' type='ordinal' />
            <column caption='Order Date' datatype='datetime' name='[OrderDate]' role='dimension' type='ordinal' />
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column-instance column='[Calculation_474848296898699264]' derivation='Month' name='[mn:Calculation_474848296898699264:ok]' pivot='key' type='ordinal' />
            <column-instance column='[Calculation_447545216416518153]' derivation='None' name='[none:Calculation_447545216416518153:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_561261115181088768]' derivation='None' name='[none:Calculation_561261115181088768:ok]' pivot='key' type='ordinal' />
            <column-instance column='[SalesAmount]' derivation='Sum' name='[sum:SalesAmount:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (CLEAR FILTERS)]'>
            <groupfilter function='level-members' level='[Calculation_295830217743659008]' user:ui-action-filter='[Action2_97DBC24A633E4E5B98F58ABF435EE34C]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Reset Filters)]'>
            <groupfilter function='member' level='[Calculation_778278323468275715]' member='&quot;Reset Filters&quot;' user:ui-domain='relevant' user:ui-enumeration='inclusive' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Year)]'>
            <groupfilter function='level-members' level='[Calculation_561261115181088768]' user:ui-action-filter='[Action2_97DBC24A633E4E5B98F58ABF435EE34C]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216416518153:nk]'>
            <groupfilter function='level-members' level='[none:Calculation_447545216416518153:nk]' user:ui-enumeration='all' user:ui-marker='enumerate' />
          </filter>
          <slices>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Year)]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (Reset Filters)]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[Action (CLEAR FILTERS)]</column>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216416518153:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='cell'>
            <format attr='text-format' field='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' value='c&quot;₹ &quot;#,##0,,.00M;&quot;₹ &quot;-#,##0,,.00M' />
          </style-rule>
          <style-rule element='header'>
            <format attr='height-header' value='17' />
          </style-rule>
          <style-rule element='label'>
            <format attr='font-family' value='Tableau Semibold' />
            <format attr='color' value='#9d7660' />
            <format attr='font-style' value='italic' />
          </style-rule>
          <style-rule element='gridline'>
            <format attr='stroke-size' scope='rows' value='0' />
            <format attr='line-visibility' scope='rows' value='off' />
          </style-rule>
          <style-rule element='zeroline'>
            <format attr='stroke-size' value='0' />
            <format attr='line-visibility' value='off' />
          </style-rule>
          <style-rule element='title'>
            <format attr='border-width' value='1' />
            <format attr='border-style' value='solid' />
            <format attr='border-color' value='#000000' />
            <format attr='background-color' value='#e6e6e6' />
          </style-rule>
          <style-rule element='quick-filter'>
            <format attr='border-width' value='2' />
            <format attr='border-style' value='solid' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Line' />
            <encodings>
              <color column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]' />
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-cull' value='true' />
                <format attr='mark-labels-show' value='true' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows>[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:SalesAmount:qk]</rows>
        <cols>[federated.1nl426t13auwkc10rmst40m1iuwe].[mn:Calculation_474848296898699264:ok]</cols>
      </table>
      <simple-id uuid='{73108CD8-0A77-467A-927C-D46E534A5893}' />
    </worksheet>
    <worksheet name='Country by Profit'>
      <layout-options>
        <title>
          <formatted-text>
            <run fontalignment='1' fontcolor='#9d7660' fontname='Tableau Semibold' underline='true'>Country by Profit</run>
          </formatted-text>
        </title>
      </layout-options>
      <table>
        <view>
          <datasources>
            <datasource caption='FactInternetSales+ (AdventureWorksDW2022)' name='federated.1nl426t13auwkc10rmst40m1iuwe' />
          </datasources>
          <datasource-dependencies datasource='federated.1nl426t13auwkc10rmst40m1iuwe'>
            <column caption='Profit' datatype='real' name='[Calculation_447545216423890964]' role='measure' type='quantitative'>
              <calculation class='tableau' formula='([SalesAmount])-([TotalProductCost])' />
            </column>
            <column caption='Sales Amount' datatype='real' name='[SalesAmount]' role='measure' type='quantitative' />
            <column caption='Sales Territory Country' datatype='string' name='[SalesTerritoryCountry]' role='dimension' semantic-role='[Country].[ISO3166_2]' type='nominal' />
            <column caption='Total Product Cost' datatype='real' name='[TotalProductCost]' role='measure' type='quantitative' />
            <column-instance column='[SalesTerritoryCountry]' derivation='None' name='[none:SalesTerritoryCountry:nk]' pivot='key' type='nominal' />
            <column-instance column='[Calculation_447545216423890964]' derivation='Sum' name='[sum:Calculation_447545216423890964:qk]' pivot='key' type='quantitative' />
          </datasource-dependencies>
          <filter class='categorical' column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]'>
            <groupfilter function='except' user:ui-domain='relevant' user:ui-enumeration='exclusive' user:ui-marker='enumerate'>
              <groupfilter function='level-members' level='[none:SalesTerritoryCountry:nk]' />
              <groupfilter function='member' level='[none:SalesTerritoryCountry:nk]' member='&quot;NA&quot;' />
            </groupfilter>
          </filter>
          <slices>
            <column>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</column>
          </slices>
          <aggregation value='true' />
        </view>
        <style>
          <style-rule element='title'>
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
            <format attr='border-color' value='#000000' />
            <format attr='background-color' value='#e6e6e6' />
          </style-rule>
        </style>
        <panes>
          <pane selection-relaxation-option='selection-relaxation-allow'>
            <view>
              <breakdown value='auto' />
            </view>
            <mark class='Pie' />
            <encodings>
              <color column='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' />
              <wedge-size column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk]' />
              <text column='[federated.1nl426t13auwkc10rmst40m1iuwe].[sum:Calculation_447545216423890964:qk]' />
            </encodings>
            <style>
              <style-rule element='mark'>
                <format attr='mark-labels-show' value='true' />
                <format attr='mark-labels-cull' value='true' />
              </style-rule>
            </style>
          </pane>
        </panes>
        <rows />
        <cols />
      </table>
      <simple-id uuid='{15DCBBDE-6664-443E-AC20-B0DDFCE17DF0}' />
    </worksheet>
  </worksheets>
  <dashboards>
    <dashboard _.fcp.AccessibleZoneTabOrder.true...enable-sort-zone-taborder='true' name='AdventureWorks'>
      <layout-options>
        <title>
          <formatted-text>
            <run bold='true' fontalignment='1' fontcolor='#9d7660' fontname='Tableau Medium' fontsize='20' italic='true' underline='true'>Adventure Works Sales Dashboard</run>
          </formatted-text>
        </title>
      </layout-options>
      <style />
      <size sizing-mode='automatic' />
      <zones>
        <zone h='100000' id='4' type-v2='layout-basic' w='100000' x='0' y='0'>
          <zone h='98120' id='172' param='horz' type-v2='layout-flow' w='99034' x='483' y='940'>
            <zone h='98120' id='149' param='horz' type-v2='layout-flow' w='97103' x='483' y='940'>
              <zone h='98120' id='115' param='vert' type-v2='layout-flow' w='97103' x='483' y='940'>
                <zone fixed-size='62' h='8226' id='116' is-fixed='true' type-v2='title' w='97103' x='483' y='940'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='background-color' value='#e6e6e6' />
                  </zone-style>
                </zone>
              </zone>
            </zone>
            <zone fixed-size='32' h='98120' id='171' is-fixed='true' param='vert' type-v2='layout-flow' w='1931' x='97586' y='940' />
          </zone>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='none' />
            <format attr='border-width' value='0' />
            <format attr='margin' value='8' />
          </zone-style>
        </zone>
        <zone h='83431' id='32' name=' Sales By Month' w='48220' x='845' y='13984'>
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='2' />
          </zone-style>
        </zone>
        <zone h='83314' id='170' name='Country by Profit' w='48702' x='49366' y='14219'>
          <layout-cache cell-count-h='1' cell-count-w='1' non-cell-size-h='50' type-h='cell' type-w='cell' />
          <zone-style>
            <format attr='border-color' value='#000000' />
            <format attr='border-style' value='solid' />
            <format attr='border-width' value='1' />
          </zone-style>
        </zone>
        <zone h='17391' id='173' name='Country by Profit' pane-specification-id='0' param='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' type-v2='color' w='8449' x='88775' y='27967' />
      </zones>
      <devicelayouts>
        <devicelayout auto-generated='true' name='Phone'>
          <layout-options>
            <title>
              <formatted-text>
                <run bold='true' fontalignment='1' fontcolor='#9d7660' fontname='Tableau Medium' fontsize='20' italic='true' underline='true'>Adventure Works Sales Dashboard</run>
              </formatted-text>
            </title>
          </layout-options>
          <size maxheight='750' minheight='750' sizing-mode='vscroll' />
          <zones>
            <zone h='100000' id='187' type-v2='layout-basic' w='100000' x='0' y='0'>
              <zone h='98120' id='186' param='vert' type-v2='layout-flow' w='99034' x='483' y='940'>
                <zone fixed-size='62' h='8226' id='116' type-v2='title' w='97103' x='483' y='940'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                    <format attr='background-color' value='#e6e6e6' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='83431' id='32' is-fixed='true' name=' Sales By Month' w='48220' x='845' y='13984'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='2' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone fixed-size='280' h='83314' id='170' is-fixed='true' name='Country by Profit' w='48702' x='49366' y='14219'>
                  <layout-cache cell-count-h='1' cell-count-w='1' non-cell-size-h='50' type-h='cell' type-w='cell' />
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='solid' />
                    <format attr='border-width' value='1' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
                <zone h='17391' id='173' name='Country by Profit' pane-specification-id='0' param='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' type-v2='color' w='8449' x='88775' y='27967'>
                  <zone-style>
                    <format attr='border-color' value='#000000' />
                    <format attr='border-style' value='none' />
                    <format attr='border-width' value='0' />
                    <format attr='margin' value='4' />
                    <format attr='padding' value='0' />
                  </zone-style>
                </zone>
              </zone>
              <zone-style>
                <format attr='border-color' value='#000000' />
                <format attr='border-style' value='none' />
                <format attr='border-width' value='0' />
                <format attr='margin' value='8' />
              </zone-style>
            </zone>
          </zones>
        </devicelayout>
      </devicelayouts>
      <simple-id uuid='{5CA7A8AA-0B19-4C3D-BDD1-8F8F88AD34F6}' />
    </dashboard>
  </dashboards>
  <windows saved-dpi-scale-factor='1.25' source-height='37'>
    <window class='worksheet' name=' Sales By Month'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='116'>
            <card param='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_447545216416518153:nk]' type='filter' />
            <card pane-specification-id='0' param='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:EnglishMonthName:nk]</field>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[yr:Calculation_474848296898699264:ok]</field>
          </color-one-way>
        </highlight>
        <default-map-tool-selection tool='2' />
      </viewpoint>
      <simple-id uuid='{3DFEB22D-3448-4AC6-8433-B0CE8ADFD716}' />
    </window>
    <window class='worksheet' name='Country by Profit'>
      <cards>
        <edge name='left'>
          <strip size='160'>
            <card type='pages' />
            <card type='filters' />
            <card type='marks' />
          </strip>
        </edge>
        <edge name='top'>
          <strip size='2147483647'>
            <card type='columns' />
          </strip>
          <strip size='2147483647'>
            <card type='rows' />
          </strip>
          <strip size='2147483647'>
            <card type='title' />
          </strip>
        </edge>
        <edge name='right'>
          <strip size='160'>
            <card pane-specification-id='0' param='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]' type='color' />
          </strip>
        </edge>
      </cards>
      <viewpoint>
        <zoom type='entire-view' />
        <highlight>
          <color-one-way>
            <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</field>
          </color-one-way>
        </highlight>
      </viewpoint>
      <simple-id uuid='{DBCBA622-6922-47AE-A080-914CA571D0B9}' />
    </window>
    <window class='dashboard' maximized='true' name='AdventureWorks'>
      <viewpoints>
        <viewpoint name=' Sales By Month'>
          <zoom type='entire-view' />
          <highlight field='[federated.1nl426t13auwkc10rmst40m1iuwe].[none:Calculation_561261115181088768:ok]'>
            <bucket-selection />
          </highlight>
        </viewpoint>
        <viewpoint name='Country by Profit'>
          <zoom type='entire-view' />
          <highlight>
            <color-one-way>
              <field>[federated.1nl426t13auwkc10rmst40m1iuwe].[none:SalesTerritoryCountry:nk]</field>
            </color-one-way>
          </highlight>
        </viewpoint>
      </viewpoints>
      <active id='170' />
      <simple-id uuid='{DF6D76A4-4DF0-4E63-AF57-74BF98439136}' />
    </window>
  </windows>
  <thumbnails>
    <thumbnail height='192' name=' Sales By Month' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAgAElEQVR4nO29WXAc55Xv+cusrB1VKOz7QgAkQQAkuIkUKUrUass2ZcuyvEiy29EdvnMn
      4sbYPc/90m8zETMdMTGecXR032u3rXarx+622rIlWyJlbuACgli4gACxk9gLte9LZn7zUCTa
      tkiJALGRyF8Eg1lZVXm+LJx/fud8qySEEBgYbFLk9S6AgcF6YgjA4JFHCB01k+HPgxld0/7s
      cwI1k0ZT1cVzypqU0MBglRBahv7THxDxzVK574sU5dtQ7HnEfVPMTs2zpXkrksWNrEZQs2l6
      Tn6MLiQOffmbRHxeJCMHMHiUCQxfIqJUY4kOE4wkSakW5GwEk0gjO4uJ+OdRLA5IJXAXuijZ
      9TxTl0/gLvJw81KnEQIZPOKYFG5dPkE4Y0dRrLQ/fZRkYJZExkSeHZKxJEUlRdQcfInwwgzj
      nR9S2tDM5PAwhfWta1cDCCGQJGktTBkYPDBrVgPour5WpgwMHhgjBDLY1BgCMNjUGAIw2NQY
      AjDY1BgCMNjUGAIw2NQYAjDY1BgCMNjUGAIw2NQYAjDY1BgCMNjUGAIw2NQsY0KMQNcFkiwj
      dA2QkCQJXdeQZRNC15EkGYFAkkAICVk2RoEabEyWLIDg7UGuXOrj8Oeepe/MaVKaQnmJk+mR
      URoPPs38yHVkZw1mPYjV7qC85TD5+c7VKLuBwUOz5BDI6fFQWr+V0Nw06XiIopIiVFslFVsa
      mBsfp/XJQygixtSNq1gr2sjPd6Jp2ifmaxoYbASWJIBUcJquD94lGo0zOzKA1V1MQVE+80Pd
      KIX1SCkfN3oGyHM52Pul10gF/QCYTCZjMozBhmTNZoRpmobJZFoLUwYGD4zRCmSwqTEEYLCp
      MQRgsKkxBGCwqTEEYLCpMQRgsKkxBGCw4RgbG+Odd97h2rVrCCHo6OjA5/MBEI/H+fDDD/F6
      vYTDYTo6Oujp6cHv9zM1NcWVK1eWZMtYHNdgQxKNRvF6vei6jtvtJhgMUlxczJkzZ5BlGU3T
      uHz5MplMBkmS6O/vJ5vNIstLe6YbNYDBhsPr9aIoCs899xwmkwmXy7X4ntls5sCBA3R0dBAI
      BLh+/TqapqHrOiaTackCMGoAgw3HwYMHKSoqQtM0VFVlaGgIs9mMy+Xi0KFDXLx4kRdeeIHC
      wkImJycxm83ouo7ZbCaTySzJljEUwmBTY4RABo8UH9y8wa+uLy3R/TQMARg8MvgTcYb9Pmai
      Ea7MTq/IEHsjBzB4JEirKr+41scrza1UuNy8c6WbeCbDodr6hxpqv+QcIBGYYfDKdfYcfY7h
      i38gRR5lxTZG+2/S8szzTFw+g6OyDbMewWYzYy/bjseTZ+QABstG03X+7foVGgqLeKK6FoCs
      pvGLa31Uut0c3dKEvEwRLDkEEkLDpFiZuXqGmalZXG478wtJSssKuNl1nqaDR4jPjzN47jgx
      3U3+Hec3ZoQZLAchBGcnxrCbzeyvqlk8bzaZ+OauPQQSCd4fvIG+TP9aUg2QTYYZ7fyYpLUG
      JTaGye5GaCrhuMDpcpNYGMHhLsLmdCFbLWjmcpp3bgOMViCD5XFtbpauqdt8Z89+zPfwH03X
      +XB4kJSq8kpz6z0/82ksSQBqKkrA68Vsd+N0WkgksuQXegjOzeIpryYZ9iKZ85BEFovdQSqZ
      wuly5wpqCMBgicxFI/z79at8d98T5Fmsn/rZEyNDzMUifHPnniWJwOgHMNiQxDMZftLdydfa
      2qm48xD9NIQQXJq6Tf/8HN9q34PDbHkgO0YzqMGGI6tp/PJaH89saXwg5weQJIkD1bXsrqjk
      X/q6iaXTD/Q9QwAGGwohBB8N36Q638Ou8solfVeSJPZUVnOkvoF/6rlEMJn4zO8YAjDYUPTO
      TBFNp3i+ceuyvi9JEs0lZXxpews/7+tmIR771M8bAjDYMEwEA3RNTfKVlp3Lbte/y5bCIr7a
      uot/6etmOhK+7+cMARhsCILJBL8d7OfrO3djN5tX5JpV7nze3L2PX9+4xljAf8/PGAIwWHcy
      msq/Xb/CF7btoNDhWNFrlzjzeKN9L2cnRgkkPpkTGAIwWFd0ofPrG9dpK6ugsah4VWwU2B28
      vHUHP++7zMyfhUOGAAzWDSEEp8dHMckyB2vqVtVWmcvF6zt386v+q9wKBRbPGwIwWBeEEAz7
      FxgL+DnW3PLQSe+DUOFy8+buffxmoJ+bC17A6Ak2WCfmY1F+ca2Pv9x34DOHOaw0kVSKf73a
      w5M19YYADNaeWCbNz3q6ONbcSq2nYF3KkMxm+HlfjyEAg7VF03X+5UoPu8oraK+oWteypFXV
      yAEM1g4hBCdGhijPcy15mMNqYFWUpQtAV9NMDQ8ggHTEy/jADZLBWQYuniWTSTPRe5aFOS++
      yTFi/mki4U/vijbYPFyZncEbj/JC07YNs2PQkgUQnhllcngYLRnm5qXTRIJ+Bnt6IOGjv+M4
      loJibl+9TP/p97k1Okmey4mu68aMsE3OrVCAC5MTvN62e01afB6UJQlACB2TLCiub6bv9++w
      MDWJnvRjLawhLcxk00mIeLE7FewFJdgLq4wtUg2IplP8dvAGr7e2r9gwh5ViSUlwKjTDwKVO
      bMW17Ni7D+/EEAVlpQx0XmDLvqOExrvRrGW47DLOwiLCwThlNdWAkQRvVrKaxk97uni6voHt
      JaXrXZxPYLQCGawamq7z7o1rlDrzeLq+YcPE/X+M0QpksCoIIeicvAXAkQ3q/GAIwGCVGPH7
      uOGd58s7WjdU0vvHJBIJQwAGK483FuV3QwO83taOxbSxFx80BGCwoqTULP9+/Spf2dGGx25f
      7+J8KrquGwIwWDk0XedX/Vd5sraOuoLC9S7OZ+J0Og0BGKwcJ8dGyLfa2VNZvd5FeSAkSTIE
      YPDwqLrOhdvjzEbDfG7r9vUuzpIwBGDwUAghOH9rnPcG+vni9pYlr8253hgdYQbL5u6UxrGA
      n5eatlGV79mwTZ73wxCAwbJIqSq/6r9CvtXO57dtJxqOMDo6itPpZMeOHaRSKTKZDG63GyEE
      g4ODVFVVkUwmSSaTFBbmkmSHw8H8/DxVVeszN2BjN9IabEj8iTi/uNbH/qoa9lfVIEkSiUSC
      7u5uAHbs2MHly5cBOHLkCJFIBJvNxpkzZ8hms1gsFtxuN4lEgpqaGnp7e3nrrbfW5V6MHMDg
      gRFCMOL38c6VHl7e2swT1bWLQxyy2Sx2u50vf/nLALS3ty/W+E6nk6GhIfbt24eu68RiMRRF
      Qdd1AoEAxcWrsxzKg2DUAAYPhC4EnZMTXJ2b5a3d+yiw/+kCVnV1dTQ1NWGz2QDo6upiamqK
      mpoaQqEQ8XicQCCA3W5HVVXy8vIoKytjdnZ2MRxaD5aRAwhS8RhWp4t0NICGGbvDSjQYxl1U
      TDLsx2R3g5pGsZhRNQmrzWrkAI8wWU3j/Zs3yGoaX9nRhkV5fJ6bS74T/0Q/17qv8/SxLzLR
      34tv3o8n30bU66Vo6w6S4QBZ1Y6c9WN1F9Gw75nVKLfBGhFNp3Ib1BUUcaS+AZP8eEXNS74b
      V4GH8oYWUpEQQpjY2tKEUtCEu6ycsHeBhu2NWMwqvqnb5FXvxG6zGJvkPYIIIZiOhPlZ72We
      rKnnaEPTY+f8sEQBpILTdB//PYlYmBsdHxINh7A48/GNXMZduxOrlGR0eJ6CokKeeOXrqLEg
      ACaTacOOBzf4JEIIrs3P8m7/VV5va2dHadl6F2nVMPoBDP4EVdM4OTbCXCzC620rt1T5RsUQ
      gMEiyWyGf7t+lRKnkxebtqHIj//f6/FJ5w0eCl88xv93rY/DtfXsrqjaNCHrfQWgaSqmDT6b
      x2BlGFyY5+ORIb68o43qfM+mcX64TxKcCs1z9lf/TDwwh9/rW+syGawRQgjOToxx7tY4b+3e
      T42nYFM5P9wnBxBCZ6K3g1QiRl71LmrqH36Cg5EDbCwymspvB2+gC8Erza1YH6POraVwz7uW
      JBn/9Bi2gkqs1se7FWA1mJ2d5ezZs7hcLr7whS9w6tQpiouLaWtrw+fz0dHRwbZt2xgfHyc/
      Px+n04kkSZSWlnL58uXF8TSrRTiV5BfX+mgtLedw3ZZVtbXRuW8/QF5+IRZHPo68vLUsz2NB
      NBplfHycZDIJwM6dOxePR0ZGePnll/F6vUiSRCQSQdM05ufnGR8fx2KxrFq5hBDcCgZ4u/cy
      zzY0cai2ftVsPSrcVwAOTxESgkwquZbleSxwuVyUl5dz7NgxAGKxGNFolHQ6jcPhYHh4GFVV
      2bZtG7quk8lkcDgcpNPpVQsThRD0zkzzwdAA39q1h61FJZsu3r8X9+0H8E+OkAxOE9GLadnd
      +tCGNlsOEI/HkSQJh8PBlStX0HWdmpoaPB4PIyMjNDQ0MD8/jyRJFBYWYjKZSKVSSJKE2+1e
      0bKous6Hw4OEkkm+1roL22PeubUU7iuAyOwYY/19FDU+Qc2Wmoc2tNkEsFFIZDL88nofVW4P
      zz2m43kehvum/kOXz2JzF2PLc61leR57To4NMxbw59rbkVBkGQlQ7jimLEnId47Nd/6XJGnR
      cU2SjCz96TlFkhfDmbvfMckyYwE/V+ZmeLqugZ3lFUbIcw/uK4Cqph3MjN3ENzNDSYlnLcv0
      WCKEYCoc4trcLDtKy6h2exAIVF0HcotKCZGbeKKJ3Lm0pgKgi9z7AJrIbTYiBIvfVXUdgVg8
      vnu9Yf8CzzVsZVfF+m9HtFG5rwB80xNYXGWUVVWsZXkeW4b9CxwfGeI7e/Z/YjbVaqHpuhHy
      fAb3/XUy6TTOwhIslj/XiCCdiOf2CIuHSSaSCC1LNOBHCEEyEiCTzpBOxNCyadLpzOrewSPA
      4MI8J0aGeLN974o7f3d3N++++y4zMzNomkZnZ+fie7MzM5w9e5ZkMsmHH37IyMgIXV1d6LrO
      jRs3CAQCn3LlzcF9awBJMmG328j8mQPnZoRd44lnnuTm1WskYhkK8i2E5+cp2d5GPLiAqjsx
      Zf1YXQXU7z266jexURFCcH1+louTt/j2nv24rbZVsdPf38+BAwdIpVKMj49z8OBBAAYGBqir
      q+P8+fMEg0E8Hg+dnZ2UlZXR09PD/v3713U+7kbgvjVA29EvkY6FULPZPznvKiigrKGFWwM3
      2L7vAFY5hamgCU95JaH5eZp2bMNiyuC9PYGzehcO++adEdY9Pcm5WxN8e/fqOf/g4CBHjhyh
      srISp9NJUVHR4nuKomC326murubrX/86Fy5coL6+nu7ubkpLN952RevBPZtBp6+e4vKpDiwO
      O7u/8BYVVeUAqOkY0wNXkfLKkVJe0hlBcWUF81PT5BVVkQ7cQpdsFJQUodgdpFISZdW5HGIz
      NYNmNY3T4yNE02m+uL1lVcfZaJpGX18fe/fuxe/3c+LECVpbWykuLiYajTI2NsbevXvp7u6m
      sbERq9WKz+ejuroaWZYpKSlZtbI9Cty7H0AI4v5pev/wATue/wZFxQ/fCrRZBJBWVX5xrZf6
      giKO1G0xmh43OPftCNM1FQlIpdLYnc6HNrQZBJBSs/zrlV62FpdwuLbecP5HgPvWzT3H/wNF
      pKh54hj2h/f/x55EJsM7V3vYU1HN3qpHY318g09Jgqsb6lGseczdvrWW5XkkiaRT/KTnEgeq
      a9lTuT6LvBosj/vWAHOjgwjFQVnx5k6SPotQMsm/Xu3h+YatNJeUGmHPI8Y9awAhBCW1TUTm
      Jwj6/CtiSFVVTp06xUcffUQymSSVSnHp0qU73fqC4eFhhoeH0TSNCxcuEIvFGB4eXlxaeyM2
      o/ricd652sPntzazo7TMcP5HkHsKwD9xnaA/TP0TX2DbrpYVMyaE4NKlSwCcPXuWsrIy+vr6
      UFWVmzdvMjAwwMmTJ1FVFV3X+c1vfkMsFuPdd99F07QVK8dKMB+L8s6Vbr6wbQdbCos++wsG
      G5J7CqB4y048hW6C0yP45uZXxJAQgpGREY4dO4bdbsdsNlNZWUkikUAIQSqVIpVKEQqFqK2t
      pbe3l/r6ek6fPk1jY+OKlGGlmItG+PfrV3i1dRf1j8BuiAb3575J8MLsLHLaRzgYXRFDZrOZ
      Y8eOYTabEUJQVVXF+++/T0tLCz6fj7y8PPLy8ti3bx9Xr16lvr6e1tZWZFmmubl5w4QX40E/
      v7zWx+ttu6nJN0bJPurcsx/Af2sQJb+S0Nhl5IImarbUPrShx6EfYCzg57eD/bzRvpcSpzFX
      +nHgnjVAcOom3qHLjAyOo1jXZ7dvIQQfDg8y4J1f9wRYCMHgwjzHR27y3b0HDOd/jLhnDZBN
      hhnu6aK8ppyZBY22fe0PbWipNYAQgh93d5LVdCQJaj0FtJaWU+Fyr+lWnEIIrs7NcP52blCb
      y2pdM9sGq8+nLo6rq2k0YcJsfvjBXA8TAsUzGUYDPoZ8XmYiEcrzXOwoLaepqBiboqxafnDX
      +bumJvnWrj3kGc7/2PHIrQ6t6Tqz0Qj983OMBnzYFIWGwmJaSssoceatmBiEEFyaus2Ad55v
      te/BphgrKTyOPJQA0vEIQlKwWs3EozGc+R7SsTCy1QlaBpNZQdMlLBbLqiTBuhBEUimGfF6G
      /QsEk0lqPQWLc26Xu7a9EIIzE6OMBwK80b530y4buBlYtgDSoUl6z55DFWYK8y0EZ2cp27GL
      mH8OVbgxqQGsefnU73kGh2NtNslLZDNMhUMMLni5FQpS5HDQWFhMc0kpLqvtgXYx14XgzPgI
      s9Eor7e1r2m+YbD2LFMAgpkbnUwOD+AsqkZxVaGHxomlsrTs3sHYwBgzN6+w4+W/pK6ubHFG
      mLKGT1IhBPOxKIMLXkYDPtKqytbiEppLyqh2598zVNKF4Hc3b5BSVV5t2WlMKF8nxsbGuHTp
      Eq2trbS2tnL27FmKiopoa2tjdHSU0dFRysvLqa+v5/r169jtdurq6kgmk/j9fnbt2vXAtpbt
      kaHZKWRbAeW1NQxc6qRu99Nkh88zPDBJUaGHqle+QcAfAMowmUxrPpRBkiTKXW7KXW6Obmkk
      kc0wGvBzbmKM+XiMGnc+20pK2VJQhNNiyQ29GOxHgOH8G4BIJILX66W0tJSioiIWFhYQQtDY
      2EgqlaKgoICuri4ymUxu7vX162Sz2cU1lR6UZQpAouWF1xdfPV3bDEBtU8OffKpogyxHI0kS
      TouVXeWV7CqvJKOqzEYj3PDO0zExjlUxEU2n2VpUwue2bjecf53xer0oisJzzz2H3+8nm80u
      PkB1XWd2dpaKigp8Ph+3b9/m4MGD6Lq+rM0YH7lWoJXm7nagp8dHebWlDafFaOpcb4QQDA0N
      0dDQgKIoXLp0iaKiIvLy8nC73USjUcrKcjtX3rp1C8udGtxsNpPJZKiufvAJSZteAAabm03T
      vhcMBjl79iyqqvLVr36VCxcuEIlE+PznP08wGKS7u5tMJkNBQQETExPs3LmTRCJBS0sLJ06c
      4Ktf/ep634IBkOn5GULNYD3wvRW53qYRQDabJRQKkUgk0HUdVVUpLi4mFotRWFjI7t27mZ6e
      pr29nWQyiSRJLCwsMDIygn5nvU2D9UOko2S6/jvq5CUkk4Wk9wbmPd/GVNGO9BDbuW4aAciy
      jNfr5fvf/z6yLBOPx8lkMos7sly9epXnnnuOqakpqqqq0DSNwsJCJicnV3y9foMHRwiBOnKC
      TNd/x9zyKo5v/BRkM7pviMzln5C5+CMse7+Lqf7IskYBbKocIJlMkkgkKCoqYm5ujkgkQklJ
      CVarlWQySVFREYFAAI/HQyKRQFEUQqEQiqJQXFy8rmXfbAgh0ANjpM/+HbKjCMtTP0ByFP2J
      k+c+M06m6x8Q4Wks+/8K05ankeQHf65vKgEYPBqIVIRM79tok5ewHvlfkSt2IUn3b5oWQqAH
      x8l2/xQ9NIl59xsoDUeRTJ+935ohAIMNgxA66sgJsj3/jLL1RcztbyCZljaeS4/MkOn9Obp3
      AHPLKyjbv4ik3L9p2xCAwYZAD0+ROvW/IdkLsR76b8iu8oe7XsJPtu8d1LGTmNu+hrnlVSTL
      J5emNwRgsK6IdIRM7z+j3bqA5anvY6rav6JD2kUqTLb3bbTpHix7/+ITOYIhAIN1Qega6vjp
      XOvOtpcx7/wGknl1lpAH0MPTZC7/BD0wirn9To6gWA0BGKw9evAW6Y7/CxRrLsl1la2d7dhC
      LsGe7sbc+qohAIO1Q2STZLr+B9pUF5ZD/w2l5sD6lSUVJtP7z4YADFYfoWtoEx2kO/8+F+60
      f+tTW2bWrFxCLF8AuppkoOMEqtlDoQvGB4bZ8fTz3Oo9h710B2Y9gtVmJr+uHY8nzxDAJkQI
      gQhPkj7/Q5BkrE/9NbL7s3cdvXnzJh999BHt7e0cPHiQDz74ALfbzfPPP8/AwAAjIyMcOHCA
      3t5eSktL8fv9bNmyhXg8js/n48UXX3zgMi574Ptk72kC/iB2q0woYaGiuoKxKz20HnmObGSO
      ka7TaI7qRedf77V9DNYWoabIXPoHkh/+DeaWV7G9/L8/kPMD+P1+5ubmcDqd+P1+2traFlcU
      VBQFk8nE+Pg4k5OTzMzMkJeXx8zMDH6/H/MS54EvWwCxSIQ8Tz6SliQ4M0ZGKUCPLzBwsROH
      00Lrc18gHY8BLGuigsGjiRA66vhZEr/8KyTFiuO1f0Cpf2pJf/+6ujoOHTrE7t27cblc3Lx5
      k2g0SjQaxWKxUFFRQTKZpKCgAF2/u3G4wGZbeivSskMgLZsim9Wx2m2kYlFseW6yqXiu+1no
      mMxm1KyK5c5aOkYI9PijhyZJn/+/QYhc607+8jcLSSQS2Gw2ZFkmGAxis9kQQmA2m/H5fJSV
      lRGPxxFCYL3jY7Iso+v64usHYdMkwcFgkI6ODhRF4eWXX6avr49IJMIzzzxDJpPh+PHjNDY2
      YrfbCQaDlJeX4/P5aGpq4ty5c0uKK1eSu0+4u3NdhRBIkoQkSX/y3t3zf/waWNWaVwgBuoo2
      d41s79uIuA/Lgf+CsuWZ1bOpqgBIK7DAQiKRWLvh0KqqcubMGXw+H8eOHSMSidDX18fzzz+P
      2Wzm3LlzSJLEtm3b6Onp4ciRI/T09PDUU09x6tQpjh49uuQJz/didHQUXdcJh8PY7XYSiQSJ
      RIJIJIKiKLjdbkZHR7FYLExMTAC5mHS9ePvttzl58iR/8zd/g6qqDA4OUltby759+3jvvffw
      eDyUlpYyNDREeXk5Z86c4Qc/+AH/+I//yKuvvrqk6YGfhdAy6KHb6L5htLnr6AuDCDWN7ChE
      JIPYv/JDJNvqrZgtslkW/s+/Q49EKfirv8TS1PhQAk+n02s7H0CSJAYHBzl27Bj9/f0cOnSI
      7u5uDhw4wMjICLt37+batWscOHCAq1ev0t3dzfbt2+nu7ubpp59+KAGk02muX7/OD37wA2RZ
      JpvNYjKZkGUZl8vFa6+9xkcffcSXvvSlxe/k5+czOTlJQUHBkmzNzs7S09NDUVERTz75JP39
      /UiSREtLbrOR27dvL1blAAUFBYviGx4epr39P9didbvdvPjii9TV1dHV1cWXvvQlLl68CEBt
      bS3Dw8NEIhFeeeUVOjo62L17N11dXeTn33vplwdFqClEdA7NO4A2fwPdP4JIhZHdlcilzShb
      jiA/8VdItnwkWVmsgVYDIQTZqSlCb/8cc20Nlvo6or/7HarPh+OJJ3A8eRBTcfGS7efl5a2d
      AIQQXL58mTfffBO7Pbfi9F2HliSJN954g+PHj2OxWBar8q1bt3L8+HGampoe2n55eTnf+973
      iMfjOBwO6uvriUQipFIphBBcuXKF9vZ2ent7icfj5OXlUVtby/z8/JJiyrtEo1EikQgHDhxg
      ZmYGRVHYunUrABcvXqSqqgpVVUmn04TDYZLJJB6Ph9nZ2T8RwM6dO1lYWMBsNlNSUsJvf/tb
      GhoamJ2dJZ1OA1BdXc2vf/1rampqsNls3Lhxg/b29gdyiLthjEj40b0DaPP96L4h9Ng8srMU
      uWQbStVe5D1vIjlL7zv7amFhgXPnzuFyuXjxxRe5cOECqqpy5MgRYrEYJ0+epKWlhVQqRSAQ
      oKKiAlVVqays5PLly7zwwgv3Lp+qEv3wI5JdXXi+822sdzZLcR45ghYOk+jqwv+jv0d25eE8
      cgRb+y7kB/x7mc3mtc0B0uk0k5OTbNu2jfn5eXp7e3n22WcJh8MMDw+jKApNTU2LIVAwGGRu
      bo6ioiLq6upWJARaC4aGhjhx4gR/8Rd/gd1uX8w99u/fT19f3+K91tbWcu3aNdra2haTvmQy
      yec+97lVK5sQOiLuyzm79wbafD8iEUCyezCV7kAubcFUsh3JVbGkqYZer5dTp04Rj8f57ne/
      y9mzZ7FarbS3t7OwsMClS5fYtWsXp06doqGhgYKCArxeL8XFxczMzPCVr3zlE9fMzswQ/Kef
      Yqmvx/211z7VsTO3bxM/20H6xg2sW7fhPPoMli31n1nuxzoJFpqWS5pUFZFVEZqae51V0TNp
      RCqNyGbRI2GSV69R8NabmDwPH8MKIRgYGKC6uhq328358+eRJInGxkYKCwtJpVIEg0GmpqZQ
      FIWKigpMJhOhUAhZltm+ffuKlEH3jyIpFvTgLTRvP7p3ED3mRbK5/9PZy9uQnCUgPVxT9fj4
      OL/85S/567/+axRF4cSJE5jNZp566ikymQwmk4njx48DuRrL7/cv+oOmabz00kv/WXZVJfrR
      RyQudlLwnW9jaWp64LKJbJbUtevETp1GCwVxHj6E/cABlMJ7b2W1YQUghCDyq3dzDqkoiGwG
      kc4gsllEMomeyeQcOJNBaHecXFVz799xcgAUBcmsIClmJMWUe60oSBYLktWKbLGAyUT65k30
      RBLnkcM4jx7F5HKt0i/xAPeua6Cri/+EmoZsEpGJIdJRRCYO6cjiscjEIBVFqElQ04hsAtQ0
      2sIgsqcOU80BTKUtyGUtyO5KJGV1Rl16vV4sFgsej4ehoSFUVaWkpASLxUJvb0bx3Y8AABAi
      SURBVO9iCDQ/P09DQwNWq5VAIICiKFRW5lZRy05PE/zZ25hra8n/2mvIy2jbhzsPgHCY+IWL
      JC9dQikvJ++lF7HU1SH9kR9uaAH4/o+/w1RYiFJZkXNYiwXJYka22ZHMZiSbNVctmu46ec7R
      uXu8xBpHi8VIdJwjceEi5vo63F9+BVNh4YM/fRZ/SgFCgK6BmkSkwohUzmG1+WuIyByypybn
      vOkoIhOFTGLxNQiQFZDkXL+KyYxkdiJZnWB1I1mcSFYXktkBVlfu2JKHZHGCYgXFhmS2o4cn
      kd1VufMbHKGqRI+fIHHuPJ5vv4mtuXnlri0E6Zs3iR0/gbrgw3noSRyHDyG73RtbANrslVyr
      Q17pKpbsjj0tuzj9Tqgqic5OYh/9HnNNBXnPHcbkduSc8+6TNx1FpEI5p02F4c45hJZ7giMA
      CUmx5VpKrHlgy4dkGCF0lLonkSx5OQdW7Ln3ra6coyufPZf1cSI7PU3gJz/FUl9H/tdff+Ak
      djlooRCJS13Ez53DlJ+/kQWgE//xy7nY9O4TTJJz//7sWJJl4M5TWjLB3Sf2YhIn/eexJC++
      L935rFDT6HNXkcvacmGHlkGomTvXNCOEQnpoHKW0Guuu/ciO/JxT2/LBkpc7tjhzr2VTrgx3
      /jeGgNyfXKx/nMSFC3jeehPr9u1r9nsJTSM7Pb1xBQCgR2eR7lT5kBMF4s4iVX98rOvknrh3
      wg7ufuTu8R+d/8Q1BEJLo92+iNLwHJjtuRhZsSxOnRNCkJ24RfTECdTpafJefBHHwQNIy9yA
      YyU5c+YMp0+f5jvf+Q5Wq5ULFy6wZcsW9uzZQzqd5kc/+hFvvfUWHR0d1NTUcOXKFd566y3e
      f/999uzZsy57MAshUGfnCL79NuaqKvJf+yqy45PzddeCNe0JvnDhAtlslieffBJZlunv72fP
      nj2L3fq3bt1CVVWmpqbYsWMHiUSShoYKRkZGaGxszC2N8SnLY/w5S3mWmIru39cgSRKWLfUU
      /ZfvoS4sEP3wONHf/x7Hk0+S99yzyM71i7FnZ2fJZrM4nU7Gxsb44he/SGdnJwBdXV00NTUx
      OTnJSy+9RE9Pz2LTq6ZpZLPZNS+v0HVix08QP9uB5603se1YuVh/OZj+9m//9m/XwpCmaYyO
      jtLR0cHhw4c5d+4c5eXl3L59m/Lycnp6eujr6+Ppp58mm82iqir/8R//wa5du/jxj3/M4cOH
      H6ofYGpqih/+8Id8/PHHHD16lPfee4+enh5aWlqQZZmTJ09y8+ZNvF4v7777Lqqq0tvbi8fj
      4cc//jEHDx4EQHY6se1sw3n4EJnRUUL/8g6qz4dSVrYuQtB1nfr6erZt20YymeT69euYTCbc
      bjd+v5/u7m4qKyuZmJhAlmXsdjvDw8Ns3boVq9W6pgt+Zefm8P+/P0JSFIr+5/+KueLhVn5Y
      Cda0J/j27du88sorOByOxY6gzs5O4vE4nZ2dRKNRMpkMk5OTPP/889TX13PmzJkVqaZlWaa4
      uJjW1laEEBQWFlJbW0ssFiMWi5FMJslmsxw4cIB0Oo3H4yEcDjM6OvoJ+5IkIdntuL/8Cnmf
      +xypvj78/8+PUCrKcb9yDKWycs1i2V27dpFIJABoamqisLAQj8dDJpPh8OHD7Nu3D4vFQjAY
      xO12o+s6e/bsWfK4+YdBaBrxU6eJnTqN51vfwNrSsiFyI1VVH64GiPtuMTY4hkVK0H/hAgWV
      VQxf+Ii0sBP3TaOlwqQ0CzZbbnhDWVnZYttwJpPh/Pnz7Nu3j3Q6zbPPPovT6cTtdmO1Wiko
      KECWZfx+P42NjZSVlT1UDWCz2QiFQrS2tpKXl8eVK1eYmZmhoaFhcRzL3NwckiRRV1dHKpXC
      7XYzOTmJw+GgoaHhnteVFAVzdTXOp48gmc1Efvs+8bNnMXk8yxqfshzMZvPiCFGHw4Esy7kJ
      JJqG6c42sna7HVmWMZlMmM1mZFle9Z51IQSq10vg7/8BNI2i//o/Ya6q2hDOD7ldaJadBGei
      Cwx2nkF2VZBNxHBZVILxDI179jHRe5XA7X6q9r1E85496OuwR9h6IYQgOz5B5P330SNRnEef
      wXHgCSTL6jRt6pkMeiyGHoujhUPokShaOIwWDJK4cBHrjmZs27ejVJRjKi5GKSpakaHEn4XQ
      dOJnzxL7wx/wfOub2O4MBNxIxOPx5QlACEHvb/+JgC9ASX0TwlqCSIVJxwOUlBSR0U1kM2ns
      5W1sba7P9crd2cJmsyCEQPP7if7uQ9KDAziffRbn4UOfmScIXUekM+ipJCKZRAtH0EIh9FAI
      LRJBC4XRQkFEJotIp0AXyK48ZLcbU34+JrcLOd+DqaAAdWYG2eVCTyRQZ2ZQAwG0BV+uA9Hp
      RCkvRyktxVxRgamkGFNeHpLD8dBPaNXnJ/jTn2EqKMDzxjeR7wx+3Gg81KR4gMDMbdzFxdzq
      v0rVjr1Epm8irEVYFR1Hfj7RSIKiO1vZrPeEmPVEi0SInz5DovMS1q1bUcpKMXk8dxw6hBYO
      557i0RginbrTy21Ddjhyju3x5Jw7Px/5jpNLNhuy1brkmkUIkRNWNIrq9aLOzaPOzqL6fGih
      MAiByZOPUlqKUlaKUl6Oubwc2eVGspg/tXddaBqJ8+eJffwH3K++im3Xzjt9NBuXDd0P8Lih
      p9MEf/Y22fEJ7AeewOR2YfJ4kPPzkZ1O5Lw8ZIdjXZ1GT6fRQqGcOOa9OXHMe9GjEYSmI7vy
      MFdU5MRRUYlSUoxSUkLm9m0i7/0G2W7H88YbmNzrN5ZqKRgCWGPWYqriSrPoIrqeE8fcPKrX
      S3ZmNvf/1CTqzCxF3/9fcDx58JG6N0MAq8DdGWHFxcUcPHiQgYEBJEmiubmZWCxGZ2cnzc3N
      jIyMkEqlaG5uxmaz4XK5GB0dZefOnet9C0tCz2TITtzKTVHc4CHPn/NolfYRIhqNMjY2hq7r
      TE1NMTMzQzabxWw209bWxvXr13nmmWcwm814vV6uXr1Kf38/Y2Nj6130e5JMJjl+/Dgff/wx
      qqoSCATo7e3NJZJmM70BP+MTE6RSKc6cOUMsFmN8fHxxE+uNui6UIYBVIBaLEQgEeOWVVxaX
      7bDZbOi6jsViYWxsDKvVyvz8PMXFxShKbk5tKpVanC66EdE0jZ6eHoQQXLp0CVmWmZ6eRghB
      c3Mzvb29/P73v1+c3HPq1ClCoRAfffTRhhXA498wvw40NTWRTqcXm37vhn7hcJh0Ok0kEqGy
      spJQKMTOnTuZmpqirKxscUbYRkQIwfDwMK+99hqKomCz2SgsLCSdTiPLMhMTEyiKQiqVorKy
      kuvXr1NaWsqZM2dobW1d7+LfFyMHMHhgxsbGkGWZ+vp6rly5wvT0NE899RTJZJK+vj7MZjOV
      lZWMjo7S1taGyWRieHiYkpIS2traNmRybAjAYFOzMetbA4M1whCAwabGEIDBpsYQgMGmxhCA
      wabGEIDBpmbZHWFCzzLSeRLNVkJVpYfhawO0HHmWia6TuGp3Y1Ij2B1WhL0UjydvJctsYLBi
      LH+TvEwS3/QEc5O30VRwWTVC8Qxbdu/lVt81gpM3qNzzAs17926qGWEGjxbLCoGEENy+dp4F
      b4ja2hJkez7RtISWThAeH8CeZ6G4YTtmZyESuQnpG7EX0MBgmY9kgZAUZFSsxS04ff1U7X6e
      yPQAurWYQkXg8OQTDedWKzCc32CjYgyFMNjUGK1ABpsaQwAGmxpDAAabGkMABpsaQwAGm5p1
      FYAQgo6ODj744AOCwSDpdJqOjg4ymQwAk5OT9Pf3E41G+fDDD5menl6cYH3jxo11Wd7b4PFi
      Q9QAPT09QG7/3KamJrq6uhBC0N/fTzgc5vz58+i6TklJCadPnyYSifDBBx8sropsYLBc1n1s
      wuDgIC+99BIFBQVIkkRhYSHDw8NAbkVnt9tNY2MjTqeTEydOsHXrVk6ePEldXd06l9zgcWDd
      O8IikQijo6Ps3r2bubk5urq6OHjw4OJ+ApFIhKamJm7cuEFzczOapnHr1i22bNlCRUUFjnXa
      Wsfg8WDdBWBgsJ6sbA4gBFHfDMlEikQkiJpOkEwkl3kpwdtd/4O+6e4VLaKBwR+zojmAd/gy
      3oUAyYSElF7A5ilj64Fnl309f9xHPBNfuQIaGPwZKxoC9X38Hlu21XN7fAbv2CCNz36L+vrc
      RmhGCGSwEVnRGsAsZbg9GaawuISaHS0EfRFg/XcCNDC4H0YSbLCpWZN+AE3T0HV9LUwBub1z
      12KR2bWys5a21nIDj43w+62JAEwmE5IkrdkfcK1srZWdtbS1lgLYCL/fmg2F+LQbTQamCYVi
      K2LnfjcanRul98Sv8S8EARBaFu/kxEPZ+qw/Xja2wFBfH2oyzM2eLu4Xa/puj6B9RiD6oI4y
      O9iF3xf6xPno3DiBuUl83sCnfv/ufsOfhdCSDF48h6ZluHnxDNoyIukHvSehZZi/Nb7k6z+I
      rXUfCgEwOXAVT81WOk9foGH/M0z2d2MvrsdhlbCYweoqZKTrDA0HXmBmoBeTSaKyZR/JRJbK
      2uoHsjF6+QzkN6LHZ+js/JjGndvpP99JtP0IGf8Y1jw39pIG0qF5THqMeV+KijI30aSKYrLi
      dCgUNuzEan7wPGbm5hWCQZ3bmTliSSsTXR8TTlsodEkkNAtKNgyOEryDl7AMj7L/hc8v9ycE
      IBWcYmZ4gAKlgoWBDqIxjeotVQQiWWxmGVmN4ml++qFs3CU40c/M1DSufBMLviCekV4mhm9T
      Xl1BfkU1qaxCeWXZitiKTg8Q8iVRJTPZyALpyDyhcIptO1sZ7uum+eiruF22ZV173UeDJoJz
      xDMQmBojk84QmBqhalsLZCIE/WHmJ6dYmBgim0oTmh6hsHEvLgeMXB+kpKrygezomSjCUcnW
      nW0MdZ3D5nQwMzpO8xP7iM2PIRxVkI6RjU4TCYUIhRKo6RS+uTka2g8QmrxGUrNhUR7c+YXQ
      iMSzKIk5YrE0LpeTWDJLNh0jsBCixC2TtVVQ5JYpazuKzWZd7s+Ys6erXD3xHuFgEJH2kZE9
      mM0y3plZmtpaiMdiROMqHtfD2blzc8zPBijIl4iFwhSVlTA/PU82nSYb9TE+NEpJeenD2wEy
      ySgTQ+OQCqEl5knEY+iWYpxOOzcvncbudCJYfri2Zq1A90JoSW52dlLXvp/pG92YXaU482xY
      rQpCMjEzMkR+ZT0J7y1kZzFutwN3STk3z/yOir0vUlTofiA7mZiPeErBU+TmVt9ZcJRj0cPE
      UgpllSVYCyqI3LpGIBCjrL4e7+1xCqsakISMzaoz2NvH3hePIS/hd9azSQK+AKloGLvDhmKC
      +alpCqvqkBQH7jyZ4d4+SusaceS7SCUFhaVFy/wlIRsPEInrWIlza/AGKibq2vajpuLk5zuJ
      xjLomkZxZdWybdxFCIFv+hZaKo4tz4nJ6mR66AZ5haX4x29Q9+QX8LhXZozW5NVz2Mu3o0en
      CfqjlNbWoTg9pMIh0qFJUsJF486dy5bA/w9C3hsWhuJswwAAAABJRU5ErkJggg==
    </thumbnail>
    <thumbnail height='192' name='AdventureWorks' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAgAElEQVR4nOy9eXRc13ng+Xu1L6gF+06sBAgQBAnuEimSWmmJkik5bUmWFdvj8SRxTyce
      u0/S6fScSXK6k04ydntyptOJt3hiObYkL5LtlmUt3MSdIHYCxF7YCkBVAVVAFWqv9+78UWRJ
      NFcQIAkK9TsHB6/qvbrv1qv73fvde79FEkII0qRZpajudQXSpLmXaC4fCCFQFOVe1iVNmrtO
      SgASiQRf//rXqauru5f1SZPmrqL56Iv6+nq2b99+r+qSJs1dJz0HSLOqSQtAmlVNWgDSLAkh
      BPOTDqKR6L2uym2RFoA0N0WJB2l94zuc/tm/Iv/2uegcLb/6MQlxu01JMHTiDZzDI0uspaDr
      f34P96QLIUdoe/M7jF7sRQiZ7t/8EM/0zDU/pbnmu2nSpBA4Tv8ajTkTl8OJimSvv+AeYXpo
      ACUawFBQS2T6IvPCTGFFJa7eZtS2Umw2A86LHSgqI2Ubt6GEZhjrHcBiNzM/42NN0y5m+04x
      0tlOdliFyWIh6OzGWrYJgzbGeN8gFU1bGT1/jKyK9fimnKxp2IhnsJM5zyyF9duwZtqT1VTC
      eEYcFD2QwVTncQLzATJVWuYc7czOKdTlZxOanWRyoBuNOYc1DZuI+13pESDNjQm6BpkY81LR
      0IBaq0VC4B1qoeXtX4Baw/RAN4U1G5gdvoDP5SbiHaPv3Fl0mgTnfv4DYrEE4+ffwTUxjeti
      M86+Tubd04y0HCHo9+OfHkNtLcSamYlGCtPffBaNwYi7t5k57zzR2VEGzp+m5/AbxOIJ+t77
      Ec7hUUIeBxdPH0/VUw7OE1MMGPRxxoYmyM7PxmSz0X/qCDUPfYL50U6a3/oZAhj84A3cTjfT
      PWfTApDm+gg5Rvf7b5JdXot33IFaq0MocfqOv0P9/t+lsnEjCUVLfkUlcjyKzmik//jbVOw6
      wGTrIYTGhJDjSCoNeoOWqcFeavY+R3FVBVprEbacbJREjJLG3VRu3UNoehBT4VqMBhVT/T0U
      1mzANdCFpaiazQf/VwryTIwNjWEw6REC9KaMVF3DAR+qDDuujiMUbXqY8Nwc4emLCHs1+YWZ
      XDz6FnVPvET19n0YjTrUGhVTg71pAUhzfSZa3yesGInMjuIc6EWt1RHzTeAPKBgtBpwXzmIu
      XofJoCERjxFwXiSqLaS0ogTvlJPcyvXkrKmiatfTmFQLLES05BTmMd3fSU7VBqR4APf4JLa8
      fBRFZqr/AjmlVcyNduMPa8gpzGJqoI+qHY+hN+jwOR2Yc0rJK68lv2Yza7d+uGcV8fvQSAlc
      00FKKosILkQY7zjLul2PogQ9+Ofj6I06nO1HwFaFSTVHMJ6RFoA01ybmn2K4u5+tn/oSWw9+
      nqpNm9EbzWhMdswZWtp/8QqjF7oorG0EBAiBd3KS+j2PI6k05FfWMt7yHl2H30KWdHhH+8iu
      akQjJZiZmKBwbdLiwGA20vGrHxAOR5DjChNth+k7e5zsqg3I85PENFlk5WUBYC+uJuF30vbr
      V5kan8RgMqXqGwkukAjNU779UUTYSywaJqNsM/ZMCypzDgVlBZz70f+Dy7XA5qc+xayjj7ya
      RqTL1qDxeJxf//rX6Z3gNAD4PVPEo+F7XY07TnoVKM01seYW3usq3BXSKlCaVU1aANKsalIq
      kFqtBuDChQv3rDJp0txtpLRLZJrVTFoFSrOqSQtAmlVNWgDSrGrSApBmVZMWgDSrmrQApFnV
      pAUgzaomLQBpVjVpAUizqrnCGrSjoyNtCpFmVXGFALz++uv4/X7KysruVX3SpLmrXOUP8Pjj
      j7Nt27Z7UZc0ae466TnAXUcQCwVQ7pIJYiIaJhFP3J2b3YekBeA28I93cez738DnDRCZHeWD
      /+/rTI1N3fAzEZ+TsYs9APS++wqhyM3vM33hOJOO0ZtcJZjuPsm5n36Xll//lHjiyhD3k+3v
      43SM3fxmq5S0S+RtoMRj6EwZzIyPkIEHndmKnEiw4BphpKsVa0kdJeWFDLS2IOJBrKUNBEfP
      4hz7MDqZs+MYSkKm5sFHUCEzdO4YFVu203fiA9bufoSxjlbsWRaE0cjMUDtTw8PkVDeRmWli
      uPU05vxq1tQ3IEkK410tVO77DFarETUJHGePshAIU9q0J3W/wPQwoxc6yK5uIifXxlDzByga
      C7UP7kOtWr394Or95kvEUlhB0DXK3Oz8pagFCj1HfkVRwwO4Ow8x5/XhcToprKxi/GIXOaXV
      ZBRUkF9eAYCtpIbAeDuRKEiSipB7GPfwAD7XODMj/SwEowRnJliYn2e8/RQySYelwVNvYcgt
      w3XhOAvBCKCm+sFHGTn1CzoOvU1ClpE0eoj7cXS2XaqtzMWjb5G5Zi3Dp9/BN96HZ8qFWq1h
      tXuDpAXgNlHp7agTs0RlHVrNh49RkGxREqA1WsmwZwKgNZqQ43G4dD4juwCN5tIALElkFZcw
      0tFCeeMWRlpPkbNmbarM4o0PYcnQMtrVCorM/OQo1sJyJAQgE4tC3cMHIejC5xxiatSJJTsH
      oXwYyVMkYvgmx8ksqcCQWUz5hk3MDp1nIXB/BrVdLtIq0G0gCzU6nUCXV0Rck406MYukxKnY
      sovhU29jLWtCqxaYrRmEQxHMlgwkgx09AUa62tHb84kveDFmFhALepFiYMouRj3uw5Kbj1ar
      x2jWEZozg4gzP+XA6/ZQWLcdi1mNo+0swUQGiXCAkKxiwT3CaMdpLGWNmCx2jNo4C4EoRlMG
      kk6NJBTKt+5hrLsTrS2fiH8W18AFTPlrMRlXdx94hUvkf/pP/4kdO3akl0FvQmRhno+LJ6nO
      aEat0d7ratwzrhoBpqamcDgc96Iuae4JvntdgXvKFQKg1Wo5cODAvapLmjR3nSsEQJIkSkpK
      7lVd0lwHRY4x0dOGOb+K7LycS+8K5lxT2POLUteFfVOMDQxSUr8Fc0YybmYsHEJrMCFJ96Di
      9wHpSfCKRzB8/hi24mpGOs7gMesw2fPwTgygsRbh7G3BVlKL3z2FSETILSnGcaEdTdyHPruY
      4XPHqHngMTwj/dTuOYDZqLvXX2hFsbqXAO4HRAKvx0vEN4nZYkSbWY53Ygiz1U4i6CEUkZkd
      7cVWWEY4GGJu2olaRAjMeUnEouTW7sAz2AqSivQwcDXpEWClI2moamxCVmeQXapBm5FDIEMi
      HJMoqbPhnpzGnpOLyZZJ3O8ms7QGKTZPILeInKIiZqfdGNfkEEloMOlX72rP9bhrAiDLcir8
      YprFIJG9puaKd7JKP9wkK7Plpo7LGy+HtrdjzU8eFVVa73QF72vSI0CaVYEQgoGBARKJBDU1
      NUiSRDAYTM8B0qwe+vr6ePXVV5Flmb6+Ppqbm9MCkGb14PP5eOGFF9Dr9dTU1KDX69MCkGZ1
      IEkSn/zkJ9HpksvAPT09uN3u9BwgzerBbrdjtycTazc2NtLY2JgeAdKsbtIjQJpViRCC6YVA
      WgDSrDyGh4c5efIke/fuxW63c+zYMZ555hlisRhnzpxhYWEBi8VCOBzGbrdTWFiIx+MhIyOD
      mpqaG5atCEGfx8155xg6tSYtAGlWHpOTk/T397N7927MZjMZGRkA6HQ6GhsbGR0dZXR0NCUA
      Y2NjBIPBG/poxOQErc4JOqYnKbHaObBuPZkG4+0KgCC8EMBgtrAwO40uIwsRDyGjxaBXsRAI
      YzIZQK0hEYtjvPQF0qS5FYqLi9m7dy9lZWXMz88zMDDAunXrMJlMXLhwgZ07dzI0NIRKpSKR
      SBCNRrFar73jPR8Jc3pshLG5OdbnF/C5pm0YtR+ahFzhEfaXf/mX/Pmf//lNKzjn7Ke7rZv1
      jdV0nWtJJlWWBGH/AiazgaBvHluujQQGKjY9gMViTptCpFkU8XgcjUZDIpEgHA6j1+tT7Uej
      0RCLxVAUBa1WixAClUqFEAK1Wo0QAqd/nhOjw8Rkma3FpdTm5F0z+sVtjQAGo56CqvW4J0Yw
      GrQoiSgltQ2Mtp8ko2gjknqYSGAGldWeavwfFxfCNHcH7aVeWqvVpo4/yuX1/I/iWggwGwpy
      ctRBltHE7rJKiq02pBtYwS5aABLhOS6eb8GUV4FOr8NetoEsu5Zp5zTrH9rPQFsb5Y2biMUE
      0UDS3U6tViPL8k1KTpPm9ojLMq2TE7zZ08XO0jKe39CEVa+/YcO/zKIFQGO00/TEp656P6s0
      +b9x3/4P3ywoWGzxadLcMglFoWPKybnxMdbl5vFHD+4hP8OCahF+D+lVoDR3hGAwyIULF7BY
      LNTX1zM/P4/RaESn0xGJRBgaGsJms+F0OqmsrEQIQXZ2NtPT0xQXF9+wbFlR6HZNc2rMQU1O
      Hl/Ysv2Kie1iSO8Ep7kjJBIJRkdHaW1tRVEUjh49isfjAeDdd9/F7/eTl5eHwWBgamqK999/
      H4fDwYkTJ65bphCCbtc0/9xyFk9ogc9v3s4jVWtvu/FDWgBWFKFQiNOnT9PV1QXA/Pw80Wgy
      cls0GqWzszO5gzk9TTgcZnp6GlmWmZiYuJfVviayLOP1ejlw4AAqlYq6urrUOUVRMBgMeL1e
      5ubmKCwsxGq14nA4yMvLu6osIQQX3S7+ueUsk4F5Xtq4hUcql9bwL5MWgBWELMtMTU3R0tKC
      oiicOHGC6elpINlrBgIBQqEQv/zlL3E6nbzzzjuMj49z7Nixe1zzq8nMzGT//v1IkkQikcDh
      cDA2Nobb7WbDhg3Mz89jMpmora3FZDKxZcsWCgoKrkjOIoSgf8bN986fZWzexwuNTTxWVYNZ
      p7ulCe6tkJ4DrCAURWF6epoXXngBlUpFfX39FedsNhtvvvkmkNwtzcrKore3l6KiousVec+Q
      JImKiorU6/37P1wcycvLo6qqCiC1gWU2myksLASSDX9wdoYPRoYosFh5vnETFt2treoslrQA
      rCCsVmtKZZBlmcHBQYxGI0ajkY0bN+JwOHj++eeZnZ1Fq9USi8XweDxYLJZ7XfVl48jwAN2u
      aUptdn5n/UZsBsMdafiXSQvACkKSpCtUgMcff/yK8+Xl5QAUfGR5+XKv+XHAs7DABdc0D5VX
      srGg6I42/Muk5wD3AUII3u6/SJ/Hfa+rcseYDQV5o6eTlzZuZlNh8V1p/JAWgPuCmCzT7Zrm
      7PgoysfQpGQ+Eub1rnaeqWsg22S+q/e+LRVIiUeYGBqktKaO0fYTaDPLIOQiocvGZhJMTXop
      LisCtY54QkV2fu7NC01zXY45Bnmkci2BWITDQwM8Vn1jm/fF4PF4OHr0KGVlZTQ1NfGTn/yE
      559/Ho1Gw+HDh8nIyECtVhMIBCguLsbr9VJQUIDD4WDfvn1Lvn8wFuUnXe08s249hZa7H8Po
      tkaAhRknM9PTjLe+z9joNCLkJq7LZKb/PBNjbuSAh/5zhxno7MKem5M2hlsCM8EFnP55NhUV
      81B5Fe6FAL0e17KV7/F4OH/+PLIso9FoWLNmTeq30mq1zMzMoNVqWVhYwOfzMTc3h9PpXBYV
      JZpI8KOOVh6uXEuJzb7k8m6HRQuAIkfxOofQZeQyMzNHdqaJyZEhQp4JdEY9sWgIlSkDU2YR
      apWU/FOr75pO93FCCMG7A308Xl2LSpJQSRLPrW/kmGMIbyi0LPcoKytj3759bN68mXA4THt7
      O729vbjdbrRaLfPz86kVp3A4TGZmJpFIZMm/Z0yWea2zjYfKK6nKzrn5B+4Qi/YHkGMhPBPj
      6DIyMeol4kKHUSexEIphy7Tic7mw5eQQT0jI0SAZmdnJz6X9ARZNr8dFr9vNs+s3XPH+dMDP
      r3q7+XzTNnSa+28hL6HIvNbZTn1ePk1F9zYc/6KfnlpnoqCyNvXaeOl/ZjIcPdlFSbNQjQ4w
      GZZav1VLXJY55hjis5u2XHWuwGJla3Epb/X18Gz9hjs2ug7OeijIsJKh1y9bmbKi8IueC1Rn
      59zzxg/pVaAViRCCU2MOGguKyNBdu/FtKixGrVLR7By/I3WYDQV5pe08P2hrZsg7syxzOCEE
      b/X1kG0ys6O07OYfuAukBWAFMh+J0OfxsL1kzXWvkSSJp2rr6XFNMz63fHm+hBAMzc7wWmcb
      /3bnbj67aQvdrmm+e/4MF92u2xYEIQSHhvoxaLTsrahatvoulbQArDCSE99eHl9bc9MM7hqV
      imfrN/Cr3m4WYkvP9yuEoMU5zvGRYT6/eTv5GRZsBiOfrGvg+Q2bGJj18K1zp+l2TS9KEIQQ
      HHUMEorHeay6ZkUtiKQFYIXh8M2CJFFxafHgZtiNRvbXrOMXPReWpKYoQvCb/l7G5+f47KYt
      mH/L5/ayIHxmYxMO3yzfOneKHtf0TTfmhBCcd47jDYU4UFu/KG+tu0FaAFYQCUXm0NAA+9fW
      3vzij1CVlUNFZhZ9M7dnKhFNJHi1oxWTTsuz9RvQ3mC1zmYw8vS69by8aSsjc16+23yGC9NT
      yIpy1bVCCNqmnPTPeDhYv+GmI9q9YOXVaJUihKB5Ypx1uXnYDMabf+C32FxcyrHhIXzhxe0P
      zEfC/KCtmcbCIvZWVN+yepKh1/NUbT0vb9qCO7jA91vO0eocTwlC0pbfw4XpKT7dsBHNCmz8
      kBaAFcNCLErn1CQPrCm/rc8bNBqeqVvPG92dxBKJW/qMc36OVzvbeLKmjob827MqNel0PFK1
      lpebthCIRfn2udOcGRuhY3qSk6MOnt+waUXvVaQFYAUghOD9wX72VVajUd3+ZmGR1cbmS/sD
      N5oPJH1rp3ir7yIvbGhaFjOE5OpONV/cuoO5SJifdrXzyboGDMvgtngnWbmiuYqYmJ8jHI9T
      m3u1P+xi2VhQxKjPy3nnONuusYyqCMGJkWEm/HN8fvM29MvcO+s1GvavXcf2kjKyTKZlLftO
      cFvfPhFdYHxgkPKGjcwOdxHXZpHwjZLQZmMzKUxN+iguT1qDJoSOnLQ16HVRhOCdgV4+tX7j
      spQnSRIH1tXzStt5CixWSj/Su8dlmV/39aBTa3hhQ9OSJ6U9PT1MTk7S1NSE3W7H6XSyZs0a
      JElCHY/jdruZnp5GURTsdjvFxcVMTU2Rk5ODaYUIx209gfDcDPNeH4FpB6MXu/FPOxDmArzD
      bUxN+xERH4PnjzHU00tm2hr0hrRNTlBmz1rW3lKjUvPc+kZ+3ddDMBYDIBiL8aOOFoqsNj5R
      s27ZVmTa2toAmJmZob29HUj6L586dYqhoSE6Ojqw2+309fUxODjIBx98wMzMzLLcezlY9Aig
      xMO4Bi+g0ucz3NGM1+1G0qiJRSIYLRkEAz4yrFZMehOxmECtkoB0aMRrEYrHaJ4Y43/ZsmPZ
      y7YbjDxaVcMb3Z08Wl3DL3q6eKy6lupltLwcHh5m27ZtZGcn9ywuO7hPTk4yPz+fSkr33nvv
      kZuby9jYGJmZmct2/+XgNlQgCXt5A3lGK9bMrYSDC2g1EuFIggyLmXmvF4vdRkKWkGPh5a/x
      xwQhBEeGBthdVrnsevhlqrNz6HZN8e1zp/jyjl3kZSyv8/yBAwfo6+tDCMH4+DgLCwtMTExg
      sVj4nd/5HTweD21tbZSWlpKXl0dmZiY2mw2DYeUYSS76yau0BnKKy1OvjZcequXSd7LnJlOU
      q7WAYfmsCD9uuBYWmAkFObBu/R29z77KahryC5e98UNyvrFu3ToA1qxZw5o1V066i4qKrgjZ
      UrACY8WmV4HuAUkn9x4O1Nbf/OJbJBwOE4lEsFgsqNVq4vE4Op0Oi04P0RjxeJxgMIharUar
      1aLX64lGo+hvMYryx5X0PsA9oGt6ioIM67L2yh0dHfzZn/0Zs7OzjI2N8dZbbwHJcIt9fX0c
      PXqUsbEx3n77bV577TW8Xi/f+ta3CAQCy1aH+5H0CHCXiSYSnBgd5otbdy5ruUajkZdeeon8
      /KQK6nA4gGTEtVAohNVqJSMjg507d+J0OmltbSU7O3tV9/6QFoC7inLJLHhnaTmGZZ741tTU
      0N/fn7TnHxrC6XTidDqT91UUjEYj4XCY+vp6YrEY0WiUrVu3olnBZgp3g9X97e8yLc5xmifG
      +LN9j9/84kVyOXwiQHV1NdXV1alzvx1v/7fPr2bSc4C7xHwkzLnxMT7dsGnF2cSvZtIjwF0g
      FI/xamcbn2poxCzA7XZjs9lS2VKMRiNCCPx+PwaDgcQla05JkjAYDESjUbRa7apXV+4E6Sd6
      h0nIMj/uaOXRqhoKLVYOHTrEG2+8wde+9jXMZjNHjhzhxRdfJJFIMDg4iNvtJhKJIIRAq9Wy
      bt06Wlpa2Lp1a1ptuQOkVaA7iKwo/LS7ky1FpVRlJc0FJEnik5/8JJWVleTn56c2h9RqNcFg
      kA0bNiCEQJIk8vLymJycXDGGYx9HbmsEWPCM0dvRQ+OOLfSc+QBD1hrkwBQxyYLNDK7JWUqr
      y5BRY8wsIa9w6Wa+9xviko9tkcXKxsIPQ33v2rUrlerI6XQyOTnJxMQEiqLg8XjIzc3Fbk9a
      cGZnZ+Pz+SgtLcVsvrtBY1cLtyUAWoOejMwcFAXM9lzk8Az59TsYOfsOSs4OLPYoU33tqHNq
      aarPW3XWoEIITow6EAgeKq+8Yq1dr9ezbds2AEpKSnjppZdS5y6bEnw0n1aaO8uiVaBEeI7O
      998iFArR8vbrhCICg1GLo+0shWvX4xpow5pXQFb1NnSSggSrLjZox9Qkzvk5nqypW1Xf+37k
      hiNAZG6aqGTGYjGjumQ/rjHa2fbcFy9dsSd1bfml/8Xrrg7lt5oY9s7QOjnB7zZtXZFRENJc
      yQ0FwDPUwehkEK1Ww45PfPJu1em+ZSrg573Bfn5309YbhhZJs3K4YReVX92IxWalvGF19+q3
      wlw4zBvdnTy/YROm3woqlWblckMBUBIR4nGBUOJ3qz6LJh6PMzY2xtTUFACRSIR4PFnfWCzG
      7Oxs6ppQKITf70cIwfz8/LLVIRSP8XpXG8/WbyDTmF6yvJ+4oQCoDTasVj1B/8o1mQ2Hw7z1
      1lv86Ec/QgjB4cOHGR0dBeDkyZO0tbVx6tQpenp6CAQCvPbaa7hcLn7yk58sy/3jlxI9PFJV
      Q5HVtixlprl73HAOIKm1mKw2pkZHqWrYcKNL7xkqlQq9Xs8zzzyDJEk0NjYSiUQA0Ol0FBcX
      E41GicVi9PT0kJ2dTWdn5xVJnG8XWVH4eXcnmwqLUxtdae4vbiwAKjVBnwetceVuwpjNZnbv
      3o3RaERRFHp6elCr1VitVrKzsxkYGKC2tpbR0VFqa2sRQjA1NbVk52xxKZxJocV6V9N6plle
      bigAsXknszN+ckqW3lveKSRJoqbmw6yJTzzxROo4Ly/vCp/Vy1xOOH27JDe6hlHE1Rtdae4v
      bigA+swSbNZB5lxOoOkuVenucHhoAE9wgVxzBhqVCklKxttXSSpUkpQK5nr5PbVKunQOej1u
      AtEon96wKd3473NuPALMTeKbW6Cwct3dqs9dYSEapdfjYktxKZlGE7KiIBAkFCV5LJKTWwGE
      43EUIZCFgqwIFKEwNufj2foNKzbicZpb56a2QHI8geaqAKeCWDiM1mAkEphDY7QgEhEU1Gg1
      KiLhKHqDDlRq5HgCvXHx4b7vFLKi8MuLF3hi7brbDhL1WPXi4venWbnceBKsNZJhtVwlAHPO
      Abrbulm3voqJEQeKokOlUgiHomSYdczPzpFdmEMCLaX121aMAAghOOYYoshqW1KEtFgsxokT
      J5ifn+fgwYO0tLSQk5NDRUUFQ0ND9Pf309TURGtrK1u2bOH8+fM8+eST/OIXv+C5555bxm+U
      ZqlcdwwXiszk0CAWuwW/98pYjkaTkfyKOibHJigsLiQc8FFc24BaCWIqXIc1N5eg101C0WOz
      W1eMNejg7AzTAT8PlVcuqRxZlhkaGsLj8QDJCbbf7wdgenqabdu20dXVhSRJBINBxsfHuXjx
      YsrTK83K4fojgKTCYDIw1dWCvWZv6m05FmLWM4ctK5ecrHrQZrBpbxnBUJyNjz2Ld9pF0eYd
      xBMSicgCkLQGvdexQUfnfJwdH+X5DZuWbKSm0+morKykpqYGlUpFT08PHo+HgoICzGYzJ0+e
      pKGhAYfDQSAQYPv27Vy8ePGqyGlp7j03zRQfD/uZGHJQ0bC08N33MlN8MBblX9tbeH5DE/YV
      oo6lWRnccA4QnXMyMe7Glld8o8tWNLKi8GbPBR6uXJtu/Gmu4oa6gEqtYazzHINtZ5Z8I1mW
      6e3tpb+/H0VR8Pl8+HzJBM/RaJTh4eFUlOFgMJjSr91u95KSM38wMkSx1cbanHSSjjRXc0MB
      EIqM2Z5FVuHSdVdZlunq6uJnP/sZiqJw7tw5mpubURSFEydOMDk5ydGjR2lpaSEUCvHjH/8Y
      v9/Pj3/849u+55B3hkn/0ie9aT6+3HgE0BkQMT8TQ4NLvpEkSQQCAV544QVUKhUWiwWr1ZoK
      2zc3N4fH46GmpoYLFy5QVlbG4cOHb9s/1h+JcGhogIP1DWnPrDTX5bpzAKHIODpbySyto6Z8
      6Rs/Wq2WT3ziEyiKgkqlQqvVIkkSPp+PrKwsgsEgW7dupbOzk5qaGiRJwul0kpu7eNVFVhTe
      6OnkibW1ZOjSOQrSXJ/rCoCkUlPe0ETXiaPEFRWZOUs39/1osoTLkREAcnNzU0ZrH02iUFZW
      tuh7CCE4PDxwKXt62kQ5zY25sUdY1E8oJhHyue5oJX7c0UrzxBjKMmyW9c24mQ0GebBs5Vqw
      plk53GAnOMHEmIf6xnVYckvuaCXK7JlM+v18+9wpTo4OpzIbLhZfOMQxxxCfrG9IB6BNc0vc
      cB8g4BljaihERs6dze10ubeOxONccE3xamcrNr2BHaVllNjst2RynFBk3uju5Ol16zFp007p
      aW6NG8wBNKzf9QRxRcV4T9s1r1ESURwdZ7GX1pOYnyChzcRqFLhccxQU5YFGRywOWbm3Znhm
      0GrZWrKGzcWlTMzPcW5ijPcG+2koKGRDfiHGq6xSkwgheKe/j/X5hRSn/XLTLJxoS7gAACAA
      SURBVIIbzgG0JiumjAxqtz90zfO+kU6cjlGmes+zEFcz1X2akcFRgq5R+psP09/ejiUzE0VR
      FrWZpZIk1tgz+Z2GjbzY2ISiKPyw/TxvdHcyFfBfVdYF1zSheIztJfe3rc3MzAx/9Vd/xTe+
      8Q3i8ThvvPEGhw8fBqCrq4vvfOc7+P1+vvOd7zA8PMwPf/hDPB4P3//+9+9xze9flhAeXWG0
      fxir1UQssoAuNI9Or0VBQmO2oE5oSaBGq1Gnoh3fDiadjp1rytleWsaIz8vxkWEC0Qibi0po
      yC9kLhLm9JiDz2/eft97Z2k0GoqLi9m6dSvhcJjq6uqUlWl9fT1zc3O0tbUhhMDtdqPX6+nt
      7aW0tPQe1/z+ZQkCIFG5aRtCZ8Wkl5j3R8jeuAX3+AQ5RYXEEhKJcDKcynI0TJUkUZmVTWVW
      NguxKOcnxvlO82lmgkG+uHXHHUs2fTex2WzU1dVRXFyM2WxmfHyczMxM3G430WiU2tparFYr
      drs9tZE4MTGRytR+vxCNRvnWt77F+Pg4f/3Xf817772HLMscOHCAkZERzpw5Q2NjI83Nzbz8
      8st8+9vf5ktf+hL/8A//wFe/+tVl7eiWJAD2og9NDPIuZfwsrFwLgBYg485Ek8jQ6dlXWc22
      klLOOyc+Nnq/JEns2LEj9fqpp5665nWXc4FBcmS431CpVOTk5LB161ZUKhWZmZloNBoSiQSV
      lZVMTExQX1+fCl6Wn5/PyZMnqaxcfpOW+7rbNOv07K2outfVuOMIIYhf+Bnq3FrUBSszPtNi
      0Gg0bNq0CbM5GXQ5Go0SDAaZn59Hp9NRUFDAwsICAwMDZGdns27dOgYHB++IqndfCMA777zD
      z3/+c77yla+kwhxu3LiR3Nxczpw5g8fjobS0lK6uLoqLi1m3bh2dnZ3U1dWldpjvZ+SRE8TO
      fx9Jn4Fh35+iLrq/I3RIknTFyLVv374rzttsyRH9C1/4Quq9hoaGm5YbiUQ4fvw4gUCA5557
      jsOHD2M2m9m5cydDQ0OMj4+zYcMGWltbaWxs5OzZs/eHABiNRvbv38/atWtpbm7mqaeeorm5
      mTVr1lBUVMS6deuwWq1otVpkWWZ6ehqNRrMi3DCXghAK8fZ/RZ5sx/yZHyPiQaJH/xbV0BF0
      O7+MpP34+TckHMdBktCU7170Z2OxGC0tLRQVFaEoCmazOeWJ2NbWlsrJlpWVhdvtZmZm5v7I
      EdbU1ER+fj4ajYa8vDzefvttysvL8Xq9BINBCgoKGBwcZNOmTVgsyclIdXU1xvvYAUbIcaJH
      /wYl4Mbwif+KZLCishRiOPANVNlVhN/8MvJk+72u5rIhIvNEz/wj0ePfIHrmnwi9/gXCv/5j
      Yu0/Qp6+gJBvHqDZbDbT0NDAU089hUqlYnJyEr/fz+zsLLm5uahUKux2O4lEgkAgQG1t7c1d
      IpeLe+kSeb+hhGaJvPt/oa1+FM3656656qEEpoke+1tU9jXodvzBbY8GHo+H06dPU1VVRV1d
      HR988AG1tbUUFhYyNDSEz+dj7dq1DA8PU1JSgt/vJzs7O6VOLBURmU828rEzaBtfQLP2cVBp
      QCiIoBt5sgN5sg3F5wBFQV3QgLpoE6r8BlTmqzdYhRAoioJarSaRSKSenUqlIh6Po9VqU/+F
      EPeHCrSakGcGiR7+z+ge/EM0JVuve53KUoDhwDdI9PyS8JtfRr/7q6gLb89v2+/3Mz09jU6n
      o7a2lqGhIQoLCzl+/DjV1dUIIfD5fBiNRkZHR1OpXHt6ejhz5gwHDx7E6/XS39/Pnj17MBqN
      HD16lOrqahwORyofclNTE+fPn2fjxo3kWfXJhj/Zhnb9s+i2/29Iqo80R0mNZClEVVuItvYT
      SXU2HkL29CE7W4l3v4kI+5CsxaiLNqEu2gRyHFVuLSoEIhZBFQ9DPIyQYyjxMKp4CFmOIsXC
      JOIhRCKaFoCVghAiOdlt/h6G/X+FynZzA0RJUqFd/yzqNQ8QPfHf0PhG0dQ9jSTdumYbDAbx
      +/289NJLTExMMD8/n8qvkJubSyKRwGKxpFJk6fV6YrEYKpWKvr4+hoeHgaTr6p49e3A4HNhs
      NmZmZtDr9WRkZBCLxTAYDAwODjLrchI4cQKLcKLd+CK6Hb93ZcO/7neVQGdGU7wZTfHmpEAI
      BeF3Ik+2E+94jfiFn6LKq0PSWUCjQ9IYQWNA0hpAY0yOkho9ktaEpDMhGWxpFWglIIQg3voK
      8lQHhsf/AklvWXwZiRixM/8DZW4U/d7/gMpy6waMs7OzZGRkoNPp6OrqYu3atQQCASRJwuv1
      otFoGBsbo7a2lszMTILBIGq1Go/Hw+zsLDt37qS5uRmXy0VDQwM6nY7Tp09jt9tT1+bZjIye
      +TlZ0REqH/0S5TueuaWGvxiUBTeSOWdRHQDiI/zFX/yFuFMkEok7VvbdJhqNikOHDolXX31V
      yLIsDh8+LD744AOhKIpob28Xhw4dEh6PR7zzzjvC5XKJ119/XciyLF555ZWrylISMRF+/y9F
      5INvCEVe2jNSFEUkJttF8LXPiVj3m0JR5KWVFwsKRVFueI0sy0JRlOS9E4krjmVZFnG/S4Q+
      +G9i4We/J2KDR4QiJ25a5t1kSSIoRwP0nvkAS2E1Yc8QcU0WNpPC9KSX0qo1CEmD2pxDXsHH
      K1F2PB7nzJkz5Ofnp3yagdTkKxwOc+rUKUwmEy6XC5/PR0dHx1XJrpWwj8hv/iOatU+gvc5k
      dzFIkoS6cCPG575FrPk7RP7n19A//B9RZeRfda0QCsSCiLAPEfIiInMoIS8iNJt8HfYij59F
      shShspUi6UygMyPpMpLqg84COhOS3oKkvXzODLoM0OgQ7ovEBw+jzPSh2/w51Lu/srie+Q6j
      RKOEm88vbQ4wePYw/rkgat0YmVVbmDj/LuE1mzEZ5xm/cA5VVjWbH6pbMaERlwuj0cjOnTtZ
      t24darU6te/g9XrRarUkEgmys7OZm5sjFAqxfv16WlpaqK6uTpUhzwwQee/PMez942Xf2JK0
      BnQP/DsUVzfhX30VSWtAshQiQrOQiIAAVCrQGlGZspGMWUjGTCRTFqq8+tSx8E8iWYtAUkE8
      hIguIGLBS8cBRMiL4hv98HUslDwOe5Fd3RgOfB39Q1+9quFHIhG++c1vMjExwTe/+U3effdd
      ZFnmmWeeoaWlBZ/Px4MPPsi7775LdXU1hw8f5stf/jJ///d/zx//8R8vqaOIu1wsvPc+sYFB
      jNu2Lk0AIqEgepOFRGSBkdaT5JeUMzbQTnF1NRpzJqH5+WSvtAJCIy4nKpWKhx9+GEj2ugcP
      Hkydy8nJuSJhx2UefPBB4PJk9zix8/+M8cDXUVmLrrp2OZAkCXVBA4ZH/k/kqQ40FXuQzNmg
      1l9xzQ1ZxDziowghEJH55CTzGvdQq9XU1NTw5JNPotFoyM7OTtkCFRcX43K5OHToEFNTUxgM
      BkpLS1PLs7dVH0Uh1NrK1//Dn3JxxsM/fve7tOdkE1hYWJoANO7/zFXvlV1nJS4ej3Pq1Ck0
      Gg07duxgamoKSZIoKirC6/XS2dnJ9u3b6e/vp6GhgYmJCcrKynA4HHR3d/P666/zJ3/yJyQS
      CcbHx2lsbKS0tJRXXnmF3bt3Mzs7SyKRYHZ2lrq6Ojo6Oli/fv1th1W5GR/9YW+lRxJCIAJT
      xDteRZkfx3jwfyTVilvk8gh6vXtdb4RV59ejyrv2M7hZmbeLJElIRvt1z2s0GjZs2IBer0eS
      JOLxOOFwGL/fTygUQq1Ws23bNnQ6HfPz84TDYYaGhhYVJEEIgezzETx6lHBbB4YNDTS8/Fme
      XL8e+9atWE6fJr+gYGkCsNgHJ8tyqqH39vYiSRL5+fm0trZisVgYHh7GZrNx+vRpOjo6eOml
      l3j77bfZuHEjL730Eg0NDZw+fZoDBw7Q3NxMaWkplZWVDAwMsG/fPjo7OykoKMDlcmEwGFJB
      t2ZnZzlw4AD9/f0sLCykIlKcO3eOtWvX0traSn19PQ6HgwcffJBjx46xd+/eJTcMIQRE5kmM
      niThOI7iHQYlgenlny5KHxZC4P7Pf4W+Zi22T/8bpGusph07dozvfe97/O3f/i3Dw8O4XC4e
      e+wxYrEYZ8+eTZkXWywWJiYmeO655/iXf/kXvvjFL2K1Wpf0PRfLb6e12rNnT+o4JyfnClXx
      ci63W+nIhBAgBNGeHgLvvoeIx8l49FGsBw8iqdVsHR0lGo0iSRIqlQqXy3V39wF6eno4ePAg
      kiRhMBhQqVQIIXj44YcZGxvj9OnTPPPMMzidTioqKnj//fepra1l8+bNtLcnt/0LCwt5++23
      2bFjB263m3g8TmZmJh0dHYyOjtLY2Mjc3Bw1NTXodDpmZ2cZHBxMbebodDpisRhzc3M4HA5i
      sRg1NTW0t7czMzNDUVERvb297NmzZ9ECIC6tTctTHchjZ5DHTiOZslGXbEW/8w+Q7GWLL1NR
      mHvlh2jy80Gtxv1f/grrwU9i2LjxirKys7P50pe+RFFREQ6Hg+3btzM3N8fs7CySJNHe3k5N
      TQ1+v5+6ujra2trIysq6YX3u1Ahx3XsJQej0GZSFBTR5uQhFAVkGIRDxZGh5IcugKKAoyWMh
      EInkvkW4rQMScXTV1dhffAFt0ZXq5UdHkJ07dwJ30RpUq9Xy4osvEg6HUalUZGRkAODz+fD7
      /TgcDp5++mnOnDnDli1biEQiuFwusrKyMBqNKR26oqLiihSnhYWFV32pyyiKwq9+9StefPFF
      NBoN4XCYWCyWCroVCoUwmUwMDg6iVquprKzkyJEji7I7F0IgFlwkRk8hj51BBKZQFWxAU/4Q
      um1fRNIYbrmsYDBIc3MzVquVpqYmBgcGUP3mHXIrKgg2bWJkZIStjz3Kyb/+r1T8/E2kpw+w
      ZnMTY+PjVFVVpRYbrFYrnZ2d7NixA6PRyNTUFOXl5UxOTpKZmYnVaiUQCLB58+Zr7s0IIVAC
      Abz//H10paUYmzahLS0FjWbZhEEIgYjFiA07iPb1ERsYQAmFkQwG1Jl2RCwGKlVytJNA0lzy
      B9eok+/p9cnIHyoJLjlD6Sv9ZDz5CTSLyAC6IjbCrtXTpKp1qWdACEQ0ihIOowRDiEiEhHeW
      6IVuMr/weaTrOMwripIyhpqZmSEUCpGRkYHNZiMSiaTi+1/OLxyJRNDpdFit1uvUR0Z2toKQ
      SYycRPH0IWmNqCv3oindicp2+5G0g8EgR48exefz8amDBzn01a8h1dXx9L//Gm+++SbV1dUE
      AgHm5+epKy3lra9/g21ZWfSWlPDy//GVpatsskyko5PgyZMogQV0FeWoMzNJuN3ExycQioza
      akVbXIyuogJNSTGanJxkQ73JvYUQKHNzRAeHkg1+ZARJktBVVqJfV4uuqgr1LahiiqIgyzIq
      lSq1uKK6dP/LHYBa/aEb7kf/q64RInNFCIDn//46ksmEpFajRCKIcAQRj4MESfEHSaVC0ulR
      mYxIJhMqgwFJpyPScxGV0UDm5z+X/DFuERENJAVLZwY5hoj6ESEfIuxNro1fXh+PBhCROUTE
      D3IcUJCnu9A2/Bs0NU+gLmhEUl9b+BbL/Pw8r7zyCp957jn83/ku7vXriRcWsHv3bmRZpqWl
      hYGBAZ544gnOnz+fDDbg9yOfa2ZXRQWZn/8catvivOMURSEy4SR07Bjy0BCa2lqse/eiLSpE
      lmUURUmt0Gg0GhKzs8QnJoiNjSM7nSQ8MyAE2pJitMXFaMvWoC0pQZ6bR1KpiPZeJDo4RGLa
      hcqSgb6mBn1tDbqyMiTd4sPX9Pb28k//9E9s3ryZp59+muPHj5OTk8OuXbt44403KCkpYcOG
      DXzzm9/k2WefxeFwUFpayuDg4DXTU60IWyDj5s2oc3PQ5BegMhqQDAZUt/BwhBBYn3uW2NAw
      s//0LYwbN2L5xBOgkpINN+xDBD2XGvNs8i88hwh5kd3dgIQqswxU2uSGjunSergxC8lSgCq3
      DslgRTLakfQ2JE2yTiIWTG76LDM2m41PPvwwM//vf2fNZz+LOxSkIDsbt9uNx+MhHA7z9NNP
      09zcTFNTE5IkMTMzQ8aDD2JVqZj97/+ArqYGy5OfQH1Jxbzec1P8fkLnmul44w1+0NbGrs+8
      yIH//d9y4tQpchzD7Coq5Be/+AVr1qwhMzOTCxcupOx6iouLcQiFg1/+g2R58TgJl4vYxASR
      jk7mf/Zzor19mB/ajb6mBuszz6ApyEdahiDFRqORBx54gMcffxy3283u3bvp7e0FoKamhs7O
      TgYHB9Hr9czMzKBWq1lYWCArK+ua5a2IESAxfJRY6w9BpU7+SeqknYikArUWkEB9Sf9UXxIM
      tQ6UBPJUJ6qcakTQS2yoF0lvRFKr0a6pTjZmcw6SwZ78b8pGMtpRmbIR8TCdXRcYcPp44okn
      8Pv9DAwMsGfPHhKJBMeOHaO+vh6n04nVasXv97N582bOnz9PfX09Z86cwe128/LLL3PkyBE0
      Gk1qNeMHP/gBTzzxBC0tLalrX3jhBV555ZUrvJyueg4zM8z+wz9if/EF9LVX7yXcDKEohM6e
      I/D2bzBubMTy5JOoTB+aSYtEgnBHB6FTp1H8AUy7d+HOzaG5q4v9+/czNTVFfn4+Fy9eZNeu
      XXR3d9PZ2cnjjz9Oe3s7RUVFjI+PY7FYiMfj7N2797r1kD2e5MR9mRFCcPr0aTZv3gzAoUOH
      qKioICcnh+HhYebm5ti/fz9dXV1kZmYSj8fxer3odDoaGxuvKm9FjACayn2oyx8CJQFCAUUG
      JZ7Uu+VLYRIvnROXXydiIEeRzLloa/YjmbIw6TJIzMzg+5cfIM8XYHvsU6gM156ESgYbCd0E
      g4PNPPHEE4yOjlJbW8v4+DgZGRmEQiECgQA6nQ6v10soFGJwcJDu7m5KS0tpb2+npKQk5XkE
      SXWitbUVm83G4OAg+/fv59y5c4RCIdra2lJLer+NEIL4xATeb32b7D/4fbQltxeKUlKpMD+w
      E9O2rQRPnsTzd3+HcetWNAX5RC/2EnOMoF9Xi+35T6O91DjNQjB1ydS5oqKCQ4cOUVlZidvt
      JhAIpFbYNBoNarWa6upq5ubmsFgs9PT00N3dzSOPPEI8Hqezs5N9+/ah0+mYCIeJDwwwMzOD
      0WgkkUhQV1fHxYsXqaqquu6zuOl3lKTUggjAgQMHUsd5eR+a3Hy0sd9oUWNFCABALJ5AURT0
      ej2oIZFIpEKoX95FjsViqNVqJElKOTboCq/M1q7JySHna18ldOYsnr/5W6zPPINhc9M1J2kT
      ExM8+uijWK1WEokELpeL/Px8dDodu3bt4ty5cym3ustqwJo1azCZTDz44INUV1ejVqtxuVxo
      tVpmZ2ex2Wy0t7dTWFjIb37zG8rLy2loaKCrq4uqqqsd+IUQxAYHmXv1NXL+6I/Q5C09k42k
      0ZCxdy/mBx/E96Mfs3DkKJlf+Bz2z750lRoiSRK7du1Kvb5eg7oW7e3tDA4O8sgjjzA4OJja
      yKytraW/vz/5W5J0urFarQwMDNDf309eXt5tC8Bys2QVSCgyyRIEICFJ0qUcABJwafZ9abZ+
      I3Po73//+5w4cYKvf/3rtLe34/P5eOqpp9Dr9fzwhz9ky5YtBAIBRkZG8Hq9fP7zn0/Zhmiu
      ExNIXggy/+qrKJEI9s+8iOYa8XNmZmbIzs4mGo0yNjaW6tWHh4epqanB4/GQkZGBVqslFouh
      0+kwGAzXveeint2lTZv5n/2cnD/8d6jvQKNIjqIy0h2Im/TOO+9gNBp56KGHOH78ONnZ2ej1
      eoxGI62trQQCAerq6ujp6aGmpoapqSnMZjNr165dMRkzlyQAihyj650foc1fT8w7TiSuxpqh
      YWZ6hqKKUhJCTU75enLycm4qAL/61a/IycnhgQce4OTJk2RnZ5Obm8vw8DDnz59n3bp1FBYW
      YrfbGR4eJhAI4HQ6+dznPnfTxhjt72fuR69i3vMQ5r17rrmTeqsIWSY6MICuogKV/vaTbwgh
      CJ87x8KhI+R85Q9RmZc2qR4ZGeHcuXM8+eSTzM7OMjw8zJ49e9BoNHR3d2O32zEYDIyOjhIO
      h9m+fTunT59m+/btGK6jJt4KlzuQRCLB0NAQFRUVxONxjEYj4XCYyclJ8vPzUavVxONx9Ho9
      arU6NTrca267WxBC4eLRXxIICSx+N2s2PsDQmXcxFOwmiwF8zmE0OetSjf9m1qA7duzA4/Eg
      hCAjI4ORkRFyc3PZuHEj5eXlJBIJPB4P+fn5+P1+/H4/27ffWjhEfU0NeX/2p/jf+jWev/k7
      7J97Gd11Ysxc3n1UQiESnhkSHg8Jtxt5dhbZ60MJBokNDyeX+4qL0ZaWoqusRFdagmQ03tJK
      hxCC4LEPiLS3k/Pvv7okQbpMLBZjaGgISKp2lZWVTE9Pk5mZicPhoLy8nNnZWWRZJhaLMTg4
      yIULF2hqalqSAORcWnrWarWpEDSXG3dGRsY1DQNXErc9AgihMDc1ytToKDarAbfbT8W6agba
      OyjfsJF4HMLzXqo2Jf1aV4pHWNzpZO5HP0aTl4e+thYkkg19ZibZwBcWkLQaJIMRTW4Omrw8
      NDk5qHNzUGdmojKbURYWUBmNJKZdxEZHiY2MEB8bQ8gKmuwstGvWoKsoT651/5ZQCCEI/Ppt
      Yg4H2X/w+8ummlyOGdrU1MSxY8cwGo2UlZURDAZpb28nkUhgs9kYGRmhqamJoaEhsrKy2LVr
      VyqSxmpkRSyD3m2EouD9zneRvV6MTU2oc3LQ5OWitttRWSy3vaOaXP6bITY2RszhID46hhKN
      orbb0JaUoC0qItzejkqvx/67Ly/LuvhHmZ2dJSsri0gkwtTUFAUFBakd00QigdFoJBAIpKKx
      6fV6DAbDivld7gUrZhXobiKpVGT//u/dkXI1+Xlo8vMwbUuOfEII5JkZYmPjhFtaiE9MkP8X
      f77sjR9IBck1Go1XLf1pL5mKXO7tf9s7bbXysRYARVE4deoULpeLgwcP0tPTQzAYZOfOnUxP
      T9Pd3U15eTkTExMYjUZCoRD79u3jvffe4/HHH18Wwy9JktDk5qLJzcW4uQmEuCONP83t8bH+
      JYQQ+P1+RkdHk7p3IIBWqyUajZKdnZ0K77F27VqysrJwOp0MDg4yMjKyJBfOc+fO8dprrxGN
      Runt7eXYsWMIIfB6vRw+coSxsTFOnTrF0NAQR44cQQjBu+++i6Ioy/jt09wKH2sBkCQJl8vF
      pz/9aTQaDaFQCJ/PlxKMqqoq3G43g4ODVFVVUVFRwfHjx68wt74dLpt3Q1Ivt9vtKXsUIQSj
      o6P09fURCASYm5tjcHCQixcvfqz8pu8XPtYqkEql4gtf+AJzc3NIksT27dsJh8Op9ehEIsHW
      rVtTZtFNTU3U19endqBvl5mZGZ577rlURLRwOExVVRU+ny/lfFNcXMzs7CxFRUUcP378tv1d
      0yyNVbkKdDfw+XzY7XZCoRBzc3NYrVbUanVqh3lmZgaz2ZzaYdZqtZhMpvs+zdP9xsd6BLiX
      XLZ1MZvNV6y4XI5zX/QRdz2T6dad49MsLx/rOUCaNDcjLQBpVjVLUoGEEMQjIVRaQ9J+HxVq
      tUQ8lkCr1SAkFUKR0aQzt6dZoSxJAOILM/Q1n0BWmUEOE4koWDK0eN1e8ksLSaChoHoTWTlp
      AUizMlmCCiRY8M1iyCzGpFcoXb+F/7+9c/tpK7vi8HfO8RXb+AL4AgmBgUzuo6SZookyM8m0
      aDSq8tDHaB7ykr+lb33Ia/+APlaq2lGrZtIkJExTItLECYEkBRLA2MZgY2PA9rn2gQFFqk28
      gYEoc74nG/zT2md7r7322Wd7LcUo440fJxSLspJLUdOdRNrDH1xuUJsPhx07gGVozIw/obpe
      I9gRJfXqFcc+u8TyzDidR09w6OyvCAc3dj82f8VlY/O+seMlkKS4OPfN1a33sR+z2Z3/+u38
      jc3ncrSxOQjsXSCbnzW2A9j8rNk3B9A0jXv37nH37l1M02RycpLJyUlg4/DYyMgIlUqF27dv
      k8lkGBsbw7IsksmkfQNts2ssyyKdTpNKpTAMg0qlQqlU2t8IYJrm1sCen58nnU5jGAbJZJJY
      LLZV5r6lpYWhoSEWFhYYGRnZzybafKBYlsXQ0BA3btxA0zTu37/P8PDw/p4FSiaTXL16dWtH
      6O2dIVmWSSQS9PX1MTw8TH9/P7du3aqbS8fGRpTNsXbt2jXcbjc+nw+v17u/6dGvX7/O6urq
      1mDfLMN55swZJiYmOHXqFKOjowwMDKBpGsVicSuNuo3NbpAkicHBwa2j8T6fb+P1Xh+HNnWV
      alXF7XKCrGDoOi6P553p0e3nBDYHwZ5GAMtQGR++SXFpmY7OKDpOuk6cx7VN3hnLsvj97d9x
      +eggv+z+rOHnbGx+Cvb0JriceUlL4jjBaJTVfBbNdBEKb5+vXpIkBo5cIBZIbPs5G5ufgj2N
      AC5/O6UXj+g5cxZVO0ltZbkp3aX+X+9lM2xsmmZPHcATSnBu8Mpbf9lIP2ia5kZRBjvrgc17
      xr48B9jNDe5unOYgtLt5aLcbmzu1exB99D7Z3Jdt0M1q8XUdwVRZyuRo6+yq+/96zww2KWZn
      8bV14nTWvwxJkho4n0V1tYzb1zgNYl2tqfL6WZJIdz+toVBd7XalRXfaXss0KOcX8UU6GlZ1
      bGSzMP+GYKIbpUEyroZ9ZKqsr2m0BOpnkNssRldPW87NIvmi+H0NipM0smlpZGdSxHsap6Vp
      pK0sp6kRIBSun+e0kW7fngQ3jAKSQiU/y7Mf7qKqel1dI63TqTB2568sZnNCNteWF3j8/Z95
      OXKPSlUV0Er4w22U5ieZevaUenPudu11uRyM3fkLuYxYe/X1RZ7c/hsP//F3YZuSpZL853eU
      iitCNgszYzz47k8k736PWSe6bGcTU+PF8E2Wl/JCNrEg8/IRMy8mMBrMOmYw+gAABEtJREFU
      2NtNWK8e3GQpu9Cwj+px8IfhzCrZVIa2WIxXoz/UbXwj0i+fETrUR35ylFK52rTOMgy8rSGQ
      5Y1as80iKchWhfxCbkfpDcuLaVytHRSmRimuNN9eR0s7iSOH8PrF8nmWMq95MzFOKH6Y2eS/
      UDWjaW2k+xjReAeS043oAtbUNSxJZqVQ39G3w6FIFHNpDENsSWfpKhYOVgpZId3BO4DkRJFN
      dFPm2KcXhKSt4RClpTyHPvkcf0vzP7v0t8VAchIOB9BFOlqS8QVD6IZEV1+/UFsB8vMpKssz
      0NpDwNd8e/X1PLlMHq8/IDRBtMa6SfT0oq4V6To1gNMh8HXLTlazUyym5hFddTtcbtTyMtnZ
      GTGhJONwyGRePUfTm3dWAMXlRltfIfN6Skj3HjiATCDUhl6rICkOodlG8fhp8XlQayqKInIp
      EpGOMJnZeRSRQQE4PAE8ToPUjydZm6VayhHpPU28uw+n0yHUXkl24va6kWVFqH8kWcHl9lAr
      F9BNwc0I0yR85ARd/X3Cg8TQddz+AP1nBwSVJroBH336BS0esdrLhq7h9rXQf+6iUB8dvANY
      OoV0iuz0CwxTLOyVslm09QUycxlRoyzOvqHn3EXcDW5IG1ErpjGdIVaX0oImDaYe3SOXzeMP
      vrsi+tvITi9t8TjFXFooAlRWCpSLBUxDRa1pYu1Fwu0PUpibEooAhrrK9OOHeEIdzIz9R8hi
      5mWS0kqV1ewklZpABDBqTD/+Nw5/lLnnD4X66OAdAJn40eOEOhIosthqM9R1GLc3QntctLKi
      RCAU5L8jd6hUa0JKb3sPkbAHda1Ibr55J7As6DjST6WYQxcJ75aFaWio1SparSI2u0kWtWoN
      hz9KNNYuoARkGb1SJnLoI6FBorj8nPrqCpFIkHiP2EnexLHz9BztxR2M4XQIXKni5tjnvyGe
      iBDrFcux+h44gITH18paYUE4Ari8flyKQSFXf7ehMRbr5RLaehmhpaZRYeiPfyA9OY4S6KQ9
      3nwhaFlxUFtdwR+J4/GK1OSymBt/gq6bfDxwScgB3IE2Ph64zJGeOLnsooASMFUKiwWW5yaY
      Hn8uJC2lXpCdz2IIruOxVDJzGaxaGcGhQDX/hrnpWXTt/3cSt+PgHUCSqazkcXh8CAYACqlp
      1qo6voBYbs2ZJw8Idp8g2tOH2yWQsFfxcuG33+JxKXhbw8gCyX7drR2c+eoKp78YxOf3vluw
      iSTT+4svOXHhMnNPHwiF901au07SdViwarvkoj3ejtMfZS0/LyQ1DQNNVXfwcE4hdqgT3VLE
      ducAU9fR1BoI2jx4B7BUMtNTeAMhELxor9dFVZWJCBaXbjvcSznzhtXymvCX5Al28Mk335JI
      7Kymr+Jw4fGKJ8OVFQenv/x6RzbZbs9+G5xuD4amcfLioJDOH+3G53UITRAApewkr5JPkTDR
      DbHo4Q53Emz1IsliNv8H4cSuQuSN0iMAAAAASUVORK5CYII=
    </thumbnail>
    <thumbnail height='192' name='Country by Profit' width='192'>
      iVBORw0KGgoAAAANSUhEUgAAAMAAAADACAYAAABS3GwHAAAACXBIWXMAABJ0AAASdAHeZh94
      AAAR2ElEQVR4nO3d6XMb533A8e+zu7hBgBcoWRIlUbIOK7JiVeOmPuQ4cR0nmXHiXG6bjDpp
      Oj1etNN/we1MX7TJ9G2bJq1n2rrNOMd44k4s2bEty0okK3YdSrJESaZ4iSIpHqBIAMQC2H36
      ghItWTxAEiQoPL/PK5Fc7D4Y7RfYxR5QWmuNEIayqj0AIapJAhBGkwCE0SQAYTQJQBhNAhBG
      kwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJ
      QBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE
      0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTSn2gMQ
      4lbaL6K9HPmCT0FHiIeDOM7KvU5LAGJV+aUMXr4Pf6oXLz8w/W93AL+QRntZ0B4Ab/fcy/O/
      fQilFAHbIhR0qI+H2dyS4J6mOu5pqmNTqo7N65JEQoElj0cCECtKezlKucuUJtopTpzGy11G
      e5mFH4eP1qC1xvU93KLHRNald+j6bdPFI0G2b2xgb1sL929rYev6+kUFIQGIitN+kVLmHO7I
      mxSvn0IX0yu2rMxUgfYPh2j/cAiAhniYA7s38MSBNna3NmNZat7HK621XrHRCaN47jXc4Vco
      jB7FdweXNa9jPffy/PuPLGseG5rreGRvK0/97nZS9bFZp5EAxLJorfGmuskPvkRh7C3w3YrM
      txIB3BQK2Dy+fyvPHNzNhqa62/4mm0BiSbTW+Plepvp/RCH9K9DFag9pTm7R48ipTo6+383B
      T27hG4/vYX1jHJAAxBJoz2Vq4EXygz8DP1/t4ZTNLXr88t3LHD/dy7Of2cPTD++SAET5tPYp
      pk+S6/shvjtQ7eEsWb5Q4j+OnOb1/+uSAER5tJcl1/tD3OFXAb/aw6mI/uFJCUDMb3ont4fs
      5e/i5S5XezgVJwGIOWmtcYcPk+v917tqW38xJAAxK+2XmLr6P+Sv/ne1h7KiJABxB+2XyPX8
      M+7wL6o9lBUnAYjbaN8l0/mPFNO/rvZQVoVcDyBmaL9k1MoPEoC4Qfslcr3fN2rlBwlAMP1p
      T37gR7jX/rfaQ1l1EsBdolAocPLkSSYnJwFIp9OcP38erTVnz55lamqK4eFhuru7gemVur29
      nVwux+DgIB0dHWitGRi4/Qiu1prCyKtM9df2pz1zkQDuEoVCgW3btvHWW2+htebEiROk0+mZ
      n48fP86pU6fo7Owkm81y+vRp4vE4r7/+OidOnKCvr4+enh5efvnl2+brTfWQ7fkXwMyTgiWA
      u0Q0GuXMmTMkk0mUUjQ2NtLd3c21a9fYtGkTgUCAWCxGS0sL+XyejRs3cvHiRdLpNEop0uk0
      jY2NtLW1zcxTl7JkL3+vZg9ylUMCuEsMDQ1RKpUIBAKcO3eOuro66uvreeKJJzh27BjNzc1Y
      lsWVK1cYGxtDKYVlWTz44IPEYjHC4TAjIyP09PTc2HTyyfX9O16us9pPrarkgpi7jOd55PN5
      YrHZr3ACmJiYIJFIzDufQvodMpf+lrW66VPJC2LmI+8Adxnbtudd+YEFV37t5cn1/oC1uvKv
      JgnAMFpr8oM/w3f7qz2UNUECMIzvDjA18JNqD2PNkAAMorVm6sp/gj9V7aGsGRKAQXz3KoXx
      E9UexpoiARgkP/hSxW5bUiskAEP4hRHc0TeqPYw1RwIwRP7aYfBy1R7GmiMBGEBrj4K8+s9K
      AjBAKdNxV9/HZyVJAAYojB6t9hDWLAmgxmnPpZA+We1hrFkSQI3zprrRxZFqD2PNkgBqXHGi
      vdpDWNMkgBpXmjxT7SGsaRJADdNejlL2w2oPY02TAGqYl7+KLl1feEKDSQA1zMtfQS56mZ8E
      UMP8qSvVHsKaJwHUsOl3ADEfCaCGecv8qlITSAA1zC+MVnsIa54EUMvk0scFSQA1SvtFtC5V
      exhrngRQo7TvgtzzbEESQK3yXe7WrzOdKIFX9EgxTpCV/QZ6+YqkWmUFuZte37SG4aLitVGb
      dycc2py9PDf6Crlt45ws3sc77h76vWZ0hZ+TBFCjlB0FZa35A8Faw0BBcWTUpn3SoqQV4cAG
      3MQGxr0WUldzPLPlOM9Ej/NhaSMn8p/gVOE+xv26iixfAqhRStko5azZ9V9ruOIqfjlq89tJ
      Cw8187dYZDe+bTOUaiLRlQEFkc0D7Aj0syPQzzf1LzlbaONtdx9nCtvJ6fCSxyEB1DBlR9Fe
      ptrDuI3W0OdOb+qcyUy/4t/KtmJEQpsBuLouxY6uXtzBZmA6AqXAUT4PhDp5INRJxg/z28IO
      3s7v42KxleIiV2kJoIYpJwmFa9UeBvDRK/6RUZvTkxY+atbpYpGdKDW9nT/QkrrxW3VHBDfF
      rTyPhs/wSOgMI36S99ydHM3v54qXgjmWcSsJoIbZkVa83KWqjuHmin94xOZ0xkLPs1IqHGLh
      nTM/jycSuIEAoWKR+SIAUApS9nU+H/0NT0V+Q5/Xwon8JzjpfoJhv37OZUoANcwOb6zasrWG
      vrzi1bH5X/FvFQpuwLHjMz97tkW6PsH64ZundNyIwFdEtl69I4KblILNzjU2x6/xldgxLhQ3
      8457H++5u5jUt3+3ggRQw6wqBKA19OYVh0dtzmVu37ldSF10z+2/UIqi8/FVVOFeawKYN4Kb
      gsrj/mAX9we7+KPY67QX7uWku4czhe0UcSSAWraa7wBaQ3de8eqozdkFNnVm49j1hALr7/j9
      SGMDrQNDH/vtdATat4i2XUGVeWggZrk8HP6Ah8MfcN2P8uv8XgmgltnhTSinHl0aX7Fl+Bo+
      zCleH7M5l138in9TPLILpew7fj+wLsUDH3TMMldFYaQBUDciWNwHvkkrxxeipySAWqbsME58
      J8XxUxWft6fhUk7x2qjDpZwqaxt/LgqHaHj7rH8bTDXjWxa2P9tpHYrCSD1aQ2zb4iMA2Qeo
      eU7dvooG4GvoyCqOjDp0TinK+ahxIZFwG5aa/WDWVCTM9UScxvGJOR6tKI42kNWK2PbesjeH
      bpIAalwgcT+VuCrA03Aua3F4xKYnX5kV/6Z4eBdqnr3ZwVTzPAFMK47VkwVi2/sW9U4gAdQ4
      O7IFK5jCLwwv6fGehrMZi1dHbXrzasnb+HMJOs0EAy3zTjPQkmLPpcsLzmspEUgANU5ZIQIN
      j+AOvbSox3kazmUsXrmx4lfyFf9Wscj8r/4AAy3N+EphlXF9w0wE2/pQ9sLTSwAGCDV9uuwA
      fA1nbrzi9+RX9nRqS4WIhtoWnG4yHiMbjVCXLe8bbopj9WR9RWxH74LvBBKAAezYLqzQBnz3
      6pzTlDR8kLE4PGrTt4Kv+LeKhrdjWaEFp/Ntm+GmhrIDACiOJ8l+uHl6c8ie+8Kgu+eKCbFk
      SilCqSdn/VvJh/cnLb7XHeAH/Q59eYvVWPlBEQvvKnvqq+vm30+YTTGdJHNxC35p7tVcAjBE
      qPlJlP3ReTCehvcmLL7bE+Df+gNccVdrxb8xnsB6Ak5D2dMPtjQv6dqG0kQd2Utb0N7sq7oE
      YAgr2ESw+UmKPpy8bvH3XQGevxqg363OKhCP7F5w5/dWY8kkbjC4pGWVJurIXNg66zuBBGAQ
      q+UZ/ulKHf814HCtUL3/esuKzlz0Uq5iwGGsPrnkZZYm42QvbsUv3X66hQRgkEhkHds3PMZq
      burMJhbejlKL/PxFKa6uX/x+wK2mI7h9n0ACMMzXPvmHRALRKo7AJhbevaRH9i8zAJiOIHOx
      bWafQAIwTGO0ia/se7Zqyw8HN+DYS7ujw3BTQ0Uu8vcmo0x2tOGXbAnANEopPrf7i9yTqM7V
      YvHIfYva+b2VGwwuaz/gIwovEyNzYasEYKKgHeSPH/xTrMWeOrlMjp0gHLxn6TNQaknHA+bi
      ZWISgKk+ufF3eGLnU6u6zFh45+J3fj9msKW5QqOZJgEY7JsHvs22pntXZVnzXfSyGNeaGvGX
      uAk1GwnAYCEnxF8+8jer8qlQJLQF24otPOECxpN15CJLvxPcx0kAhttU38qffOrPl7xjWq5y
      Tnsui1JcXZdaeLoySQCCR7c9zrMPfAu1QgfIAnbD8nZ+P2awRQIQFfb03q/yxT1fWpF5xyJL
      O/A1l8FUU8Vu+isBCAAsZfEH+w9xcNvjFZ5vkGh44YteFmM8kSAfXvg6gnJIAGKGYzv82cN/
      xaPbHq/YPCOhNmwrUrH5ARSDAUYa5r7f52JIAOI2juXwFw//NV/c86UK7BMo4hXe/LlpoEI7
      whJADZmamr4BitaayclJJiYm8DyPsbExtNZkMhlc152ZPp/PUyqVmJycJJ/Po7VmamoK27L5
      5oFv843931rW0eJgoIWA07js5zWboeamisxHrgmuEf39/Rw5coTvfOc7AHR1dXHhwgUSiQTR
      aBSlFCMjI9i2zdNPP43v+7zwwgscOHCA7u5uSqUSBw4c4Pjx4xw6dAhLWXx579dojqV4/p3v
      M1Us/3rcm6aP/K7Ma+xQqpmSZeHMese48sk7QI3YuHEjra2twPQJb7FYjFwuRzKZxHVdOjo6
      2L17N4lEAoA333yTYDDI8PAwnucxOjpKa2sr69d/dINapRSPbvs0f/eFf6CtcXFHcS0VJhre
      Vrkn+DFuMMBo4/L3A+znnnvuueUPR1RbV1cXx48fp7m5mVwuR1dXF8Vika1btzI0NMRDDz1E
      e3s7tm3jOA47d+4kEomQSCTwfZ+GhgZ83+fEiRO0trbOhAKQCCd5uO0x8sUpusY60WV8CBmL
      7CISbF25A2xK0Tw2zrqRseXNRmv5NuVakslk0FpTVzf7Ofdaa4aGhli3bt2iV06tNe1X3+eF
      d5+n/3rfPFNarG/8CgGnMp/UzGVHZzdPvn1iybvqga1bJQCxeIWSy5GOX/DSmR/Pum8QCm6g
      pf4LKz6OeCbLoZ/8fNHb8SoaIfn1r1P35O9LAGJptNaMZId56fSP+VXXWxS8wszfmhJPEA1v
      XZVxHPrJz0lksmVNqyIRYgcfJfHMl3Eapz+dkgDEsmitGZoc4JXzL3P88lEKns36xq9iWYFV
      Wf7njv6KHd29806jolHin/0MdZ9/CrupCaUUWmvy+bwEICpnfCrNyZ4PODtcZDi7Ot9PfP/5
      izz2znuz/s3ZuJHYYweJP3YQu+H2m3CdO3eOjo4OCUBUntaaK9fHebe/j3PXhphw8yu2rKax
      NM++fJib98C1GxuJ/t6niD12kMCWLfPu6L/22msSgFhZRc9jYHKCy2OjXB4b4crEdbKFwsIP
      LFO97XDo7AUS995LaM8egq2bUAvcQU5rTXt7O8eOHZMAxOpySyVGshlGcllGc1mGMhlGc1km
      XRfXK+H7Gl9rNBqFwlLgWDYhxyEeCpGKxUhF4zRFY6TicRojUUJ3fJVq+SQAsaZ4vk/B8yh6
      Ho5lEXRsHOvOb4+sFAlALMv4+Dh1dXXYto3WmnQ6TUNDA11dXaRSKQYHB8nn8+zatQvHcejs
      7KSpqYl0Ok0ul2PHjh3kcjkaGhpW/LLM2ci5QGLJ8vk8P/3pT8lkpj/x6e7u5siRIwwMDNDd
      3c0bb7xBQ0MDnZ2deJ5HqVTCtm2OHj1KfX09fX19jIyM8OKLL1IsFqvyHCQAsWThcJi9e/fO
      /NzW1kZzczOO4zA8PEwgECCRSNDQ0EAkEpk5+S4QCJBMJgmHw2zatIndu1fmmoFyyOnQYskm
      JiY4f/48pVKJVCqF1prOzk5aWlqIRCKEQiG6u7vZt28fAwMDaK0ZHx8nEAjQ09PDvn37GBsb
      49KlSySTSfbv37/qz0H2AcSy+b7PxMQE9fVzn/yWTqdJJpNY1tra6JAAhNHWVo5CrDIJQBhN
      AhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQA
      YTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBG
      kwCE0SQAYTQJQBhNAhBGkwCE0SQAYTQJQBhNAhBGkwCE0f4fdF96Va7Kt3YAAAAASUVORK5C
      YII=
    </thumbnail>
  </thumbnails>
</workbook>
