"""removed column

Revision ID: 57ff7ed87652
Revises: 36658e875da7
Create Date: 2025-06-11 15:56:04.972568

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '57ff7ed87652'
down_revision: Union[str, None] = '36658e875da7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('server_report_details', 'workbook_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
  
    op.add_column('server_report_details', sa.Column('workbook_id', sa.VARCHAR(length=36), autoincrement=False, nullable=True))
   
    # ### end Alembic commands ###
