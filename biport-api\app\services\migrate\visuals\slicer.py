import json, uuid

from ..core import (get_queryref, get_calc_filter_column, get_select_json, fetch_zone_attributes, 
                    fetch_border_color, calculate_dimensions)
from app.core import slicer_template, singleValue_slicer_template
from app.core import SLICER_MODES
from app.core import logger
from app.core.enums import TableauXMLTags, WorkSheet


def get_slicer_report(dashboard, table_column_data):
    try:

        overall_slicer_result = []

        # for dashboard in dashboards:
        zones = dashboard.findall(TableauXMLTags.ZONE.value)
        column_instance_data = dashboard.findall(WorkSheet.DS_COL_INSTANCES.value)
        column_data = dashboard.findall(WorkSheet.DS_COLS.value)
        with open("table_column_data.json","w", encoding="utf-8") as fw:
            json.dump(table_column_data, fw, indent=4)
        if column_instance_data and column_data:
            for column in column_instance_data:
                twb_query = column.get(TableauXMLTags.NAME.value)
                col_name = twb_query.split(":")[1]


                filter_data, col_data, table_name = get_calc_filter_column(col_name, table_column_data, column_data)
        
                value_queryref = get_queryref(filter_data, col_data, table_name)
                from_list = [{"Name": table_name,"Entity": table_name,"Type": 0}]
                select_list = [get_select_json(col_data, filter_data, table_name, value_queryref)]
                zone_attributes = fetch_zone_attributes(zones, col_name)
                mode = zone_attributes.get('@mode')
                mode_value = SLICER_MODES.get(mode) if mode else "Between"
                border_color = fetch_border_color(zone_attributes.get('zone_style'))
                border_color = border_color if border_color else "#000000"

                dimensions = calculate_dimensions(zone_attributes.get("@x", 0), zone_attributes.get("@y",0), zone_attributes.get("@h",0), zone_attributes.get("@w", 0))
                    
                slicer_json = {
                    "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
                    "value_queryref" : value_queryref,
                    "from_list" : json.dumps(from_list),
                    "select_list" : json.dumps(select_list),
                    "mode_value" : mode_value,
                    "title_text" : col_data,
                    "border_color" : border_color
                    }
                
                if mode_value == "radiolist":
                    selection_list = [{"properties": {"selectAllCheckboxEnabled": {"expr": {"Literal": {"Value": "true"}}},"strictSingleSelect": {"expr": {"Literal": {"Value": "true"}}}}}]

                    single_value_json = {
                        "filter_source": table_name[0].lower(),
                        "filter_property": col_data,
                        "selection_list": json.dumps(selection_list)
                    }
                    result = singleValue_slicer_template.format(**slicer_json, **single_value_json, **dimensions)
                    
                elif mode_value == "dropdown":
                    selection_list = [{"properties": {"strictSingleSelect": {"expr": {"Literal": {"Value": "true"}}},"selectAllCheckboxEnabled": {"expr": {"Literal": {"Value": "true"}}},"singleSelect": {"expr": {"Literal": {"Value": "false"}}}}}]

                    single_value_json = {
                        "filter_source": table_name[0].lower(),
                        "filter_property": col_data,
                        "selection_list": json.dumps(selection_list)
                    }
                    result = singleValue_slicer_template.format(**slicer_json, **single_value_json, **dimensions)
                else:
                
                    result = slicer_template.format(**slicer_json, **dimensions)
                overall_slicer_result.append({"config": result, "filters":"[]", **dimensions})


        return overall_slicer_result
    
    except Exception as e:
        logger.error(f"---Error in generating slicers--- {str(e)}")
        raise ValueError(f"Error in generating slicers - {str(e)}")

