"""Add columns in report_details

Revision ID: 83a079b0e077
Revises: 6b46e399e2bf
Create Date: 2025-06-10 17:17:07.613312

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '83a079b0e077'
down_revision: Union[str, None] = '6b46e399e2bf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('report_details', sa.Column('workbook_id', sa.String(length=36),nullable=True))
    op.create_unique_constraint('uq_report_details_workbook_id', 'report_details', ['workbook_id'])



def downgrade() -> None:

    op.drop_constraint('uq_report_details_workbook_id', 'report_details', type_='unique')
    op.drop_column('report_details', 'workbook_id')
