from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

from app.schemas import *
from app.services import DiscoverService, DiscoverServerService
from app.models_old.user import UserOld
from app.models.users import User
from app.core.dependencies import get_current_user
from uuid import UUID
from app.services.discovery import DiscoverProcessor
discover_router = APIRouter()
from app.schemas.discover import AssignUserRequest

@discover_router.post("/discover/server", response_model=dict)
async def discover_server(request: DiscoverServerRequest, page: int = Query(1, ge=1), page_size: int = Query(10, ge=1, le=100),
                          user: UserOld = Depends(get_current_user)):
    """API to get a discover server."""
    response = await DiscoverServerService.execute(DiscoverServerService()._discover_server, request, page, page_size)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@discover_router.post("/discover/servers", response_model=dict)
async def get_all_servers(organization_id: UUID = Query(...),page: int = Query(1),page_size: int = Query(10),
                             user: User = Depends(get_current_user)):
    
    """API to get all servers for a given organization."""
    response = DiscoverProcessor().process_get_all_servers(organization_id, page, page_size)
    return JSONResponse(content={"data": response.data, "error": response.error},status_code=response.status_code)

@discover_router.post("/discover/sites", response_model=dict)
async def get_all_sites(server_id: UUID = Query(...),page: int = Query(1),page_size: int = Query(10),
                        user: User = Depends(get_current_user)):
    """API to get all sites for a given server ID."""
    response = DiscoverProcessor().process_get_all_sites(server_id, page, page_size)
    return JSONResponse(content={"data": response.data, "error": response.error},status_code=response.status_code)

@discover_router.post("/discover/managers", response_model=dict)
async def get_all_managers(organization_id: UUID = Query(...),page: int = Query(1, ge=1),page_size: int = Query(10, ge=1, le=100),
                           user: User = Depends(get_current_user)):
    """ API to get all managers by organization ID with pagination"""
    response = DiscoverProcessor().process_get_all_managers(organization_id=organization_id,page=page,page_size=page_size)
    return JSONResponse(content={"data": response.data, "error": response.error},status_code=response.status_code)

@discover_router.post("/discover/developers", response_model=dict)
async def get_all_developers(organization_id: UUID = Query(...),manager_id: UUID = Query(...),page: int = Query(1, ge=1),page_size: int = Query(10, ge=1, le=100),
                            user: User = Depends(get_current_user)):
    """API to get all developers by organization ID and manager ID with pagination"""
    response = DiscoverProcessor().process_get_all_developers(organization_id=organization_id,manager_id=manager_id,page=page,page_size=page_size)
    return JSONResponse(content={"data": response.data, "error": response.error},status_code=response.status_code)

@discover_router.post("/discover/root-projects", response_model=dict)
async def get_all_root_projects(page: int = Query(1, ge=1),page_size: int = Query(10, ge=1, le=100),
                                user: User = Depends(get_current_user)):
    """API to get all root projects where is_upload = True"""
    response = DiscoverProcessor().process_get_all_root_projects(page=page, page_size=page_size)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@discover_router.patch("/discover/project/assign", response_model=dict)
async def update_project_assigned_to(project_id: UUID = Query(...),user_id: UUID = Query(...),
                                    user: User = Depends(get_current_user)):
    """API to assign user to project via query params"""
    response = DiscoverProcessor().process_update_assign_to(project_id, user_id)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@discover_router.post("/discover/projects/by-site", response_model=dict)
async def get_all_projects_by_site(site_id: UUID = Query(...), user: User = Depends(get_current_user)):
    """API to get all projects by site including subprojects and files"""
    response = DiscoverProcessor().process_get_all_projects_by_site(site_id)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@discover_router.post("/discover/project/by-parent", response_model=dict)
async def get_projects_by_parent(project_id: UUID = Query(...), user: User = Depends(get_current_user)):
    """Get all sub-projects by parent project ID along with files and assigned user."""
    response = DiscoverProcessor().process_get_projects_by_parent(project_id)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@discover_router.post("/discover/developers/by-org", response_model=dict)
async def get_all_developers(organization_id: UUID = Query(...), page: int = Query(1, ge=1), page_size: int = Query(10, ge=1, le=100),
                         user: User = Depends(get_current_user)):
    """API to get all developers by organization ID with pagination"""
    response = DiscoverProcessor().process_get_all_developers_by_orgid( organization_id=organization_id, page=page, page_size=page_size)
    return JSONResponse(content={"data": response.data, "error": response.error},status_code=response.status_code)
