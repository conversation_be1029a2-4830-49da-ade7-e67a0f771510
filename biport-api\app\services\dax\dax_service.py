import os
import xml.etree.ElementTree as ET
import shutil
from uuid import UUID

from app.core.config import S3Config
from app.core import ClOUD_WORKBOOKS_PATH, logger
from app.core.exceptions import BadRequestError, ServerError
from app.services import extract_json, get_tables_data
from app.services.analysis import calculate_fields
from app.models.report_details import ReportDetailManager
from .convert import get_openai_response, generate_pdf

class DaxService:
    async def dax_formulae_converstion(self, report_id: UUID, user) -> dict:
        logger.info(f"[DAX Service] Starting DAX conversion for report_id: {report_id}, user: {user.email}, organization: {user.organization.name}")

        s3 = S3Config()

        # Get organization_name from user
        organization_name = user.organization.name
        report_handler = ReportDetailManager()

        # Retrieve report details to get report_name
        logger.info(f"[DAX Service] Retrieving report details for report_id: {report_id}")
        report_detail = report_handler.get_report_by_id(report_id)
        if not report_detail:
            logger.error(f"[DAX Service] Report {report_id} not found in database")
            raise BadRequestError(detail=f"Report {report_id} not found")

        report_name = report_detail.name
        s3_report_id = report_detail.report_id
        project_id = report_detail.project_id

        logger.info(f"[DAX Service] Report details - name: {report_name}, s3_report_id: {s3_report_id}, project_id: {project_id}")

        # Construct S3 input and output paths
        #input can be .twb or .twbx
        input_key = f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{report_name}.twb"
        input_key_twbx = f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{report_name}.twbx"
        output_key = f"BI-PortV3/{organization_name}/{s3_report_id}/converted_outputs/{report_name}.pdf"
        pdf_filename = f"{report_name}.pdf"

        logger.info(f"[DAX Service] S3 paths - input_key: {input_key}, output_key: {output_key}")

        # Check if report is already converted
        logger.info(f"[DAX Service] Checking if report is already converted - is_converted: {getattr(report_detail, 'is_converted', False)}")
        if report_detail and getattr(report_detail, 'is_converted', False):
            logger.info(f"[DAX Service] Report already converted, generating presigned URL for existing file")
            # Generate presigned URL for existing converted file
            download_url = await s3.generate_presigned_url(output_key)
            report_path = report_handler.get_report_hierarchy_path(project_id, report_name)  # type: ignore

            logger.info(f"[DAX Service] Returning existing conversion result for report_id: {report_id}")
            return {
                "report_id": str(report_id),
                "report_name": report_name,
                "download_url": download_url,
                "report_path": report_path,
                "converted_status": report_detail.converted_status or '{"status": "SUCCESS", "message": "Previously converted"}'
            }

        # If not converted, perform conversion
        logger.info(f"[DAX Service] Report not converted, starting conversion process")
        local_base_path = None
        try:
            # Set up local directories
            local_base_path = os.path.join(ClOUD_WORKBOOKS_PATH, str(report_id))
            local_input_path = os.path.join(local_base_path, "input_files")
            local_pdf_path = os.path.join(local_base_path, "pdf")

            logger.info(f"[DAX Service] Setting up local directories - base: {local_base_path}")
            os.makedirs(local_input_path, exist_ok=True)
            os.makedirs(local_pdf_path, exist_ok=True)

            # Download the input file from S3
            logger.info(f"[DAX Service] Attempting to download TWB/TWBX files from S3")
            local_paths = [] #list of local paths of twb files downloaded from S3. It will be empty if twb file is not found. It will have one element if twb file is found. It will have two elements if twbx file is found.
            for input_key_attempt in [input_key, input_key_twbx]: #try twb and twbx files in that order
                try:
                    logger.info(f"[DAX Service] Trying to download: {input_key_attempt}")
                    local_paths = await s3.download_twb_file_from_s3(input_key_attempt, local_input_path)
                    logger.info(f"[DAX Service] Successfully downloaded {len(local_paths)} files from {input_key_attempt}")
                    break #if twb file is found, break the loop
                except Exception as e:
                    logger.warning(f"[DAX Service] Failed to download S3 file: {input_key_attempt}, error: {str(e)}")
                    continue  # Try the next file type

            if not local_paths:
                logger.error(f"[DAX Service] No TWB/TWBX files found in S3 for report_id: {report_id}")
                # Mark as failed and return error response
                report_handler.mark_converted(str(report_id), "FAILURE", "TWB/TWBX file not found in S3")
                raise BadRequestError(detail="TWB/TWBX file not found in S3")

            logger.info(f"[DAX Service] Processing {len(local_paths)} TWB files for conversion")
            for twb_file in local_paths:
                try:
                    logger.info(f"[DAX Service] Converting file: {twb_file}")
                    data = extract_json(twb_file)
                    table_data = get_tables_data(data)
                    tree = ET.parse(twb_file)
                    root = tree.getroot()
                    calculated_fields = calculate_fields(root)

                    logger.info(f"[DAX Service] Found {len(calculated_fields)} calculated fields to convert")

                    result_payload = {}
                    field_results = []

                    for field in calculated_fields:
                        if 'Parameter' in field.get("name", ""):
                            continue
                        caption = field.get("caption", "")
                        formula = field.get("formula", "")
                        tableau_formula = f"{caption} = {formula}"
                        logger.debug(f"[DAX Service] Converting field: {caption}")
                        dax_formula = get_openai_response(tableau_formula, table_data)
                        field_results.append({
                            'cal_field': tableau_formula,
                            'powerbi_dax': dax_formula
                        })

                    result_payload[report_name] = field_results
                    logger.info(f"[DAX Service] Converted {len(field_results)} fields successfully")

                    pdf_path = os.path.join(local_pdf_path, pdf_filename)
                    logger.info(f"[DAX Service] Generating PDF at: {pdf_path}")
                    generate_pdf({"dax": result_payload}, pdf_path)

                    logger.info(f"[DAX Service] Uploading PDF to S3: {output_key}")
                    await s3.upload_to_s3(pdf_path, output_key)

                    # Mark as successfully converted
                    logger.info(f"[DAX Service] Marking report as successfully converted")
                    report_handler.mark_converted(str(report_id), "SUCCESS", "DAX conversion completed successfully")

                    presigned_url = await s3.generate_presigned_url(output_key)
                    report_path = report_handler.get_report_hierarchy_path(project_id, report_name)  # type: ignore

                    logger.info(f"[DAX Service] DAX conversion completed successfully for report_id: {report_id}")
                    return {
                        "report_id": str(report_id),
                        "report_name": report_name,
                        "download_url": presigned_url,
                        "report_path": report_path,
                        "converted_status": '{"status": "SUCCESS", "message": "DAX conversion completed successfully"}'
                    }

                except Exception as file_err:
                    logger.error(f"[DAX Service] File processing error for {twb_file}: {str(file_err)}", exc_info=True)
                    # Mark as failed
                    report_handler.mark_converted(str(report_id), "FAILURE", f"File processing failed: {str(file_err)}")
                    raise ServerError(detail=f"Failed processing file: {twb_file}")

        except Exception as e:
            logger.error(f"[DAX Service] Conversion failed for report_id: {report_id}, error: {str(e)}", exc_info=True)
            # Mark as failed if not already marked
            try:
                report_handler.mark_converted(str(report_id), "FAILURE", f"DAX conversion failed: {str(e)}")
            except Exception as mark_err:
                logger.warning(f"[DAX Service] Failed to mark report as failed: {str(mark_err)}")
            raise ServerError(detail=f"DAX conversion failed for {report_id}: {str(e)}")

        finally:
            if local_base_path and os.path.exists(local_base_path):
                logger.info(f"[DAX Service] Cleaning up local directory: {local_base_path}")
                shutil.rmtree(local_base_path, ignore_errors=True)

        # This should never be reached due to the return statements above, but added for completeness
        logger.error(f"[DAX Service] Unexpected code path reached for report_id: {report_id}")
        raise ServerError(detail="Unexpected error in DAX conversion process")