from typing import List , NamedTuple , Optional
from pydantic import BaseModel

class WorkbookId(BaseModel):
    workbook_name: str
    workbook_id: str
 
class S3Path(BaseModel):
    workbook_name: str
    s3_path: str
 
class WorkbooksRequest(BaseModel):
    workbook_ids: Optional[List[WorkbookId]] = None
    s3_paths: Optional[List[S3Path]] = None

class DaxConversionRequest(NamedTuple):
    twb_files: List[dict]  
    is_upload_file: bool
    organization_name: str
    user_email: str

