from app.services.data_sources.datasources_format import DataSourceDetailsFormat
from app.core.enums import Datasource_types


class DatasourcesDetailsHandling:
    @staticmethod
    def get_ds_format(ds_type,
                      ds_details):
        new_ds = {}
        if ds_type == Datasource_types.MYSQL.value:
            new_ds = DataSourceDetailsFormat.new_mysql_source(ds_details)
        elif ds_type == Datasource_types.POSTGRESQL.value:
            new_ds = DataSourceDetailsFormat.new_postgresql_source(ds_details)
        elif ds_type == Datasource_types.MSSQL.value:
            new_ds = DataSourceDetailsFormat.new_mssql_data_source(ds_details)
        elif ds_type == Datasource_types.ORACLE.value:
            new_ds = DataSourceDetailsFormat.new_oracle_source(ds_details)
        elif ds_type == Datasource_types.SQLITE.value:
            new_ds = DataSourceDetailsFormat.new_sqlite_source(ds_details)
        return new_ds

    @staticmethod
    def get_record(record_dict,
                   record):
        for key, value in record_dict.items():
            setattr(record, key, value)
        return record