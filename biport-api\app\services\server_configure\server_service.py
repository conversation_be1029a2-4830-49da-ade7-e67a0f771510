import json
import uuid
import aiofiles
import requests
import xml.etree.ElementTree as ET
from fastapi import BackgroundTasks
import os
from typing import Optional

from app.core.exceptions import BadRequestError, NotFoundError, ServerError, ConflictError, AuthenticationError
from app.core.response import ServiceResponse
from app.core.config import logger, S3Config
from app.core.constants import BAC<PERSON>GROUND_TASK, TABLEAU_VERSION, XMLNS, CLOUD_SITE_DISCOVERY_DIR, FILE_TYPE, LOCAL_DIR
from app.core.base_service import BaseService
from passlib.hash import bcrypt
from app.core.task import SiteDiscoveryService
from app.models.organization_details import OrganizationDetailManager
from app.models.project_details import ProjectDetailManager
from app.models.report_details import ReportDetailManager
from app.models.users import User
from app.models_old.cloud_server import CloudServerDetailsManager
from app.models_old.on_premise_server import ONServerDetailsManager
from app.models_old.server import ServerDetailsManager, ServerDetails
from app.models_old.site_discovery import SiteDiscoveryManager
from app.core.enums import <PERSON>verSiteStatus, ServerAuthType
from app.models_old.reports import ReportDetailsManager
from app.models_old.user import UserOld
from app.schemas.server import AddServerRequest, UpdateServerRequest, UpdateServerStatusRequest, ServerType, AddTableauServerRequest,DeleteServerRequest
from app.models.tableau_server import TableauServerDetailManager, TableauServerCredentialManager, TableauSiteDetailManager
from app.schemas.server import DeleteServerRequest
from app.services.discovery.discover_service import TableauClient

class ServerService(BaseService):
    """Handles all server-related operations."""
    
    def __init__(self):
        super().__init__()
        self.s3 = S3Config()
        self.background_task = os.getenv(BACKGROUND_TASK, False)
        self.site_discovery_manager = SiteDiscoveryManager()

    def _encode_for_display(self, text: str) -> str:
        """Encodes text for safe display."""
        
        logger.debug("Encoding text for safe display.")
        return text.encode('ascii', errors="backslashreplace").decode('utf-8')

    def create_xml_request(self, username: Optional[str], password: Optional[str], pat_name: Optional[str], pat_secret: Optional[str], site: str = "") -> bytes:
        """Creates an XML request for server authentication."""
        logger.debug(f"Creating XML request for username: {username}, site: {site}")
        xml_request = ET.Element('tsRequest')
        if username and password:
            credentials_element = ET.SubElement(xml_request, 'credentials', name=username, password=password)
        elif pat_name and pat_secret:
            credentials_element = ET.SubElement(
                xml_request, 'credentials', personalAccessTokenName=pat_name, personalAccessTokenSecret=pat_secret
            )
        else:
            raise ValueError("Either username/password or pat_name/pat_secret must be provided.")
        ET.SubElement(credentials_element, 'site', contentUrl=site)
        return ET.tostring(xml_request)

    def server_sign_in(self, request: AddServerRequest, site: str = ""):
        """Signs in to the server and retrieves authentication details."""
        
        logger.info(f"Signing in to server: {request.server_url}")
        url = f"{request.server_url}/api/{TABLEAU_VERSION}/auth/signin"
        if request.server_type.value == ServerType.ONPREMISE.value:
            return self._sign_in(
                url,
                request.username,
                request.password,
                request.pat_name,
                request.pat_secret,
                site
            )
            
        elif request.server_type.value == ServerType.CLOUD.value:
            # Validate all credentials; fail fast if any fail
            for credential in request.server_credentials:
                self._sign_in(
                    url,
                    None,
                    None,
                    credential.pat_name,
                    credential.pat_secret,
                    site
                )
            # Return the last successful one (or modify as needed)
            last_credential = request.server_credentials[-1]
            return self._sign_in(
                url,
                None,
                None,
                last_credential.pat_name,
                last_credential.pat_secret,
                site
            )


    def _sign_in(
        self, url: str, username: Optional[str], password: Optional[str], pat_name: Optional[str], pat_secret: Optional[str], site: str = ""
    ) -> tuple[Optional[str], Optional[str], Optional[str], Optional[str], int]:
        """
        Returns:
            token: str
            site_id: str
            user_id: str
            content_url: str
            status_code: int
        """
        xml_request = self.create_xml_request(username, password, pat_name, pat_secret, site)
        headers = {'Content-Type': 'application/xml'}
        try:
            server_response = requests.post(url, data=xml_request, headers=headers, timeout=30)
            status_code = server_response.status_code
            server_response_text = self._encode_for_display(server_response.text)

            if status_code != 200:
                logger.error(f"Failed to sign in to the server. Status code: {status_code}")
                raise ServerError(f"Failed to sign in to the server. Status code: {status_code}")

            parsed_response = ET.fromstring(server_response_text)
            credentials_elem = parsed_response.find('t:credentials', namespaces=XMLNS)
            token = credentials_elem.get('token') if credentials_elem is not None else None
            site_elem = parsed_response.find('.//t:site', namespaces=XMLNS)
            site_id = site_elem.get('id') if site_elem is not None else None
            content_url = site_elem.get('contentUrl') if site_elem is not None else None
            user_elem = parsed_response.find('.//t:user', namespaces=XMLNS)
            user_id = user_elem.get('id') if user_elem is not None else None
            if not all([token, site_id, content_url, user_id]):
                logger.error("Missing authentication details in server response.")
                raise ServerError("Missing authentication details in server response.")
            logger.info("Successfully signed in to the server.")
            return token, site_id, user_id, content_url, status_code
        except ET.ParseError as e:
            logger.error(f"Error parsing server response: {e}")
            raise ServerError("Failed to parse server response")
        except requests.RequestException as e:
            logger.error(f"Error during server sign-in: {e}")
            raise AuthenticationError("Server is unreachable")
        except Exception as e:
            raise e        

    def add_server(self, request: AddServerRequest, user: UserOld, bt: BackgroundTasks) -> ServiceResponse:
        """Adds a new server after validation and authentication."""
        logger.info(f"Processing add server request for server: {request.server_name}")
        
        if ServerDetailsManager.get_server_by_name_or_url(request.server_name, request.server_url):
            logger.error(f"Server already exists")
            raise ConflictError(f"Server already exists")

        ServerService().server_sign_in(
            request,
            site=""
        )

        server_id = ServerDetailsManager.add_server(
            user.id,
            request.server_name,
            request.server_url,
            request.server_type,
            user.id,  # created_by
            user.id  # updated_by
        )
        
        if request.server_type == ServerType.ONPREMISE.value:
            ONServerDetailsManager.add_on_premise_server(
                server_id,
                request.server_auth_type.value,
                request.username,
                request.password,
                request.pat_name,
                request.pat_secret,
                user.id,  # created_by
                user.id  # updated_by
            )
            ServerDetailsManager.update_site_count(server_id, 1)
            
        elif request.server_type == ServerType.CLOUD.value:
            not_allowed = []
            for credential in request.server_credentials:
                if CloudServerDetailsManager.get_cloud_server_by_pat_secret(
                    credential.pat_secret
                ) is not None:
                    not_allowed.append(credential.pat_secret)
                else:
                    cloud_server = CloudServerDetailsManager.add_cloud_server(
                        server_id,
                        credential.pat_name,
                        credential.pat_secret,
                        user.id,  # created_by
                        user.id  # updated_by
                    )

                    if self.background_task:
                        # Start the site discovery process in the background
                        cloud_server_id = getattr(cloud_server, 'id', None)
                        if not isinstance(cloud_server_id, uuid.UUID):
                            raise ValueError("cloud_server.id must be a UUID")
                        site_discovery = self.site_discovery_manager.add(server_id, "CLOUD", DiscoverSiteStatus.INITIATED, cloud_server_id)
                        site_discovery_id = getattr(site_discovery, 'id', None)
                        if not isinstance(site_discovery_id, uuid.UUID):
                            raise ValueError("site_discovery.id must be a UUID")
                        client = SiteDiscoveryService(
                            server_url=request.server_url,
                            server_id=server_id,
                            server_type_id=cloud_server_id,
                            site_discovery_id=site_discovery_id,
                            pat_name=credential.pat_name,
                            pat_secret=credential.pat_secret
                        )
                        # Add the task to the background task queue
                        bt.add_task(client.run)                            
            ServerDetailsManager.update_site_count(server_id, len(request.server_credentials) - len(not_allowed))
            # Check if any PAT secrets already exist
            if not_allowed:
                logger.error(f"PAT secret(s) already exists: {', '.join(not_allowed)}")
                raise ConflictError(f"PAT secret(s) already exists: {', '.join(not_allowed)}")
            
        else:
            logger.error("Unsupported server type")

        logger.info(f"Server added successfully: {request.server_name}")
        return ServiceResponse.success("Server added successfully", 201)

    def add_tableau_server(self, request: AddTableauServerRequest, user: User, background_tasks: BackgroundTasks) -> ServiceResponse:
        """Adds a new TableauServerDetail after validation and authentication, and starts site discovery."""
        logger.info(f"Processing add TableauServerDetail request for server: {request.name}")
        
        # Force auth_type to PAT if server type is CLOUD
        if request.type == ServerType.CLOUD.value:
            request.auth_type = ServerAuthType.PAT

        # Only consider non-deleted servers for duplicate check
        organization_id = getattr(user, 'organization_id', None)
        user_id = getattr(user, 'id', None)
        if not isinstance(organization_id, uuid.UUID):
            raise ValueError("user.organization_id must be a UUID")
        if not isinstance(user_id, uuid.UUID):
            raise ValueError("user.id must be a UUID")
        existing_server = TableauServerDetailManager.get_server_by_name_or_url(organization_id, request.name, request.server_url)
        if existing_server and not getattr(existing_server, 'is_deleted', False):
            logger.error(f"Server already exists")
            raise ConflictError(f"Server already exists")

        # Validate credentials by attempting to sign in
        self._validate_tableau_credentials(request)

        # Add the server
        server_id = TableauServerDetailManager.add_server(
            organization_id,
            user_id,
            request.name,
            request.server_url,
            request.type,
            created_by=user.id,
            updated_by=user.id
        )
        
        # Add credentials based on auth_type
        if request.auth_type == ServerAuthType.CREDENTIALS:
            if not request.username or not request.password:
                raise BadRequestError("Username and password are required for credential authentication")
            hashed_password = bcrypt.hash(request.password)
            TableauServerCredentialManager.add_server_credential(
                server_id,
                request.auth_type,
                username=request.username,
                password=hashed_password,
                created_by=user.id,
                updated_by=user.id
            )
        elif request.auth_type == ServerAuthType.PAT:
            if not request.pat_name or not request.pat_secret:
                raise BadRequestError("PAT name and secret are required for PAT authentication")
            hashed_pat_secret = bcrypt.hash(request.pat_secret)
            TableauServerCredentialManager.add_server_credential(
                server_id,
                request.auth_type,
                pat_name=request.pat_name,
                pat_secret=hashed_pat_secret,
                created_by=user.id,
                updated_by=user.id
            )

        # Schedule site discovery as a background task
        background_tasks.add_task(self.site_discovery_process, server_id, request, user)

        logger.info(f"TableauServerDetail added successfully: {request.name}")
        return ServiceResponse.success("Server added successfully", 201)

    def site_discovery_process(self, server_id, request, user):
        """
        Background task to fetch Tableau sites, projects, and workbooks,
        save them to the DB, and upload Tableau files to S3 at the new path.
        """
        # Authenticate to Tableau
        url = f"{request.server_url}/api/3.25/auth/signin"
        if request.auth_type == ServerAuthType.PAT:
            token, site_id, user_id, content_url, _ = self._sign_in(url, None, None, request.pat_name, request.pat_secret, "")
        else:
            token, site_id, user_id, content_url, _ = self._sign_in(url, request.username, request.password, None, None, "")

        organization_name = OrganizationDetailManager.get_name_by_id(user.organization_id)
        credentials_id = TableauServerCredentialManager.get_credential_id_by_server_id(server_id)

        # Only use the authenticated site
        sites = [{
            "id": site_id,
            "contentUrl": content_url
        }]

        for site in sites:
            # Add TableauSiteDetail
            if not isinstance(credentials_id, uuid.UUID):
                logger.error(f"Invalid credentials_id for TableauSiteDetail: {credentials_id}")
                continue
            site_name = site["contentUrl"]
            site_id_val = site["id"]
            # site_id_val should be a UUID, try to convert if it's a string
            if isinstance(site_id_val, str):
                try:
                    site_id_val = uuid.UUID(site_id_val)
                except Exception:
                    logger.error(f"Invalid site_id for TableauSiteDetail: {site_id_val}")
                    continue
            if not isinstance(site_name, str) or not isinstance(site_id_val, uuid.UUID):
                logger.error(f"Invalid site_name or site_id for TableauSiteDetail: {site_name}, {site_id_val}")
                continue
            site_detail = TableauSiteDetailManager.add_site_detail(
                id=uuid.uuid4(),
                credentials_id=credentials_id,
                site_name=site_name,
                site_id=site_id_val,
                created_by=user.id,
                updated_by=user.id
            )

            # Sign in to this site (already signed in, but for consistency)
            pat_name = getattr(request, "pat_name", "") or ""
            pat_token = getattr(request, "pat_secret", "") or ""
            site_str = site["contentUrl"] or ""
            client = TableauClient(
                server_id=str(server_id),
                server_url=request.server_url,
                pat_name=pat_name,
                pat_token=pat_token,
                site=site_str
            )
            client.token = token
            client.site_id = site["id"]
            client.site_name = site["contentUrl"]

            # Fetch projects for this site
            projects = client.get_projects()
            project_id_map = {}

            for project in projects:
                new_uuid = uuid.uuid4()
                project_id_map[project["project_id"]] = new_uuid

            for project in projects:
                new_uuid = project_id_map[project["project_id"]]
                tableau_parent_id = project.get("parent_project_id")
                parent_id = project_id_map.get(tableau_parent_id) if tableau_parent_id else None

                ProjectDetailManager.add_project(
                    id=new_uuid,
                    name=project["project_name"],
                    site_id=site_detail.id,
                    server_id=server_id,
                    user_id=user.id,
                    parent_id=parent_id,
                    created_by=user.id,
                    updated_by=user.id
                )

            # Fetch workbooks for this site
            import asyncio
            workbooks = asyncio.run(client.get_workbooks(organization_name))
            for wb in workbooks:
                new_report_uuid = uuid.uuid4()
                project_id = project_id_map.get(wb["location_id"])
                ReportDetailManager.add_report(
                    id=new_report_uuid,
                    name=wb["workbook_name"],
                    report_id=wb["workbook_id"],
                    project_id=project_id,
                    created_by=user.id,
                    updated_by=user.id
                )
                # Download and upload workbook file from Tableau (handles S3 and cleanup)
                asyncio.run(client.download_and_upload_workbook(wb["workbook_id"], organization_name, wb["workbook_name"]))
                # No extra S3 upload or os.remove here

        logger.info(f"Site discovery process completed for server_id={server_id}, organization={organization_name}")

    def _validate_tableau_credentials(self, request: AddTableauServerRequest):
        """Validates Tableau server credentials by attempting to sign in."""
        logger.info(f"Validating credentials for server: {request.server_url}")
        
        url = f"{request.server_url}/api/{TABLEAU_VERSION}/auth/signin"
        
        if request.auth_type == ServerAuthType.CREDENTIALS:
            if not request.username or not request.password:
                raise BadRequestError("Username and password are required")
            self._sign_in(url, request.username, request.password, None, None, "")
        elif request.auth_type == ServerAuthType.PAT:
            if not request.pat_name or not request.pat_secret:
                raise BadRequestError("PAT name and secret are required")
            self._sign_in(url, None, None, request.pat_name, request.pat_secret, "")
        else:
            raise BadRequestError("Invalid authentication type")

    async def process_update_site_discovery(self, server_id: uuid.UUID) -> None:
        """Processes the update of site discovery."""
        server_id_str = str(server_id)

        file_name = f"{server_id}.{FILE_TYPE}"
        s3_path = os.path.join(CLOUD_SITE_DISCOVERY_DIR, file_name)
        local_path = os.path.join(LOCAL_DIR, file_name)

        await self._process_and_update_file(
            s3_object_path=s3_path,
            local_file_path=local_path,
            exclude_key="server_id",
            exclude_value=server_id_str,
        )

    async def _process_and_update_file(
        self,
        s3_object_path: str,
        local_file_path: str,
        exclude_key: str,
        exclude_value: str,
    ) -> None:
        """Downloads, filters, uploads, and cleans up a file from S3."""
        if not await self.s3.check_file_exists(s3_object_path):
            return
        try:
            await self.s3.download_file(s3_object_path, local_file_path)

            async with aiofiles.open(local_file_path, "r") as f:
                data = json.loads(await f.read())

            filtered_data = [item for item in data if item.get(exclude_key) != exclude_value]

            async with aiofiles.open(local_file_path, "w") as f:
                await f.write(json.dumps(filtered_data, indent=4))

            await self.s3.upload_to_s3(file_path=local_file_path, object_name=s3_object_path)

        finally:
            if os.path.exists(local_file_path):
                os.remove(local_file_path)

    async def delete_server(self, server_id: uuid.UUID) -> ServiceResponse:
        """Deletes (soft-deletes) a server and all related records."""
        logger.info(f"Processing delete server request for server ID: {server_id}")
        
        server = ServerDetailsManager.get_server_by_id(server_id)
        
        if not server:
            logger.error(f"Server not found: {server_id}")
            raise NotFoundError("Server not found")
        
        if server.server_type == ServerType.ONPREMISE.value:
            ONServerDetailsManager.delete_on_premise_server(server_id)
            
        else:
            CloudServerDetailsManager.delete_cloud_server_id(server_id)
            self.site_discovery_manager.delete(server_id)
            if self.background_task:
                await self.process_update_site_discovery(server_id)

        ReportDetailsManager.delete_reports(server_id)
        ServerDetailsManager.delete_server(server_id)
        
        logger.info(f"Server deleted successfully: {server_id}")
        return ServiceResponse.success("Server deleted successfully")

    def update_server(self, request: UpdateServerRequest, user: UserOld) -> ServiceResponse:
        """Updates an existing server."""
        
        logger.info(f"Processing update server request for server ID: {request.server_id}")
        
        server = ServerDetailsManager.get_server_by_id(request.server_id)
        
        if not server:
            logger.error(f"Server not found: {request.server_id}")
            raise NotFoundError("Server not found")
        
        if server.server_type != request.server_type.value:
            logger.error("Server type cannot be changed")
            raise ConflictError("Server type cannot be changed")
        
        on_premise_server = ONServerDetailsManager.get_on_premise_server_by_id(request.server_id)
        
        if on_premise_server:
            if on_premise_server.server_auth_type != request.server_auth_type.value:
                logger.error("Server authentication type cannot be changed")
                raise ConflictError("Server authentication type cannot be changed")
        
        ServerDetailsManager.update_server(
            request.server_id,
            request.server_name,
            request.server_url,
            user.id
        )
        
        if request.server_type == ServerType.ONPREMISE.value:
            update_data: dict = {
                "username": request.username,
                "password": request.password,
                "pat_name": request.pat_name,
                "pat_secret": request.pat_secret,
                "updated_by": user.id
            }
            
            ONServerDetailsManager.update_on_premise_server(
                request.server_id,
                update_data
            )
            
        elif request.server_type == ServerType.CLOUD.value:
            
            cloud_servers = CloudServerDetailsManager.get_cloud_server_by_id(request.server_id)
            pat_names = [credential.pat_name for credential in cloud_servers]
            remove_pat_names = set(pat_names) - set([credential.pat_name for credential in request.server_credentials])
            
            for credential in request.server_credentials:
                CloudServerDetailsManager.update_cloud_server(
                    request.server_id,
                    credential.pat_name,
                    credential.pat_secret,
                    user.id
                )
                
            for pat_name in remove_pat_names:
                CloudServerDetailsManager.delete_cloud_server(
                    request.server_id,
                    pat_name
                )

            ServerDetailsManager.update_site_count(
                request.server_id,
            len(server.site_count) - len(remove_pat_names)
            )
                
        logger.info(f"Server updated successfully: {request.server_id}")
        return ServiceResponse.success("Server updated successfully")

    def get_server_data(self, server: ServerDetails) -> dict:
        
        on_premise_server = ONServerDetailsManager.get_on_premise_server_by_id(server.id)
        
        cloud_server = CloudServerDetailsManager.get_cloud_server_by_id(server.id)

        logger.info(f"Server details fetched successfully for server ID: {server.id}")
        
        # Constructing response
        response_data = {
            "id": str(server.id),
            "server_name": server.server_name,
            "server_url": server.server_url,
            "status": server.status.value,
            "server_type": server.server_type.value,
            "server_credentials": [],
            "server_auth_type": None,
            "username": None,
            "password": None,
            "pat_name": None,
            "pat_secret": None,
        }

        if server.server_type == ServerType.ONPREMISE.value and on_premise_server:
            response_data.update({
                "server_auth_type": on_premise_server.server_auth_type.value,
                "username": on_premise_server.username,
                "password": on_premise_server.password,
                "pat_name": on_premise_server.pat_name,
                "pat_secret": on_premise_server.pat_secret
            })

        elif server.server_type == ServerType.CLOUD.value and cloud_server:
            response_data["server_credentials"] = [
                {"pat_name": cred.pat_name, "pat_secret": cred.pat_secret} for cred in cloud_server
            ]
        return response_data
    
    def get_server(self, server_id: uuid.UUID) -> ServiceResponse:
        """Fetches details of a server."""
        
        logger.info(f"Fetching server details for server ID: {server_id}")
        server = ServerDetailsManager.get_server_by_id(server_id)
        
        if not server:
            logger.error(f"Server not found: {server_id}")
            raise NotFoundError("Server not found")
        
        data = self.get_server_data(server)

        logger.info(f"Server details fetched successfully for server ID: {server_id}")
        return ServiceResponse.success(data)
    
    def get_servers(self, page: int, page_size: int) -> ServiceResponse:
        """Fetch servers with pagination using Pydantic ORM conversion."""
        
        total_servers = ServerDetailsManager.get_total_servers()
        offset = (page - 1) * page_size
        
        servers = ServerDetailsManager.get_servers(offset=offset, limit=page_size)
        data = []
        
        for server in servers:
            server_data = self.get_server_data(server)
            data.append(server_data)
            
        response_data = {
            "content": data,
            "total": total_servers,
            "page": page,
            "page_size": page_size if total_servers > page_size else total_servers,
            "total_pages": (total_servers + page_size - 1) // page_size,
        }
        
        logger.info(f"Fetched {len(data)} servers successfully.")
        return ServiceResponse.success(response_data)

    def get_org_servers(self, organization_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Fetch servers for a specific organisation with pagination using the new TableauServerDetail model."""
        
        total_servers = TableauServerDetailManager.get_total_servers_by_org_id(organization_id)
        offset = (page - 1) * page_size
        
        servers = TableauServerDetailManager.get_servers_by_org_id(organization_id, offset=offset, limit=page_size)
        data = []
        
        for server in servers:
            server_data = {
                "id": str(server.id),
                "name": server.name,
                "server_url": server.server_url,
                "status": server.status.value,
            }
            data.append(server_data)
            
        response_data = {
            "content": data,
            "total": total_servers,
            "page": page,
            "page_size": page_size if total_servers > page_size else total_servers,
            "total_pages": (total_servers + page_size - 1) // page_size,
        }
        
        logger.info(f"Fetched {len(data)} servers for organisation {organization_id} successfully.")
        return ServiceResponse.success(response_data)
    
    def update_server_status(self, request: UpdateServerStatusRequest) -> ServiceResponse:
        """Updates the status of a server."""
        logger.info(f"Processing update server status request for server ID: {request.server_id}")
        server = ServerDetailsManager.get_server_by_id(request.server_id)
        
        if not server:
            logger.error(f"Server not found: {request.server_id}")
            raise NotFoundError("Server not found")

        ServerDetailsManager.update_server_status(request.server_id, request.status)
        
        logger.info(f"Server status updated successfully: {request.server_id}")
        return ServiceResponse.success("Server status updated successfully")

    def update_tableau_server_status(self, request: UpdateServerStatusRequest) -> ServiceResponse:
        """Updates the status of a TableauServerDetail."""
        logger.info(f"Processing update TableauServerDetail status request for server ID: {request.server_id}")
        
        server = TableauServerDetailManager.get_server_by_id(request.server_id)
        
        if not server:
            logger.error(f"TableauServerDetail not found: {request.server_id}")
            raise NotFoundError("Server not found")

        TableauServerDetailManager.update_server_status(request.server_id, request.status)
        
        logger.info(f"TableauServerDetail status updated successfully: {request.server_id}")
        return ServiceResponse.success("Server status updated successfully")

    def soft_delete_tableau_server(self, request: DeleteServerRequest) -> ServiceResponse:
        """Soft deletes a TableauServerDetail by setting is_deleted=True."""
        server = TableauServerDetailManager.get_server_by_id(request.server_id)
        if not server:
            raise NotFoundError("Server not found")
        TableauServerDetailManager.soft_delete_server(request.server_id)
        return ServiceResponse.success("Server deleted successfully")

    def get_user_projects(self, user_id: uuid.UUID, page: int, page_size: int) -> ServiceResponse:
        """Fetch projects for a specific user with pagination using the new ProjectDetail model."""
        from app.models.project_details import ProjectDetailManager
        total_projects = ProjectDetailManager.get_total_projects_by_user_id(user_id)
        offset = (page - 1) * page_size
        projects = ProjectDetailManager.get_projects_by_user_id(user_id, offset=offset, limit=page_size)
        data = [
            {
                "id": str(project.id),
                "name": project.name,
                # Add other fields as needed
            }
            for project in projects
        ]
        response_data = {
            "content": data,
            "total": total_projects,
            "page": page,
            "page_size": page_size if total_projects > page_size else total_projects,
            "total_pages": (total_projects + page_size - 1) // page_size,
        }
        logger.info(f"Fetched {len(data)} projects for user {user_id} successfully.")
        return ServiceResponse.success(response_data)
