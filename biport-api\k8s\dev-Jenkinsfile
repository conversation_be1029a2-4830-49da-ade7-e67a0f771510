pipeline {
    agent { label 'Jenkins-slave' }

    environment {
        AWS_REGION = 'ap-south-1'
        AWS_ACCOUNT_ID = '************'
        ECR_REPO = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/dev-biport"
        WORKSPACE_DIR = "/home/<USER>/jenkins/jenkins/workspace/dev-biport-backend"
    }
 
    stages {
        stage('Login to AWS ECR') {
            steps {
                script {
                    echo "Logging into AWS ECR..."
                    withAWS(credentials: 'Jenkins-Aws-Credentials', region: "${AWS_REGION}") {
                        echo "Logging into AWS ECR2..."
                        sh "aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REPO}"
                    }
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                script {
                    echo "Changing to Jenkins workspace directory..."
                    sh "cd ${WORKSPACE_DIR}"

                    echo "Building Docker image..."
                    sh "docker build -t dev-biport-backend ."
                }
            }
        }

        stage('Tag Docker Image') {
            steps {
                script {
                    def DATETIME_TAG = new Date().format("yyyy-MM-dd_HH-mm-ss")
                    echo "Generated Image Tag: ${DATETIME_TAG}"

                    echo "Tagging Docker image..."
                    sh """
                        docker tag dev-biport-backend ${ECR_REPO}:backend-latest
                        docker tag dev-biport-backend ${ECR_REPO}:backend-${DATETIME_TAG}
                    """
                    env.DATETIME_TAG = DATETIME_TAG
                }
            }
        }

        stage('Push Docker Image to ECR') {
            steps {
                script {
                    echo "Pushing Docker images to AWS ECR..."
                    sh """
                        docker push ${ECR_REPO}:backend-latest
                        docker push ${ECR_REPO}:backend-${DATETIME_TAG}
                    """
                }
            }
        }

        stage('Cleanup Local Docker Images') {
            steps {
                script {
                    echo "Removing local Docker images..."
                    sh """
                        docker rmi -f dev-biport-backend
                        docker rmi -f ${ECR_REPO}:backend-latest
                        docker rmi -f ${ECR_REPO}:backend-${DATETIME_TAG}
                    """
                }
            }
        }

        stage('Apply Kubernetes Manifest') {
            steps {
                script {
                    echo "Applying Kubernetes manifest..."
                    withAWS(credentials: 'Jenkins-Aws-Credentials', region: "${AWS_REGION}") {
                        echo "Updating kubeconfig for EKS..."
                        sh """
                            aws eks update-kubeconfig --region ${AWS_REGION} --name tax-litigation
                            kubectl config get-contexts
                            kubectl apply -f k8s/dev-manifest.yaml
                            kubectl rollout restart deployment/dev-biport-api -n dev-biport
                        """
                    }
                }
            }
        }
    }
}
