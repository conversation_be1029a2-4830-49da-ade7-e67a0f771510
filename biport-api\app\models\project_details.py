import uuid
from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, cast, text, or_, case
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Query, joinedload
from app.core.session import Base,scoped_context
from app.models.base import AuditMixin
from app.models.users import User, Role
from app.core.session import scoped_context, Base
from app.core.enums import RoleEnum
# from app.models import Role


class ProjectDetail(Base, AuditMixin):
    __tablename__ = "project_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    is_upload = Column(Boolean, server_default=text("false"), nullable=False)

    site_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_site_details.id"), nullable=True)
    server_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_server_details.id"), nullable=True)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.project_details.id"), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=False)
    assigned_to = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=True)

    creator = relationship("User", foreign_keys=[user_id], back_populates="created_projects")
    assignee = relationship("User", foreign_keys=[assigned_to], back_populates="assigned_projects")
    reports = relationship("ReportDetail", back_populates="project")

    @staticmethod
    def get_projects_by_user_role(session, user, role_name: str) -> Query:
        """
        Return a SQLAlchemy query for projects based on user's role and organization.
        """
        query = session.query(ProjectDetail).options(joinedload(ProjectDetail.reports))

        # Filter by organization
        query = query.join(ProjectDetail.creator).filter(User.organization_id == user.organization_id)

        #Role-based access control
        if role_name == "Admin":
            pass  # See all
        elif role_name == "Manager":
            subordinate_ids = session.query(User.id).filter(User.manager_id == user.id).all()
            subordinate_ids = [sid[0] for sid in subordinate_ids]
            query = query.filter(
                or_(
                    ProjectDetail.assigned_to == user.id,
                    ProjectDetail.assigned_to.in_(subordinate_ids)
                )
            )
        elif role_name == "Developer":
            query = query.filter(ProjectDetail.assigned_to == user.id)

        return query
        


class ProjectDetailManager:
    @staticmethod
    def get_all_root_projects(page: int, page_size: int):
        from app.models.report_details import ReportDetail
        offset = (page - 1) * page_size
        with scoped_context() as session:
            return (
                session.query(ProjectDetail)
                .filter(
                    ProjectDetail.is_upload == True,
                )
                .order_by(ProjectDetail.updated_at.desc())
                .offset(offset)
                .limit(page_size)
                .all()
            )
        
    @staticmethod
    def assign_user_to_project(project_id: UUID, user_id: UUID):
        with scoped_context() as session:
            project = session.query(ProjectDetail).filter(
                ProjectDetail.id == project_id,
                ProjectDetail.is_deleted == False
            ).first()

            if not project:
                return None

            project.assigned_to = user_id
            session.commit()
            session.refresh(project)
            return project      
        
    @staticmethod
    def get_projects_by_site(site_id: UUID):
        from app.models.report_details import ReportDetail
        with scoped_context() as session:
            admin_user = (
                session.query(User)
                .filter(User.role.has(name=RoleEnum.ADMIN.value), User.is_deleted == False)
                .first()
            )
            admin_id = admin_user.id if admin_user else None

            root_projects = (
                session.query(ProjectDetail)
                .filter(
                    ProjectDetail.site_id == site_id,
                    ProjectDetail.parent_id == None,
                    ProjectDetail.is_deleted == False
                )
                .all()
            )

            project_ids = [p.id for p in root_projects]

            sub_projects = (
                session.query(ProjectDetail)
                .filter(
                    ProjectDetail.parent_id.in_(project_ids),
                    ProjectDetail.is_deleted == False
                )
                .all()
            )

            for sp in sub_projects:
                if not sp.assigned_to:
                    sp.assigned_to = admin_id

            sub_project_ids = [sp.id for sp in sub_projects]

            reports = (
                session.query(ReportDetail)
                .filter(
                    ReportDetail.project_id.in_(sub_project_ids),
                    ReportDetail.is_deleted == False
                )
                .all()
            )

            return root_projects, sub_projects, reports
    
    @staticmethod
    def get_projects_by_parent(parent_id: UUID):
        from app.models.report_details import ReportDetail
        with scoped_context() as session:
            admin_user = session.query(User).join(User.role).filter(Role.name == RoleEnum.ADMIN).first()
            admin_id = admin_user.id if admin_user else None

            sub_projects = session.query(
                ProjectDetail.id,
                ProjectDetail.name,
                ProjectDetail.parent_id,
                case(
                    (ProjectDetail.assigned_to == None, admin_id),
                    else_=ProjectDetail.assigned_to
                ).label("assigned_to")
            ).filter(
                ProjectDetail.parent_id == parent_id,
                ProjectDetail.is_deleted == False
            ).all()

            sub_project_ids = [sp.id for sp in sub_projects]

            reports = []
            if sub_project_ids:
                reports = session.query(
                    ReportDetail.id,
                    ReportDetail.name,
                    ReportDetail.project_id,
                    ReportDetail.view_count 
                ).filter(
                    ReportDetail.project_id.in_(sub_project_ids),
                    ReportDetail.is_deleted == False
                ).all()

            return sub_projects, reports

    @staticmethod
    def get_projects_by_user_id(user_id: uuid.UUID, offset: int = 0, limit: int = 10):
        from app.models.project_details import ProjectDetail
        with scoped_context() as session:
            return session.query(ProjectDetail).filter(
                ProjectDetail.user_id == user_id,
                ProjectDetail.is_deleted == False
            ).order_by(ProjectDetail.updated_at.desc()).offset(offset).limit(limit).all()

    @staticmethod
    def get_total_projects_by_user_id(user_id: uuid.UUID) -> int:
        from app.models.project_details import ProjectDetail
        with scoped_context() as session:
            return session.query(ProjectDetail).filter(
                ProjectDetail.user_id == user_id,
                ProjectDetail.is_deleted == False
            ).count()         

    @staticmethod
    def add_project(id, name, site_id, server_id, user_id, parent_id=None, created_by=None, updated_by=None):
        from app.models.project_details import ProjectDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            project = ProjectDetail(
                id=id,
                name=name,
                site_id=site_id,
                server_id=server_id,
                user_id=user_id,
                parent_id=parent_id,
                created_by=created_by,
                updated_by=updated_by
            )
            session.add(project)
            session.commit()
            session.refresh(project)
            return project         

    @staticmethod
    def soft_delete_by_server_id(server_id):
        from app.models.project_details import ProjectDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            session.query(ProjectDetail).filter_by(server_id=server_id).update({"is_deleted": True})
            session.commit()

    @staticmethod
    def get_ids_by_server_id(server_id):
        from app.models.project_details import ProjectDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            return [p.id for p in session.query(ProjectDetail).filter_by(server_id=server_id).all()]
