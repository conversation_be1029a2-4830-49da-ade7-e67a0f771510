"""
This module defines the ReportDetails model for use with SQLAlchemy ORM.
It represents metadata about individual reports, including their processing status
and association with a server.
"""

import uuid
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON>olean, Foreign<PERSON>ey, Integer, text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core import Base, scoped_context, logger
from app.core.exceptions import BadRequestError



class ReportDetails(Base):
    """
    SQLAlchemy ORM model representing the details of a report.

    Attributes:
        id (str): Primary key, a UUID string uniquely identifying each report.
        report_name (str): The name of the report.
        is_analised (bool): Flag indicating whether the report has been analyzed.
        is_converted (bool): Flag indicating whether the report has been converted.
        is_migrated (bool): Flag indicating whether the report has been migrated.
        serverid (str): Foreign key linking to the associated server.
        server (ServerDetails): Relationship to the ServerDetails model.
    """
    __tablename__ = "server_report_details"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    report_name = Column(String, nullable=False)
    is_analyzed = Column(Boolean, default=False)
    is_converted = Column(Boolean, default=False)
    is_migrated = Column(Boolean, default=False)
    server_id = Column(UUID, ForeignKey("server_details.id"), nullable=True)   
    workbook_id = Column(UUID, nullable=True, unique=True)
    dashboard_count = Column(Integer, nullable=False, server_default=text('0'))
    worksheet_count = Column(Integer,nullable=False, server_default=text('0'))
    calc_count = Column(Integer, nullable=False, server_default=text('0'))
    datasource_count = Column(Integer, nullable=False, server_default=text('0'))

    
class ReportDetailsManager:
    @staticmethod
    def get_workbook_id(workbook_id: UUID) -> ReportDetails:
        """
        Retrieve a ReportDetails record by workbook ID.

        Parameters
        ----------
            The ID of the workbook to retrieve.

        Returns
        -------
            The ReportDetails instance if found, otherwise None.
        """
        try:
            with scoped_context() as session:
                return session.query(ReportDetails).filter(ReportDetails.workbook_id == workbook_id).first()
        except Exception as e:
            logger.exception(f"{workbook_id} ID not found in database : {e}")
            return None
    
      
    @staticmethod  
    def mark_analysed(workbook_id: int):
        """
        Mark a workbook as migrated by setting its `is_migrated` flag to True.
 
        Parameters
        ----------
        workbook_id : int
            The ID of the workbook to update.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(ReportDetails).filter(ReportDetails.workbook_id == workbook_id).first()
                if db_report:
                    db_report.is_analyzed = True
                    session.commit()
        except Exception as e:
            logger.exception(f"Failed to mark workbook ID {workbook_id} as analyzed: {e}")

    @staticmethod
    def mark_update_counts(workbook_id: UUID, dashboard_count: int, calc_count: int, datasource_count: int, worksheet_count: int ) -> None:
        """
        Update the dashboard_count for the given workbook_id in the database.

        Parameters
        ----------
        workbook_id : str
            The ID of the workbook to update.

        count : int
            The number of dashboards to set.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(ReportDetails).filter(
                    ReportDetails.workbook_id == workbook_id
                ).first()
                if db_report:
                    db_report.dashboard_count = dashboard_count
                    db_report.calc_count = calc_count
                    db_report.datasource_count = datasource_count
                    db_report.worksheet_count = worksheet_count

                    session.commit()

        except Exception as e:
            logger.exception(f"Failed to update count values for {workbook_id}: {e}")
       
    @staticmethod  
    def mark_converted(workbook_id: int):
        """
        Mark a workbook as migrated by setting its `is_migrated` flag to True.
 
        Parameters
        ----------
        workbook_id : int
            The ID of the workbook to update.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(ReportDetails).filter(ReportDetails.workbook_id == workbook_id).first()
                if db_report:
                    db_report.is_converted = True
                    session.commit()
        except Exception as e:
            logger.exception(f"Failed to mark workbook ID {workbook_id} as converted: {e}")

    @staticmethod   
    def mark_migrated(workbook_id: int):
        """
        Mark a workbook as migrated by setting its `is_migrated` flag to True.

        Parameters
        ----------
            The ID of the workbook to update.
        """
        try:
            with scoped_context() as session:
                db_report = session.query(ReportDetails).filter(ReportDetails.workbook_id == workbook_id).first()
                if db_report:
                    db_report.is_migrated = True
                    session.commit()
        except Exception as e:
            logger.exception(f"Failed to mark workbook ID {workbook_id} as migrated: {e}")

    @staticmethod
    def bulk_insert_reports(report_entries: list[ReportDetails]):
        """
        Bulk insert report details into the database, skipping duplicates.
        """
        try:
            with scoped_context() as session:
                incoming_ids = [r.workbook_id for r in report_entries]
                existing_ids = set(
                    str(row[0]) for row in session.query(ReportDetails.workbook_id)
                    .filter(ReportDetails.workbook_id.in_(incoming_ids))
                    .all()
                )

                # Filter out duplicates
                new_entries = [r for r in report_entries if r.workbook_id not in existing_ids]

                if new_entries:
                    session.bulk_save_objects(new_entries)
                    session.commit()
        except Exception as e:
            logger.exception(f"Bulk insert failed for report details: {e}")    
    
    @staticmethod
    def delete_reports(server_id: UUID):
        """
        Delete all report details associated with a specific server ID.

        Parameters
        ----------
            server_id: The ID of the server whose reports should be deleted.
        """
        try:
            with scoped_context() as session:
                session.query(ReportDetails).filter(ReportDetails.server_id == server_id).delete()
                session.commit()
        except Exception as e:
            logger.exception(f"Failed to delete reports for server ID {server_id}: {e}")

    @staticmethod
    def get_server_reports_details(user_id: int):
        """Fetches server reports details associated with a user."""
        with scoped_context() as session:
            report_details = session.query(ReportDetails.dashboard_count,
                                            ReportDetails.worksheet_count).all()
            return report_details
