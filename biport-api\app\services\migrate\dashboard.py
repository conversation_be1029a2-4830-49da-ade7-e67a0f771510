from .core import get_tables_data, find_zones_by_name, calculate_dimensions, get_border_config
from .visuals.text_box import get_text_box_json_only_in_dashboards
from .visuals import get_title_textbox, get_slicer_report
from .worksheet import get_config_json, get_worksheet_names
from app.core import logger

def get_dashboard_json(data,chart_types):
    logger.info(f"===Started dashboard migration=======")
    dashboard_result = []
    dashboards = data.get('workbook',{}).get('dashboards',{}).get('dashboard')
    table_column_data = get_tables_data(data)
    if dashboards:
        dashboards = dashboards if isinstance(dashboards, list) else [dashboards]
        for dashboard in dashboards:
            dashboard_visual = []
            dashboard_name = dashboard.get('@name')
            dashboard_style = dashboard.get('style')
            dashboard_title_layout = dashboard.get('layout-options',{}).get('title',{}).get('formatted-text',{}).get('run')
            if dashboard_title_layout:
                dashboard_title_result = get_title_textbox(dashboard_title_layout,dashboard_style, is_dashboard = True)
                if dashboard_title_result:
                    dashboard_visual.extend(dashboard_title_result)

            slicer_result = get_slicer_report(dashboard, table_column_data)
            if slicer_result:
                dashboard_visual.extend(slicer_result)

            worksheet_data = get_config_json(data,chart_types) 
            worksheets = data.get('workbook').get('worksheets').get('worksheet')
            worksheets = worksheets if isinstance(worksheets, list) else [worksheets]
            zones = dashboard.get("zones")
            worksheet_names = get_worksheet_names(worksheets)
            dashboard_reports = find_zones_by_name(zones, worksheet_names)
            for visual in dashboard_reports:
                worksheet_name = visual.get("@name")
                visual_content = worksheet_data.get(worksheet_name)
                dimensions = calculate_dimensions(visual.get("@x"), visual.get("@y"),visual.get("@h"), visual.get("@w"))
                zones_format = visual.get("zone-style",{}).get("format")
                if visual_content:
                    if isinstance(visual_content, list):
                        for item in visual_content:
                            template = item.get("template")
                            config = item.get("config")
                            border_list = get_border_config(zones_format)
                            config["border_list"] = border_list
                            result = template.format(**config, **dimensions)
                            dashboard_visual.append({"config": result, "filters":"[]", **dimensions})
                            # report_name_uuid_name_in_power_bi=get_name_of_report_name_id(result)


                    else:
                        card_visual = worksheet_data.get(worksheet_name).get("visual_content")
                        no_of_cards = len(card_visual)
                        base_x = dimensions.get("x", 0)
                        base_y = dimensions.get("y", 0)
                        base_width = dimensions.get("width", 1000)
                        base_height = dimensions.get("height", 80)
                        margin = 10

                        # Calculate card width
                        card_width = (base_width - (margin * (no_of_cards - 1))) / no_of_cards
                        card_config_list = []
                        card_title = worksheet_data.get(worksheet_name).get("card_title")
                        if card_title:
                            card_title_template = card_title.get("template")
                            card_title_config = card_title.get("config")
                            card_title_dimensions = {
                                "x": base_x,
                                "y": base_y,
                                "width": base_width,
                                "height": 50,
                                "z":0.0
                            }
                            card_title_result = card_title_template.format(**card_title_config, **card_title_dimensions)
                            card_config_list.append({"config": card_title_result, "filters":"[]", **card_title_dimensions})
                        for idx, card_content in enumerate(card_visual):
                            card_template = card_content.get("template")
                            card_config = card_content.get("config")
                            card_x = base_x + idx * (card_width + margin)
                            card_y = base_y + 50
                            card_dimensions = {
                                "x": card_x,
                                "y": card_y,
                                "width": card_width,
                                "height": base_height,
                                "z":0.0
                            }
                            card_result = card_template.format(**card_config, **card_dimensions)
                            card_config_list.append({"config": card_result, "filters":"[]", **card_dimensions})
                        dashboard_visual.extend(card_config_list)
                for text_box_zone in get_text_box_json_only_in_dashboards(zones.get("zone")):
                    dashboard_visual.append(text_box_zone.get("visual_container"))   
            dashboard_result.append({"dashboard_name": dashboard_name, "visual_container": dashboard_visual})
    # print('=============dashboard data================')
    # print(json.dumps(dashboard_result))
    return dashboard_result