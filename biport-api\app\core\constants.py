from pathlib import Path

S3_BASE_PATH = "BI-PortV3/{organization_name}/{s3_report_id}"
REPORT_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/tableau_file"
ANALYZED_OUTPUTS_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/ANALYZED_OUTPUTS_DIR"
CONVERTED_OUTPUTS_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/CONVERTED_OUTPUTS_DIR"
MIGRATE_OUTPUTS_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/MIGRATE_OUTPUT_DIR"

ANALYSIS_REPORT_TYPE = "analyzed_files"
ANALYZED_OUTPUTS_DIR = "analyzed_outputs"
CONVERTED_OUTPUTS_DIR = "converted_outputs"
MIGRATE_OUTPUT_DIR = "migrate_outputs"

ANALYZED_WORKBOOK_FILE_PATH = "{workbook_name}_report_{prefix}"
ANALYZED_S3_FILE_PATH = "{workbook_name}_report"
ANALYZED_S3_ZIP_PATH = "{WORKBOOKS_PATH}/{ANALYZED_OUTPUTS_DIR}/{workbook_id}.zip"
 

AUTOMATIC_MIGRATED_CHARTS = [
    "SymbolMap", "Text", "filledMap", "Pie", "Bar", "HorizontalBar", "VerticalBar",
    "StackedVerticalBar", "StackedHorizontalBar", "TreeMap", "Cards", "TextBox",
    "Bar & Area", "Bar & Line", "ScatterPlot", "HighlightedTable", "PivotTable",
    "Slicer", "Card or TextTable", "Area", "Line", "Square", "TextTable"
]

BACKGROUND_TASK = "BACKGROUND_TASK"

BLACKLIST_PREFIX = "blacklist:jwt:"

BLOCKED_EMAILS = [
    "gmail.com", "hotmail.com", "ymail.com", "yahoo.com", "yopmail.com",
    "outlook.com", "live.com", "msn.com", "aol.com", "icloud.com",
    "protonmail.com", "zoho.com", "yandex.com", "qq.com"
]

chunk_size = 20  # Made uppercase for consistency (optional)

ClOUD_WORKBOOKS_PATH = "My_workspace/workbooks/"

CLOUD_SITE_DISCOVERY_DIR = "Site_discoveries/"

COLOR_PALETTE_DATA = {
    "blue_10_0": {"Min": "#B9DDF1", "Mid": "#6798C1", "Max": "#2A5783"},
    "blue_teal_10_0": {"Min": "#BCE4D8", "Mid": "#45A2B9", "Max": "#2C5985"},
    "brown_10_0": {"Min": "#EEDBBD", "Mid": "#D18954", "Max": "#9F3632"},
    "gold_purple_diverging_10_0": {"Min": "#AD9024", "Mid": "#E3D8CF", "Max": "#AC7299"},
    "gray_10_0": {"Min": "#D5D5D5", "Mid": "#889296", "Max": "#49525E"},
    "gray_warm_10_0": {"Min": "#DCD4D0", "Mid": "#98908C", "Max": "#59504E"},
    "green_10_0": {"Min": "#B3E0A6", "Mid": "#5EA654", "Max": "#24693D"},
    "green_blue_diverging_10_0": {"Min": "#24693D", "Mid": "#CADAD2", "Max": "#2A5783"},
    "green_blue_white_diverging_10_0": {"Min": "#24693D", "Mid": "#FFFFFF", "Max": "#2A5783"},
    "green_gold_10_0": {"Min": "#F4D166", "Mid": "#60A656", "Max": "#146C36"},
    "orange_10_0": {"Min": "#FFC685", "Mid": "#ED6F20", "Max": "#9E3D22"},
    "orange_blue_diverging_10_0": {"Min": "#9E3D22", "Mid": "#D9D5C9", "Max": "#2B5C88"},
    "orange_blue_white_diverging_10_0": {"Min": "#9E3D22", "Mid": "#FFFFFF", "Max": "#2B5C8A"},
    "orange_gold_10_0": {"Min": "#F4D166", "Mid": "#EF701B", "Max": "#9E3A26"},
    "purple_10_0": {"Min": "#EEC9E5", "Mid": "#BC86A9", "Max": "#7C4D79"},
    "red_10_0": {"Min": "#FFBEB2", "Mid": "#F26250", "Max": "#AE123A"},
    "red_black_10_0": {"Min": "#AE123A", "Mid": "#D9D9D9", "Max": "#49525E"},
    "red_black_white_diverging_10_0": {"Min": "#AE123A", "Mid": "#FFFFFF", "Max": "#49525E"},
    "red_blue_diverging_10_0": {"Min": "#A90C38", "Mid": "#DFD4D1", "Max": "#2E5A87"},
    "red_blue_white_diverging_10_0": {"Min": "#A90C38", "Mid": "#FFFFFF", "Max": "#2E5A87"},
    "red_gold_10_0": {"Min": "#F4D166", "Mid": "#EE734A", "Max": "#B71D3E"},
    "red_green_diverging_10_0": {"Min": "#AE123A", "Mid": "#CED7C3", "Max": "#24693D"},
    "red_green_gold_diverging_10_0": {"Min": "#BE2A3E", "Mid": "#F4D166", "Max": "#22763F"},
    "red_green_white_diverging_10_0": {"Min": "#AE123A", "Mid": "#FFFFFF", "Max": "#24693D"},
    "sunrise_sunset_diverging_10_0": {"Min": "#33608C", "Mid": "#F6BA57", "Max": "#B81840"},
    "tableau-blue-light": {"Min": "#E5E5E5", "Mid": "#D5DFEC", "Max": "#C4D8F3"},
    "tableau-map-blue-green": {"Min": "#FEFFD9", "Mid": "#C4EAB1", "Max": "#41B7C4"},
    "tableau-map-temperatur": {"Min": "#529985", "Mid": "#DBCF47", "Max": "#C26B51"},
    "tableau-orange-blue-light": {"Min": "#FFCC96", "Mid": "#E5E5E5", "Max": "#C4D8F3"},
    "tableau-orange-light": {"Min": "#E5E5E5", "Mid": "#F5D9C2", "Max": "#FFCC9E"}
}

DATASOURCE_TYPES = {
    "excel-direct": "Excel",
    "hyper": "Hyper file",
    "snowflake": "Snowflake",
    "sqlproxy": "SQL Proxy",
    "sqlserver": "SQL Server",
    "textscan": "CSV or Text File"
}

DELETE = "DELETE"
FILE_TYPE = "json"
FLOW_FILE_NAME = "flow"
GET = "GET"
HTTP_STATUS_INTERNAL_ERROR = 500
HTTP_STATUS_OK = 200
INPUT_FILES_DIR = "input_files"
LIMIT_EXCEEDED = "You have exceeded the files limit"
LOCAL_DIR = "static"
LOCAL_DOWNLOAD_PATH = "./storage/{organization_name}/{user_email}/{process_id}/twb_files"
LOCAL_WORKBOOKS_DOWNLOAD_PATH = "./storage/My_workspace/workbooks/{workbook_id}/twb_files"
MAX_UPLOAD_RETRIES = 3
MIGRATE_OUTPUT_DIR = "My_workspace/workbooks/migrate_outputs"
MIGRATE_REPORT_TYPE = "migrated_files"
MSG_S3_DOWNLOAD_FAILED = "Failed to download file from S3."
MSG_S3_FETCH_ERROR = "Problem in fetching previous data."
OPENAI_PBI_PROMPT = """You are an expert in Tableau and Power BI. Convert each Tableau calculated field formula into its exact Power BI DAX equivalent.


Input:
- A list of Tableau formulas to be converted
- A dictionary of table names and their associated columns

Output:
Return only a JSON object in this format:
{
  "json_output": [
    {
      "cal_field": "<Tableau formula>",
      "powerbi_dax": "<Power BI DAX formula>"
    }
  ]
}

Guidelines:
- Maintain a one-to-one mapping for each formula
- Use column and table names exactly as provided
- Do not add aggregations unless they exist in the original
- Output each DAX formula as a single line
- Do not include comments, summaries, or any text outside the JSON object
"""
PATCH = "PATCH"

HTTP_STATUS_OK = 200
HTTP_STATUS_INTERNAL_ERROR = 500


# Local base directory for storage
STORAGE_BASE_DIR = Path("./storage")

# Path to download TWB files based on organization and user
LOCAL_DOWNLOAD_PATH = "./storage/{organization_name}/{user_email}/{process_id}/twb_files"

# Path to download TWB files for an individual workbook
LOCAL_WORKBOOKS_DOWNLOAD_PATH = "./storage/My_workspace/workbooks/{workbook_id}/twb_files"
LOCAL_S3_FILES_DOWNLOAD_PATH = "./storage/{workbook_id}/twb_files"

WORKBOOKS_PATH = "My_workspace/workbooks"

INPUT_FILES_DIR = "input_files"

POST = "POST"
POWER_BI_STRUCTURE = "My_workspace/workbooks/Semantic_model/{workbook_name}.zip"

# Path to store prep file output
PREP_FILE_OUTPUT_DIR = "Prep_file_outputs"

PARSED_FLOW_FILENAME = "parsed_flow.json"

PROJECTS_URL = "{server_url}/api/{version}/sites/{site_id}/projects"

RETRY_BACKOFF_BASE = 2

# S3 endpoint template
S3_ENDPOINT_FORMAT = "https://s3.{region}.amazonaws.com"
S3_INPUT_PATH = "{organization_name}/{user_email}/{process_id}/input_files"
S3_URL_EXPIRATION_SECONDS = 3600
S3_WORKBOOKS_PATH = "My_workspace/workbooks/{workbook_id}.twb"

# Duration (in seconds) that a pre-signed S3 URL remains valid
S3_URL_EXPIRATION_SECONDS = 3600
S3_PATH = "s3_path"

# S3 path where migrated Power BI ZIP files will be stored
# MIGRATE_OUTPUT_DIR = "My_workspace/workbooks/migrate_outputs"
MIGRATE_OUTPUT_DIR = "migrate_outputs"
MIGRATE_S3_ZIP_PATH = "BI-PortV3/{organization_name}/{s3_report_id}/{MIGRATE_OUTPUT_DIR}/{report_name}.zip"

# S3-related error messages
MSG_S3_DOWNLOAD_FAILED = "Failed to download file from S3."
MSG_S3_FETCH_ERROR = "Problem in fetching previous data."

# Limits & Validation Messages
LIMIT_EXCEEDED = "You have exceeded the files limit"
SIGN_IN_URL = "{server}/api/{version}/auth/signin"
SLICER_MODES = {
    "checklist": "Basic",
    "checkdropdown": "Dropdown",
    "dropdown": "Dropdown",
    "radiolist": "Basic"
}

STORAGE_BASE_DIR = Path("./storage")
TABLEAU_VERSION = '3.25'
TOO_MANY_FILES = "You are allowed for only {remaining_allowed_files} file(s) for migration"
WORKBOOK_DOWNLOAD_URL = "{server_url}/api/{version}/sites/{site_id}/workbooks/{workbook_id}/content"
WORKBOOK_URL = "{server_url}/api/{version}/sites/{site_id}/workbooks"
WORKBOOK_USAGE_STATISTICS_URL = "{server_url}/api/-/content/usage-stats/workbooks/{workbook_id}"
WORKBOOKS_PATH = "My_workspace/workbooks"
WORKBOOK_ID = "workbook_id"
XMLNS = {'t': 'http://tableau.com/api'}

TRUNCATED_DATE_TIME = {"tyr": ["yr"], "tqr": ["yr","qr"], "tmn":  ["yr","qr","mn"], "twk": ["yr","qr","mn"], "tdy": ["yr", "qr", "mn", "dy"]}
AGGREGATION_SELECTION = '{{"Aggregation": {{"Expression": {{"Column": {{"Expression": {{"SourceRef": {{"Source": \"{table_name}\"}}}},"Property": \"{column}\"}}}},"Function":{select_function}}},"Name": \"{query_ref}\","NativeReferenceName": \"{native_ref_name}\"}}'
DATE_HEIRARCHY_SELECTION = '{{"HierarchyLevel":{{"Expression":{{"Hierarchy":{{"Expression":{{"PropertyVariationSource":{{"Expression":{{"SourceRef":{{"Source":\"{table_name}\"}}}},"Name":"Variation","Property":\"{column}\"}}}},"Hierarchy":"Date Hierarchy"}}}},"Level":\"{date_level}\"}},"Name":\"{query_ref}\","NativeReferenceName":\"{column} {date_level}\"}}'
COLUMN_SELECTION = '{{"Column": {{"Expression": {{"SourceRef": {{"Source": \"{table_name}\"}}}},"Property": \"{column}\"}},"Name": \"{query_ref}\","NativeReferenceName": \"{native_ref_name}\"}}'
DATE_QUERY_REF = "{table_name}.{column_value}.Variation.Date Hierarchy.{date_level}"
AGGREGATION_QUERY_REF = "{aggregation}({table_name}.{column_value})"
COLUMNN_QUERY_REF = "{table_name}.{column_value}"
DATE_NATIVE_REFERENCE = "{column_value} {date_level}"
AGGREGATION_NATIVE_REFERENCE = "{aggregation} of {column_value}"


TABLEAU_WORKSHEET_DIMENSIONS = {
    "width": 1280.00,
    "height": 720.00,
    "x" : 0.00,
    "y" : 0.00,
    "z" : 0.00
}

LOCAL_DATE_TEMPLATE = '''table {local_date_table_ref}
	isHidden
	showAsVariationsOnly
	lineageTag: {table_lineage_tag}

	column Date
		dataType: dateTime
		isHidden
		lineageTag: {date_lineage_tag}
		dataCategory: PaddedDateTableDates
		summarizeBy: none
		isNameInferred
		sourceColumn: [Date]

		annotation SummarizationSetBy = User

	column Year = YEAR([Date])
		dataType: int64
		isHidden
		lineageTag: {year_lineage_tag}
		dataCategory: Years
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Year

	column MonthNo = MONTH([Date])
		dataType: int64
		isHidden
		lineageTag: {month_no_lineage_tag}
		dataCategory: MonthOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = MonthNumber

	column Month = FORMAT([Date], "MMMM")
		dataType: string
		isHidden
		lineageTag: {month_lineage_tag}
		dataCategory: Months
		summarizeBy: none
		sortByColumn: MonthNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Month

	column QuarterNo = INT(([MonthNo] + 2) / 3)
		dataType: int64
		isHidden
		lineageTag: {quarter_no_lineage_tag}
		dataCategory: QuarterOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = QuarterNumber

	column Quarter = "Qtr " & [QuarterNo]
		dataType: string
		isHidden
		lineageTag: {quarter_lineage_tag}
		dataCategory: Quarters
		summarizeBy: none
		sortByColumn: QuarterNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Quarter

	column Day = DAY([Date])
		dataType: int64
		isHidden
		lineageTag: {day_lineage_tag}
		dataCategory: DayOfMonth
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Day

	hierarchy 'Date Hierarchy'
		lineageTag: {hierarchy_lineage_tag}

		level Year
			lineageTag: {hierarchy_year_lineage_tag}
			column: Year

		level Quarter
			lineageTag: {hierarchy_quarter_lineage_tag}
			column: Quarter

		level Month
			lineageTag: {hierarchy_month_lineage_tag}
			column: Month

		level Day
			lineageTag: {hierarchy_day_lineage_tag}
			column: Day

		annotation TemplateId = DateHierarchy

	partition {local_date_table_ref} = calculated
		mode: import
		source = Calendar(Date(Year(MIN('{table_name}'[{column_name}])), 1, 1), Date(Year(MAX('{table_name}'[{column_name}])), 12, 31))

	annotation __PBI_LocalDateTable = true
'''

UNCOVERED_TEXT_BOX = "This visual is not supported in this version. This will be added in future versions."
VISUAL_GENERATION_ERROR = "Error occured during genration of this file."


PARTITION_BLOCK_EXCEL = """
\tpartition {table_name} = m
\t\tmode: import
\t\tsource =
\t\t\tlet
\t\t\t    Source = Excel.Workbook(File.Contents("{file_path}"), null, true),
\t\t\t    {table_name}_Sheet = Source{{[Item="{table_name}",Kind="Sheet"]}}[Data],
\t\t\t    #"Promoted Headers" = Table.PromoteHeaders({table_name}_Sheet, [PromoteAllScalars=true])
\t\t\tin
\t\t\t    #"Promoted Headers"

\tannotation PBI_ResultType = Table
"""


PARTITION_BLOCK_CSV = """
\tpartition {table_name} = m
\t\tmode: import
\t\tsource =
\t\t\tlet
\t\t\t    Source = Csv.Document(File.Contents("{file_path}"), [Delimiter=",", Columns={column_count}, Encoding=1252, QuoteStyle=QuoteStyle.None]),
\t\t\t    #"Promoted Headers" = Table.PromoteHeaders(Source, [PromoteAllScalars=true])
\t\t\tin
\t\t\t    #"Promoted Headers"

\tannotation PBI_ResultType = Table
"""


PARTITION_BLOCK_SQL = """
\tpartition {table_name} = m
\t\tmode: import
\t\tsource =
\t\t\tlet
\t\t\t    Source = Sql.Databases("{server}"),
\t\t\t    #"{database}" = Source{{[Name="{database}"]}}[Data],
\t\t\t    #"dbo_{table_name}" = #"{database}"{{[Schema="dbo", Item="{table_name}"]}}[Data]
\t\t\tin
\t\t\t    #"dbo_{table_name}"

\tannotation PBI_ResultType = Table
"""
TABLE_NAME_CALCULATIONS = "Calculations"
PARTITION_BLOCK_Calculations = """\
\tpartition {table_name} = m
\t\tmode: import
\t\tsource =
\t\t\tlet
\t\t\t    Source = Table.FromRows(Json.Document(Binary.Decompress(Binary.FromText("i44FAA==", BinaryEncoding.Base64), Compression.Deflate)), let _t = ((type nullable text) meta [Serialized.Text = true]) in type table [Column1 = _t]),
\t\t\t    #"Changed Type" = Table.TransformColumnTypes(Source,{{"Column1", type text}}),
\t\t\t    #"Removed Columns" = Table.RemoveColumns(#"Changed Type",{{"Column1"}})
\t\t\tin
\t\t\t    #"Removed Columns"

\tannotation PBI_ResultType = Table
"""



MODEL_TMDL_HEADER = """model Model
\tculture: en-US
\tdefaultPowerBIDataSourceVersion: powerBI_V3
\tsourceQueryCulture: en-IN
\tdataAccessOptions
\t\tlegacyRedirects
\t\treturnErrorValuesAsNull

\tannotation PBI_QueryOrder = {query_order}
\tannotation __PBI_TimeIntelligenceEnabled = 1
\tannotation PBIDesktopVersion = 2.144.1378.0 (25.06)+cba1e61f16ea36d44901f06fa446299040122394
\tannotation PBI_ProTooling = ["DevMode"]
"""


MODEL_TMDL_REF_CULTURE = "ref cultureInfo en-US"

