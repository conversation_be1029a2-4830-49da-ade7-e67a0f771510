import uuid,json,re
def get_page_navigation_report(section_list,dashboards,report_name_id_in_pbi,button_id_worksheet_name):
    dashboard_name_buttons={}
    target_id_names={}
    icons_dict={"text":"blank"}
    for key,value in button_id_worksheet_name.items():
        if value in report_name_id_in_pbi.keys():
            target_id_names[key]=report_name_id_in_pbi[value]
    for dashboard in dashboards.findall(".//dashboard"):
        name_of_dashboard = dashboard.attrib.get("name")
        buttons_in_dashboard=[]
        for zone in dashboard.findall("./zones/zone"):
            if not "button" in zone.keys():
                continue
            button_config={}
            button_config["visual_config_name"]=f'{str(uuid.uuid4()).replace("-","")[:20]}'
            button_config["x"]=f'{(float(zone.attrib.get("@x"))* 720.00 / 100000.00)}'
            button_config["y"]=f'{(float(zone.attrib.get("@y"))* 720.00 / 100000.00)}'
            button_config["width"]=f'{(float(zone.attrib.get("@w"))* 720.00 / 100000.00)}'
            button_config["height"]=f'{(float(zone.attrib.get("@h"))* 720.00 / 100000.00)}'
            button_element = zone.find("button")
            icon = icons_dict.get(button_element.attrib.get("button-type", ""), "blank")
            action_attr = button_element.attrib.get("action", "")
            for_target_id = re.search(r'"(.*?)"', action_attr)


            target_id=target_id_names[for_target_id[0][1:-1]]

            button_element = zone.find("button")
            visual_state = button_element.find("button-visual-state") if button_element is not None else None
            # Caption text
            text_value = "Go Back Default"
            if visual_state is not None:
                caption = visual_state.find("caption")
                if caption is not None and caption.text:
                    text_value = caption.text

            # Bold font check
            bold_tab_val = ""
            font_style = visual_state.find("button-caption-font-style") if visual_state is not None else None
            if font_style is not None:
                bold_tab_val = font_style.attrib.get("fontname", "")
            is_bold = True  if bold_tab_val else  False

            # Background color
            bg_color = "#ffffff"
            formats = visual_state.findall("format") if visual_state is not None else []
            for fmt in formats:
                if fmt.attrib.get("attr") == "background-color":
                    bg_color = fmt.attrib.get("value", "#ffffff")

            button_config["objects"] = get_objects(icon, text_value, is_bold, bg_color)
            button_config["vc_objects"] = get_vc_objects(text_value,target_id)
            button_json_filters = {"filters":"[]","height":button_config["height"],"width":button_config["width"],"x":button_config["x"],"y":button_config["y"],"z":0.00}
            button_jsn = {"config":json.dumps(get_button_json(button_config["visual_config_name"],button_config["x"],button_config["y"],button_config["width"],button_config["height"],get_objects(icon, text_value, is_bold, bg_color),get_vc_objects(text_value,target_id)))}
            button_jsn.update(button_json_filters)
            buttons_in_dashboard.append(button_jsn)
        if buttons_in_dashboard:
            dashboard_name_buttons[name_of_dashboard]=buttons_in_dashboard

        for visual in section_list:
            if visual["displayName"] in dashboard_name_buttons.keys(): visual["visualContainers"].extend(dashboard_name_buttons[visual["displayName"]])
    return section_list


def get_objects(icon,text_value,is_bold,bg_color):
    objects={"objects":{"icon":[{"properties":{"shapeType":{"expr":{"Literal":{"Value":f"'{icon}'"}}}},"selector":{"id":"default"}}],"text":[{"properties":{"show":{"expr":{"Literal":{"Value":True if text_value else  False}}}}},{"properties":{"text":{"expr":{"Literal":{"Value":f"'{text_value}'"}}},"bold":{"expr":{"Literal":{"Value":True if is_bold else False}}},"fontFamily":{"expr":{"Literal":{"Value":"'Callibri'"}}}},"selector":{"id":"default"}}],"fill":[{"properties":{"show":{"expr":{"Literal":{"Value":True if bg_color else  False}}}}},{"properties":{"fillColor":{"solid":{"color":{"expr":{"Literal":{"Value":f"'{bg_color}'"}}}}},"transparency":{"expr":{"Literal":{"Value":"0D"}}}},"selector":{"id":"default"}}]}}
    objects=json.dumps(objects)
    return objects[1:-1]

def get_vc_objects(text_value,target_id):
    vc_objects={"vcObjects":{"title":[{"properties":{"show":{"expr":{"Literal":{"Value": False if text_value else True }}},"text":{"expr":{"Literal":{"Value":f"'{text_value}'"}}}}}],"visualLink":[{"properties":{"show":{"expr":{"Literal":{"Value":True  if target_id else  False}}},"type":{"expr":{"Literal":{"Value":"'PageNavigation'"}}},"navigationSection":{"expr":{"Literal":{"Value":f"'{target_id}'"}}}}}]}}
    vc_objects=json.dumps(vc_objects)
    return vc_objects[1:-1]

def get_button_json(visual_config_name, x, y, width, height, objects, vc_objects):
    return {
        "name": visual_config_name,
        "layouts": [{"id": 0, "position": {"x": x, "y": y, "z": 0, "width": width, "height": height, "tabOrder": 1000}}],
        "singleVisual": {
            "visualType": "actionButton",
            "drillFilterOtherVisuals": True,
            "objects": objects,
            "vc_objects": vc_objects
        },
        "howCreated": "InsertVisualButton"
    }