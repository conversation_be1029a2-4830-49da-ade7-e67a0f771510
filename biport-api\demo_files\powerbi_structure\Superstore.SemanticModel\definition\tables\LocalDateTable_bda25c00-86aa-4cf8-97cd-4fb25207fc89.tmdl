table LocalDateTable_bda25c00-86aa-4cf8-97cd-4fb25207fc89
	isHidden
	showAsVariationsOnly
	lineageTag: 0885b3d3-e46f-426d-8e1b-793db4c3a5c8

	column Date
		dataType: dateTime
		isHidden
		lineageTag: 084913e4-5f02-42e2-b853-1f613c65a20f
		dataCategory: PaddedDateTableDates
		summarizeBy: none
		isNameInferred
		sourceColumn: [Date]

		annotation SummarizationSetBy = User

	column Year = YEAR([Date])
		dataType: int64
		isHidden
		lineageTag: 6745ca60-b68d-4d91-8a62-6caea40dd289
		dataCategory: Years
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Year

	column MonthNo = MONTH([Date])
		dataType: int64
		isHidden
		lineageTag: f9d68f28-affe-4d58-ba1e-5e65d6ca4367
		dataCategory: MonthOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = MonthNumber

	column Month = FORMAT([Date], "MMMM")
		dataType: string
		isHidden
		lineageTag: 16e6ed8a-994a-4069-ab67-1e1c533e8504
		dataCategory: Months
		summarizeBy: none
		sortByColumn: MonthNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Month

	column QuarterNo = INT(([MonthNo] + 2) / 3)
		dataType: int64
		isHidden
		lineageTag: 7ad29ee8-36f3-422a-9ca3-2c27b0d5b850
		dataCategory: QuarterOfYear
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = QuarterNumber

	column Quarter = "Qtr " & [QuarterNo]
		dataType: string
		isHidden
		lineageTag: d6ed04e6-655b-43b6-bfd3-45840c479450
		dataCategory: Quarters
		summarizeBy: none
		sortByColumn: QuarterNo

		annotation SummarizationSetBy = User

		annotation TemplateId = Quarter

	column Day = DAY([Date])
		dataType: int64
		isHidden
		lineageTag: f67b3747-43ba-4f91-92a4-2b8d20115e52
		dataCategory: DayOfMonth
		summarizeBy: none

		annotation SummarizationSetBy = User

		annotation TemplateId = Day

	hierarchy 'Date Hierarchy'
		lineageTag: 38b24e1a-10f8-4867-a9c5-3ecc370fa378

		level Year
			lineageTag: 7dfa741c-d600-4d3a-b67e-be482724dcdd
			column: Year

		level Quarter
			lineageTag: 0231ea2f-2609-4eaf-b9a0-4c5f71019a86
			column: Quarter

		level Month
			lineageTag: 38852339-ade8-444a-adb7-920a22829f9c
			column: Month

		level Day
			lineageTag: 7bd665d7-4f06-4f27-a4f0-43f2c75104b0
			column: Day

		annotation TemplateId = DateHierarchy

	partition LocalDateTable_bda25c00-86aa-4cf8-97cd-4fb25207fc89 = calculated
		mode: import
		source = Calendar(Date(Year(MIN('Orders'[Order Date])), 1, 1), Date(Year(MAX('Orders'[Order Date])), 12, 31))

	annotation __PBI_LocalDateTable = true

