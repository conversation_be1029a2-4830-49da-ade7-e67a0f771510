from app.schemas import DaxConversionRequest
from app.services.dax.dax_service import DaxService
from app.core.response import ServiceResponse
from app.core import logger

class DaxProcessor:
    @staticmethod
    async def convert_dax(report_id, user) -> ServiceResponse:
        logger.info(f"[DAX Processor] Starting DAX conversion process for report_id: {report_id}, user: {user.email}")

        try:
            download_links = await DaxService().dax_formulae_converstion(report_id, user)
            logger.info(f"[DAX Processor] DAX conversion service completed successfully for report_id: {report_id}")
            return ServiceResponse.success(download_links)
        except Exception as e:
            logger.error(f"[DAX Processor] DAX conversion failed for report_id: {report_id}, error: {str(e)}", exc_info=True)
            raise
