"""
This module defines the RoleModel for SQLAlchemy ORM.
It represents user roles within the system, such as admin, user, etc.,
and maintains a relationship with users assigned to each role.
"""

from sqlalchemy import Column, String
from sqlalchemy.orm import relationship
import uuid
from app.core import Base

class RoleModel(Base):
    """
    SQLAlchemy ORM model representing a user role.

    Attributes:
        id (str): Primary key, a UUID string uniquely identifying each role.
        type (str): The name/type of the role (e.g., 'admin', 'editor', 'viewer').
        users (List[UserManager]): Relationship to users associated with this role.
    """
    __tablename__ = "roles"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    type = Column(String, nullable=False, unique=True)

    # One-to-many relationship with users
    # users = relationship("UserManager", back_populates="role")
    