import re
from app.core import logger
from app.core.enums import (
    ChartType, GeneralKeys as GS, WorkSheet as WS
)

class WaterfallChart:
    @staticmethod
    def check_waterfall_chart(worksheet):
        try:
            marks = worksheet.findall(WS.MARK.value)
            if not marks:
                logger.debug("No <mark> found.")
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            mark_class = marks[0].get(GS.CLASS.value, '').strip().lower()
            logger.debug(f"Mark class found: {mark_class}")
            if mark_class != ChartType.GANTT_BAR.value.lower():
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            panes = worksheet.findall(WS.PANES_PANE.value)
            logger.debug(f"Number of panes: {len(panes)}")
            if len(panes) != 1:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            encodings = worksheet.findall(WS.ENCODINGS.value)
            has_size_encoding = (
                encodings and any(e.tag.lower() == WS.SIZE.value for e in encodings[0])
            )
            logger.debug(f"Has size encoding: {has_size_encoding}")
            if not has_size_encoding:
                return {GS.STATUS.value: False, GS.CHART_TYPE.value: None}

            rows = worksheet.find(WS.ROWS.value)
            logger.debug(f"Rows element: {rows}, text: {rows.text if rows is not None else None}")
            if rows is not None and rows.text:
                row_items = rows.text.strip().split(',')
                for col in row_items:
                    logger.debug(f"Processing row item: {col}")
                    match = re.search(r'\.\[(.*?)\]$', col.strip())
                    if match:
                        column_part = match.group(1)
                        parts = column_part.split(':')
                        logger.debug(f"Extracted parts: {parts}")
                        if len(parts) == 4:
                            logger.debug("Detected Waterfall chart.")
                            return {
                                GS.STATUS.value: True,
                                GS.CHART_TYPE.value: ChartType.WATER_FALL.value
                            }

            logger.debug("Did not detect Waterfall structure. Defaulting to GanttBar.")
            return {
                GS.STATUS.value: True,
                GS.CHART_TYPE.value: ChartType.GANTT_BAR.value
            }

        except Exception as e:
            logger.error(f"Error in check_waterfall_chart: {e}")
            return {
                GS.STATUS.value: False,
                GS.CHART_TYPE.value: None,
                GS.ERROR.value: str(e)
            }
