from app.core.enums import GeneralKeys, TableauXMLTags, VisualRequest
from app.services.migrate.visuals.text_box import process_text_box_in_worksheets
from app.services.migrate.Tableau_Analyzer.report import (
    extract_worksheet_title,
    extract_style_data,
    extract_datasource_columns,
    extract_table_columns, get_fields_data,
    extract_calcuations_related_details
)
from app.core.constants import UNCOVERED_TEXT_BOX, VISUAL_GENERATION_ERROR

from app.services.migrate.visuals import visuals_generation_mapping

def process_worksheets(root, chart_types):
    worksheets = root.findall(TableauXMLTags.WORKSHEET.value)
    datasources = root.findall(TableauXMLTags.DATASOURCE.value)
    table_column_data = extract_table_columns(datasources)
    worksheet_visuals_data = []
    for worksheet in worksheets:
        worksheet_name = worksheet.get(TableauXMLTags.NAME.value)
        rows = worksheet.find(TableauXMLTags.ROWS.value).text
        cols = worksheet.find(TableauXMLTags.COLS.value).text
        filters = worksheet.find(TableauXMLTags.FILTER.value)
        panes = worksheet.findall(TableauXMLTags.PANE.value)
        style_data = worksheet.find(TableauXMLTags.STYLE.value)
        title_run_data = worksheet.findall(TableauXMLTags.TITLE_RUN.value)
        datasource_dependencies = worksheet.findall(TableauXMLTags.DATASOURCE_DEPENDENCIES.value)
        chart_types_data = {chart.get(GeneralKeys.NAME.value): chart.get(GeneralKeys.CHART_TYPE.value) for chart in chart_types}
        visual_type = chart_types_data.get(worksheet_name)
        rows_list = get_fields_data(rows)
        cols_list = get_fields_data(cols)

        worksheet_title_data = extract_worksheet_title(title_run_data) if title_run_data else None
        worksheet_style_data = extract_style_data(style_data) if style_data else None
        datasource_columns = extract_datasource_columns(datasource_dependencies) if datasource_dependencies else None
        calculations_related_data = extract_calcuations_related_details(table_column_data, datasource_columns)  #  This is for temporary as we don't have any fixed approach for the calcuations issue we are using this
        small_multiples = True if "*" in str(rows)+str(cols) else False

        visual_request = VisualRequest(
            rows = rows_list,
            cols = cols_list,
            panes = panes,
            visual_type = visual_type,
            worksheet_name = worksheet_name,
            table_column_data = table_column_data,
            datasource_columns = datasource_columns,
            worksheet_title_data = worksheet_title_data,
            worksheet_style_data = worksheet_style_data,
            calculations_related_data = calculations_related_data,
            small_multiples = small_multiples,
            filters=filters
        )
        "  All visuals visuals here are generated. "
        visual_generating_function = visuals_generation_mapping.get(visual_request.visual_type, None)
        try:
            if visual_generating_function:
                visual_generation_response =  visual_generating_function(visual_request)
                if visual_generation_response: worksheet_visuals_data.append(visual_generation_response)
            else:
                uncovered_visual = process_text_box_in_worksheets(worksheet,text = UNCOVERED_TEXT_BOX)
                if uncovered_visual: worksheet_visuals_data.append(uncovered_visual)
        except:
            error_occured_visual = process_text_box_in_worksheets(worksheet,text = VISUAL_GENERATION_ERROR)
            if error_occured_visual: worksheet_visuals_data.append(error_occured_visual)
    return worksheet_visuals_data
