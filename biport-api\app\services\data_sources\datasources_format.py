class DataSourceDetailsFormat:
    """DATA SOURCES DETAILS FORMATTING"""

    @staticmethod
    def new_mssql_data_source(request):
        """MSSQL DS DETAILS FORMAT"""
        try:
            mssql_db_details = {
                "host": request["host"],
                "port": request["port"],
                "username": request["username"],
                "database": request["database"],
                "password": request["password"],
                "db_instance": request.get("db_instance", None),
            }
            return mssql_db_details
        except KeyError as e:
            raise ValueError(f"Missing required key: {e.args[0]}")
        except Exception as e:
            raise ValueError(str(e))

    @staticmethod
    def new_postgresql_source(request):
        """POSTGRESQL DS DETAILS FORMAT"""
        try:
            postgresql_db_details = {
                "host": request["host"],
                "port": request["port"],
                "username": request["username"],
                "database": request["database"],
                "password": request["password"],
            }
            return postgresql_db_details
        except Key<PERSON>rror as e:
            raise ValueError(f"Missing required key: {e.args[0]}")
        except Exception as e:
            raise ValueError(str(e))

    @staticmethod
    def new_mysql_source(request):
        """MYSQL DS DETAILS FORMAT"""
        try:
            mysql_server_db_details = {
                "host": request["host"],
                "port": request["port"],
                "username": request["username"],
                "database": request["database"],
                "password": request["password"],
            }
            return mysql_server_db_details
        except KeyError as e:
            raise ValueError(f"Missing required key: {e.args[0]}")
        except Exception as e:
            raise ValueError(str(e))

    @staticmethod
    def new_oracle_source(request):
        """ORACLE DS DETAILS FORMAT"""
        try:
            oracle_db_details = {
                "host": request["host"],
                "port": request["port"],
                "service_name": request["service_name"],
                "username": request["username"],
                "sid": request["sid"],
                "password": request["password"],
            }
            return oracle_db_details
        except KeyError as e:
            raise ValueError(f"Missing required key: {e.args[0]}")
        except Exception as e:
            raise ValueError(str(e))

    @staticmethod
    def new_sqlite_source(request):
        """SQLite DS DETAILS FORMAT"""
        try:
            sqlite_path = {
                "path": request["path"],
                "password": "",
            }
            return sqlite_path
        except KeyError as e:
            raise ValueError(f"Missing required key: {e.args[0]}")
        except Exception as e:
            raise ValueError(str(e))