from ..core import *
from app.core import area_chart_json
import json, uuid,re
from app.core.logger_setup import logger
from app.core.enums import TableauXMLTags, PowerBITemplateKeys, PowerBIReportKeys, PowerBIChartTypes, VisualRequest
from app.services.migrate.Tableau_Analyzer.report import (extract_multiple_encodings_data, remove_duplicate_fields, generate_projections_data)


def get_areachart_report(rows, cols, pane_encodings,table_column_data, datasource_col_list,orderby, worksheet_name,worksheet_title_layout, style):
    try:
        overall_areachart_result = []
        from_list,select_list,category_list,y_ref_list,series_ref_list,orderby_list,select_list_non_dumplicates,cols_list,table_names,tooltips_list,column_properties= [],[],[],[],[],[],[],[],[],[],{}
        series = pane_encodings.get('color', {}).get('@column',"")
        cols_list=get_fields_data(cols)
        rows_list=get_fields_data(rows)
        series_list=get_fields_data(series)

        for rows in rows_list:
            y_ref_list,y_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,y_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            if y_tab_name  not in table_names :table_names.append(y_tab_name)

        for cols in cols_list:
            category_list,cat_tab_name=query_ref_list(cols,table_column_data,datasource_col_list,category_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(cols,table_column_data,datasource_col_list)),select_list)
            if cat_tab_name  not in table_names :table_names.append(cat_tab_name)

        if series:
            for series in series_list:
                series_ref_list,series_tab_name=query_ref_list(series,table_column_data,datasource_col_list,series_ref_list)
                select_list=get_select_json_list(*list(get_calc_filter_column(series,table_column_data,datasource_col_list)),select_list)
                if series_tab_name  not in table_names : table_names.append(series_tab_name)

        orderby=[orderby] if isinstance(orderby,dict) else [orderby]
        for eachorder in orderby:
            if eachorder.get("@using"):
                direction_order=1 if eachorder.get("@direction")=="ASC" else 2
                order_list=re.findall(r'\[.*?\]\.\[.*?\]',eachorder.get("@using"))
                for category in order_list:
                    orderfil,ordercol,ordertab=get_calc_filter_column(category,table_column_data,datasource_col_list)
                    orderby_list.extend(get_order_by_list(category,table_column_data,datasource_col_list,direction_order))
                    select_list=get_select_json_list(*list(get_calc_filter_column(category,table_column_data,datasource_col_list)),select_list)
                    if ordertab  not in table_names : table_names.append(ordertab)

        for query in select_list:
            if query  not in select_list_non_dumplicates:
                select_list_non_dumplicates.append(query)

        from_list=get_from_list(table_names)
        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)

        areachart_json={
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "category_list" : json.dumps(category_list),
            "y_ref_list" : json.dumps(y_ref_list),
            "series_ref_list" : json.dumps(series_ref_list),
            "from_list" : json.dumps(from_list),
            "select_list" :json.dumps(select_list_non_dumplicates),
            "orderby_list":json.dumps(orderby_list),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }

        overall_areachart_result.append({"config":areachart_json, "template": area_chart_json})
        return overall_areachart_result
    except Exception as e:
        logger.error(f"---Error in generating area chart visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating area chart visual for {worksheet_name} - {str(e)}")

def get_secondary_axis(style_data):
    '''retrieves the value of 'field' from 'encoding' tag in styles for secondary axis'''
    return style_data.get(TableauXMLTags.AXIS.value, {}).get(TableauXMLTags.ENCODING.value, {}).get(TableauXMLTags.FIELD.value,"") if style_data else ""
    

def process_area_chart_report(request : VisualRequest):
    area_chart_result = {}
    panes = request.panes
    rows = request.rows
    cols = request.cols
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data
    style_data = request.worksheet_style_data

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )
    unique_tooltips = remove_duplicate_fields(
        encodings[TableauXMLTags.TOOLTIP.value],
        encodings[TableauXMLTags.LOD.value],
        encodings[TableauXMLTags.TEXT.value],
        rows,
        cols
    )
    unique_color_data = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])
    secondary_axis = get_secondary_axis(style_data)

    areachart_field_mapping = {
        PowerBIReportKeys.Y.value: rows,
        PowerBIReportKeys.CATEGORY.value: cols,
    }

    if unique_tooltips:
        areachart_field_mapping[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips
    if secondary_axis:
        areachart_field_mapping[PowerBIReportKeys.Y2.value] = [secondary_axis]

    projections_data = generate_projections_data(table_column_data, calculations_related_data, areachart_field_mapping)

    area_chart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.AREA.value
    area_chart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    area_chart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data
    return area_chart_result
