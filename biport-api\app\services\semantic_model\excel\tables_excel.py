import os
import re
import json
import uuid
from app.core.config import logger
import xml.etree.ElementTree as ET

def extract_table_columns_excel(root, output_dir_for_date_tables, template_content_str, unique_table_lineage_tags, relationships_list_for_date_tables):
    logger.info(f"Excel TMDL Gen - extract_table_columns_excel - Starting column extraction and LocalDateTable generation.")
    table_columns_map = {}
    variant_relationships = []

    for metadata_record_node in root.findall(".//metadata-record[@class='column']"):
        twb_table_name_raw = metadata_record_node.findtext("parent-name")
        twb_column_name_raw = metadata_record_node.findtext("remote-name") or metadata_record_node.findtext("local-name")
        # twb_aggregation_hint = metadata_record_node.findtext("aggregation", "None") # Unused
        twb_local_type = metadata_record_node.findtext("local-type", "string")

        if not twb_table_name_raw or not twb_column_name_raw:
            logger.warning("Skipping column: missing parent-name or column-name.")
            continue

        tmdl_table_name = twb_table_name_raw.strip("[]")
        if tmdl_table_name not in unique_table_lineage_tags:
            unique_table_lineage_tags[tmdl_table_name] = str(uuid.uuid4())

        table_columns_map.setdefault(tmdl_table_name, [])
        column_lineage_tag_guid = str(uuid.uuid4())

        tmdl_dataType = "string"
        tmdl_formatString = "none"
        tmdl_summarizeBy = "None"
        underlying_data_type_hint = None

        if twb_local_type == "integer":
            tmdl_dataType = "int64"
            tmdl_formatString = "0"
            tmdl_summarizeBy = "Sum"
        elif twb_local_type == "real":
            tmdl_dataType = "double"
            tmdl_formatString = "0.00"
            tmdl_summarizeBy = "Sum"
        elif twb_local_type == "string":
            tmdl_dataType = "string"
            tmdl_formatString = "none"
            tmdl_summarizeBy = "Count"
        elif twb_local_type == "date":
            tmdl_dataType = "dateTime"
            tmdl_formatString = "Long Date"
            tmdl_summarizeBy = "None"
            underlying_data_type_hint = "Date"
        elif twb_local_type == "datetime":
            tmdl_dataType = "dateTime"
            tmdl_formatString = "General Date"
            tmdl_summarizeBy = "None"
            underlying_data_type_hint = "DateTime"
        elif twb_local_type == "boolean":
            tmdl_dataType = "boolean"
            tmdl_summarizeBy = "Count"
        else:
            logger.warning(f"Unknown TWB type '{twb_local_type}' for {tmdl_table_name}.{twb_column_name_raw}. Defaulting to string.")
            tmdl_dataType = "string"
            tmdl_summarizeBy = "Count"

        column_name_cleaned_for_source = twb_column_name_raw.strip("[]")
        tmdl_column_definition_name = f"'{column_name_cleaned_for_source}'" if re.search(r"[^a-zA-Z0-9_]", column_name_cleaned_for_source) or " " in column_name_cleaned_for_source else column_name_cleaned_for_source
        tmdl_source_column_name = tmdl_column_definition_name

        column_definition_data = {
            "name": tmdl_column_definition_name,
            "dataType": tmdl_dataType,
            "formatString": str(tmdl_formatString),
            "lineageTag": column_lineage_tag_guid,
            "summarizeBy": tmdl_summarizeBy,
            "sourceColumn": tmdl_source_column_name,
            "isDataTypeInferred": True,
            "annotation": "SummarizationSetBy = Automatic",
        }

        if underlying_data_type_hint:
            column_definition_data["annotations"] = [
                {"name": "SummarizationSetBy", "value": "Automatic"},
                {"name": "UnderlyingDateTimeDataType", "value": underlying_data_type_hint}
            ]
            if "annotation" in column_definition_data:
                del column_definition_data["annotation"]

        if tmdl_dataType == "dateTime" and template_content_str:
            local_date_table_name = f"LocalDateTable_{column_lineage_tag_guid}"
            local_date_file_path = os.path.join(output_dir_for_date_tables, f"{local_date_table_name}.tmdl")

            logger.info(f"Creating LocalDateTable: {local_date_table_name} for column {tmdl_table_name}.{tmdl_column_definition_name}")

            # Start with the original template content
            temp_content_after_replacements = template_content_str
            
            # Perform initial replacements for table name, lineage tags, and M-query source
            temp_content_after_replacements = re.sub(
                r"(table\s+)DateTableTemplate_[0-9a-fA-F\-]{36}",
                lambda m: f"{m.group(1)}{local_date_table_name}",
                temp_content_after_replacements,
                count=1
            )
            new_date_table_lineage_tag = str(uuid.uuid4())
            # Replace only the table's lineageTag, not columns' or other elements' here.
            # A more specific regex for table lineageTag:
            temp_content_after_replacements = re.sub(
                r"(table\s+" + re.escape(local_date_table_name) + r"\s*\n(?:.*\n)*?\s+lineageTag:\s*)[0-9a-fA-F\-]{36}",
                lambda m: f"{m.group(1)}{new_date_table_lineage_tag}",
                temp_content_after_replacements,
                count=1 
            ) if re.search(r"table\s+" + re.escape(local_date_table_name), temp_content_after_replacements) else re.sub( # Fallback if table name was not yet replaced in a multi-line context
                r"(\s+lineageTag:\s*)[0-9a-fA-F\-]{36}", # Original less specific replacement
                lambda m: f"{m.group(1)}{new_date_table_lineage_tag}",
                temp_content_after_replacements,
                count=1
            )



            def replace_col_lineage_tag_with_new_uuid(match):
                return f"{match.group(1)}{str(uuid.uuid4())}"

            temp_content_after_replacements = re.sub(r"(column\s+\w+\s+.*?lineageTag:\s*)[0-9a-fA-F\-]{36}", replace_col_lineage_tag_with_new_uuid, temp_content_after_replacements, flags=re.DOTALL)
            temp_content_after_replacements = re.sub(r"(level\s+\w+\s+.*?lineageTag:\s*)[0-9a-fA-F\-]{36}", replace_col_lineage_tag_with_new_uuid, temp_content_after_replacements, flags=re.DOTALL)
            temp_content_after_replacements = re.sub(r"(hierarchy\s+'.*?'\s+lineageTag:\s*)[0-9a-fA-F\-]{36}", replace_col_lineage_tag_with_new_uuid, temp_content_after_replacements, flags=re.DOTALL)

            m_query_table_ref = f"'{tmdl_table_name}'" if " " in tmdl_table_name or re.search(r"[^a-zA-Z0-9_]", tmdl_table_name) else tmdl_table_name
            m_query_column_ref = tmdl_column_definition_name
            calendar_source_expression = f"Calendar(Date(Year(MIN({m_query_table_ref}[{m_query_column_ref}])), 1, 1), Date(Year(MAX({m_query_table_ref}[{m_query_column_ref}])), 12, 31))"
            temp_content_after_replacements = re.sub(
                r"(source\s*=\s*)Calendar\(.*?\)",
                lambda m: f"{m.group(1)}{calendar_source_expression}",
                temp_content_after_replacements
            )
            temp_content_after_replacements = re.sub(
                r"(partition\s+)DateTableTemplate_[0-9a-fA-F\-]{36}(\s*=\s*calculated)",
                lambda m: f"{m.group(1)}{local_date_table_name}{m.group(2)}",
                temp_content_after_replacements
            )

            # Correctly set table properties for variation target: 'showAsVariationsOnly'
            lines = temp_content_after_replacements.split('\n')
            final_lines = []
            indent_char = "\t" 

            for i, line_content in enumerate(lines):
                stripped_line = line_content.strip()
                
                if stripped_line.startswith(f"table {local_date_table_name}"):
                    final_lines.append(line_content)
                    current_indent = indent_char 
                    if i + 1 < len(lines):
                        match_indent = re.match(r"^(\s+)", lines[i+1])
                        if match_indent:
                            current_indent = match_indent.group(1)
                    
                    already_has_show_as_variations = False
                    if i + 1 < len(lines) and lines[i+1].strip() == "showAsVariationsOnly":
                        already_has_show_as_variations = True
                    
                    if not already_has_show_as_variations:
                         final_lines.append(current_indent + "showAsVariationsOnly")
                
                elif stripped_line == "isHidden" or stripped_line == "isPrivate":
                    # Skip 'isHidden' and 'isPrivate' lines from the template for this variation target table
                    continue
                else:
                    final_lines.append(line_content)
            
            updated_content = "\n".join(final_lines)
            
            # Ensure there isn't an empty line right after showAsVariationsOnly if isHidden/isPrivate were removed
            updated_content = re.sub(r"(showAsVariationsOnly\s*\n)\s*\n", r"\1", updated_content)


            with open(local_date_file_path, 'w', encoding='utf-8') as f_local_date:
                f_local_date.write(updated_content)

            logger.info(f"Successfully wrote {local_date_file_path}")

            relationship_id = str(uuid.uuid4())
            relationships_list_for_date_tables.append({
                "id": relationship_id,
                "joinOnDateBehavior": "datePartOnly",
                "fromColumn": f"{tmdl_table_name}.{tmdl_column_definition_name}",
                "toColumn": f"{local_date_table_name}.Date",
                "fromCardinality": "many",
                "toCardinality": "one",
                "crossFilteringBehavior": "bothDirections",
                "isActive": True
            })

            logger.info(f"Added relationship: {relationship_id} between {tmdl_table_name}.{tmdl_column_definition_name} and {local_date_table_name}.Date")

            column_definition_data["variation"] = {
                "isDefault": True,
                "relationship": relationship_id,
                "defaultHierarchy": f"{local_date_table_name}.'Date Hierarchy'"
            }

            if "annotations" not in column_definition_data:
                column_definition_data["annotations"] = []

            if "annotation" in column_definition_data and isinstance(column_definition_data["annotations"], list):
                found_sum_set_by = any(ann["name"] == "SummarizationSetBy" for ann in column_definition_data["annotations"])
                if not found_sum_set_by and "annotation" in column_definition_data:
                    single_ann_parts = column_definition_data["annotation"].split(" = ", 1)
                    if len(single_ann_parts) == 2:
                        column_definition_data["annotations"].append({"name": single_ann_parts[0], "value": single_ann_parts[1]})
                if "annotation" in column_definition_data:
                    del column_definition_data["annotation"]

            column_definition_data["changedProperties"] = ["IsHidden", "DataType"]
            column_definition_data["annotations"].append({"name": "PBI_FormatHint", "value": '{"isDateTimeCustom":true}'})

        table_columns_map[tmdl_table_name].append(column_definition_data)

    logger.info(f"Finished column extraction. Tables processed: {len(table_columns_map)}")
    return table_columns_map, variant_relationships


def write_table_model_files_excel(table_columns_map, unique_table_lineage_tags, output_dir, excel_file_path_for_mquery: str, calculated_columns_json_data=None):
    if calculated_columns_json_data is None:
        calculated_columns_json_data = []

    logger.info(f"Excel TMDL Gen - write_table_model_files_excel - Starting. Total tables: {len(table_columns_map)}")
    
    base_excel_path_for_mquery_dir = r"C:\Program Files\Microsoft Power BI Desktop\bin\SampleData"

    for table_key, columns_list_from_extraction in table_columns_map.items():
        logger.info(f"Excel TMDL Gen - write_table_model_files_excel - Processing table: {table_key}")
        tmdl_file_path = os.path.join(output_dir, f"{table_key}.tmdl")
        logger.info(f"Excel TMDL Gen - write_table_model_files_excel - Output TMDL file: {tmdl_file_path}")

        tmdl_table_name_quoted = f"'{table_key}'" if re.search(r"[^a-zA-Z0-9_]", table_key) or " " in table_key else table_key
        partition_name_quoted = tmdl_table_name_quoted
        
        excel_filename_from_twb = os.path.basename(excel_file_path_for_mquery)
        final_excel_path_for_m = os.path.join(base_excel_path_for_mquery_dir, excel_filename_from_twb)
        

        with open(tmdl_file_path, 'w', encoding='utf-8') as tmdl_file:
            tmdl_file.write(f"table {tmdl_table_name_quoted}\n")
            tmdl_file.write(f"\tlineageTag: {unique_table_lineage_tags[table_key]}\n")

            for col_def in columns_list_from_extraction:
                tmdl_file.write(f"\tcolumn {col_def['name']}\n")
                tmdl_file.write(f"\t\tdataType: {col_def['dataType']}\n")
                tmdl_file.write(f"\t\tformatString: {col_def['formatString']}\n")
                tmdl_file.write(f"\t\tlineageTag: {col_def['lineageTag']}\n")
                tmdl_file.write(f"\t\tsummarizeBy: {col_def['summarizeBy']}\n")
                tmdl_file.write(f"\t\tsourceColumn: {col_def['sourceColumn']}\n")
                if col_def.get("isDataTypeInferred", False):
                    tmdl_file.write(f"\t\tisDataTypeInferred\n")

                if "annotations" in col_def and col_def["annotations"]:
                    for ann in col_def["annotations"]:
                        ann_value_str = ann['value']
                        if isinstance(ann_value_str, dict) or isinstance(ann_value_str, list) :
                            ann_value_str = json.dumps(ann_value_str)
                        elif isinstance(ann_value_str, bool):
                             ann_value_str = str(ann_value_str).lower()
                        tmdl_file.write(f"\t\tannotation {ann['name']} = {ann_value_str}\n")
                elif "annotation" in col_def and col_def["annotation"]:
                     tmdl_file.write(f"\t\tannotation {col_def['annotation']}\n")

                if "variation" in col_def:
                    var_info = col_def["variation"]
                    tmdl_file.write(f"\t\tvariation Variation\n")
                    tmdl_file.write(f"\t\t\tisDefault: {str(var_info['isDefault']).lower()}\n")
                    tmdl_file.write(f"\t\t\trelationship: {var_info['relationship']}\n")
                    
                    dh_table_part_raw, dh_hier_part_raw = var_info['defaultHierarchy'].split('.', 1)
                    dh_table_part_cleaned = dh_table_part_raw.strip("'")
                    dh_hier_part_cleaned = dh_hier_part_raw.strip("'")

                    dh_table_part_quoted = f"'{dh_table_part_cleaned}'" if re.search(r"[^a-zA-Z0-9_]", dh_table_part_cleaned) or " " in dh_table_part_cleaned else dh_table_part_cleaned
                    dh_hier_part_quoted = f"'{dh_hier_part_cleaned}'"
                    tmdl_file.write(f"\t\t\tdefaultHierarchy: {dh_table_part_quoted}.{dh_hier_part_quoted}\n")

                if "changedProperties" in col_def:
                    for prop in col_def["changedProperties"]:
                        tmdl_file.write(f"\t\tchangedProperty = {prop}\n")
                tmdl_file.write("\n")

            logger.info(f"Excel TMDL Gen - write_table_model_files_excel - Adding DAX calculations for table: {table_key}")
            for calc_item in calculated_columns_json_data:
                if calc_item.get("table_name") == table_key:
                    calc_name_quoted = calc_item["caption"]
                    calc_formula = calc_item["formula"]
                    calc_dataType_twb = calc_item["data_type"]
                    calc_formatString = None
                    calc_summarizeBy = "Sum"
                    is_measure = calc_item.get("role") == "measure"

                    if calc_dataType_twb == "integer": calc_dataType_tmdl, calc_formatString = "int64", "0"
                    elif calc_dataType_twb == "real" or calc_dataType_twb == "double": calc_dataType_tmdl, calc_formatString = "double", "0.00"
                    elif calc_dataType_twb == "string": calc_dataType_tmdl, calc_formatString = "string", "none"
                    elif calc_dataType_twb == "date": calc_dataType_tmdl, calc_formatString = "dateTime", "Long Date"
                    elif calc_dataType_twb == "datetime": calc_dataType_tmdl, calc_formatString = "dateTime", "General Date"
                    elif calc_dataType_twb == "boolean": calc_dataType_tmdl, calc_formatString = "boolean", "TRUE/FALSE"
                    else: calc_dataType_tmdl, calc_formatString = "string", "none"

                    if is_measure:
                        tmdl_file.write(f"\tmeasure {calc_name_quoted} = {calc_formula}\n")
                        tmdl_file.write(f"\t\tdataType: {calc_dataType_tmdl}\n")
                        if calc_formatString and calc_formatString != "none":
                            tmdl_file.write(f"\t\tformatString: {calc_formatString}\n")
                        tmdl_file.write(f"\t\tlineageTag: {str(uuid.uuid4())}\n")
                    else:
                        tmdl_file.write(f"\tcolumn {calc_name_quoted} = {calc_formula}\n")
                        tmdl_file.write(f"\t\tdataType: {calc_dataType_tmdl}\n")
                        if calc_formatString and calc_formatString != "none":
                             tmdl_file.write(f"\t\tformatString: {calc_formatString}\n")
                        tmdl_file.write(f"\t\tlineageTag: {str(uuid.uuid4())}\n")
                        tmdl_file.write(f"\t\tsummarizeBy: {calc_summarizeBy}\n")
                        tmdl_file.write(f"\t\tisDataTypeInferred\n")
                    
                    tmdl_file.write(f"\t\tannotation SummarizationSetBy = Automatic\n")
                    if calc_formatString and ("$" in calc_formatString or "%" in calc_formatString) :
                         hint_type = "isCurrency" if "$" in calc_formatString else "isPercentage"
                         tmdl_file.write(f'\t\tannotation PBI_FormatHint = {{"{hint_type}":true}}\n')
                    tmdl_file.write("\n")

            tmdl_file.write(f"\tpartition {partition_name_quoted} = m\n")
            tmdl_file.write(f"\t\tmode: import\n")
            tmdl_file.write(f"\t\tsource =\n")
            tmdl_file.write(f"\t\t\tlet\n")

            logger.info(f"Excel TMDL Gen - write_table_model_files_excel - M Query: Using Excel file path: {final_excel_path_for_m}")

            tmdl_file.write(f"\t\t\t\tSource = Excel.Workbook(File.Contents(\"{final_excel_path_for_m}\"), null, true),\n")
            tmdl_file.write(f"\t\t\t\tSheet1_Sheet = Source{{[Item=\"Sheet1\",Kind=\"Sheet\"]}}[Data],\n")
            tmdl_file.write(f"\t\t\t\t#\"Promoted Headers\" = Table.PromoteHeaders(Sheet1_Sheet, [PromoteAllScalars=true])")
            
            m_type_transforms = []
            for col_data in columns_list_from_extraction:
                excel_col_name = col_data['sourceColumn'].strip("'") 
                m_type = "type text" 
                if col_data['dataType'] == 'int64': m_type = "Int64.Type"
                elif col_data['dataType'] == 'double': m_type = "type number" 
                elif col_data['dataType'] == 'dateTime':
                    col_annotations = col_data.get("annotations", [])
                    underlying_type = next((ann['value'] for ann in col_annotations if ann['name'] == 'UnderlyingDateTimeDataType'), None)
                    if underlying_type == 'Date': m_type = "type date"
                    elif underlying_type == 'DateTime': m_type = "type datetime"
                    else: 
                        m_type = "type date" if col_data.get('formatString', '').lower() in ['long date', 'short date'] else "type datetime"
                elif col_data['dataType'] == 'boolean': m_type = "type logical"
                m_type_transforms.append(f"{{\"{excel_col_name}\", {m_type}}}")

            tmdl_file.write(",\n") 
            if m_type_transforms:
                tmdl_file.write(f"\t\t\t\t#\"Changed Type1\" = Table.TransformColumnTypes(#\"Promoted Headers\",{{{', '.join(m_type_transforms)}}})\n")
            else:
                
                tmdl_file.write(f"\t\t\t\t#\"Changed Type1\" = Table.TransformColumnTypes(#\"Promoted Headers\", {{}})\n")
            
            tmdl_file.write(f"\t\t\tin\n")
            tmdl_file.write(f"\t\t\t\t#\"Changed Type1\"\n")
            
            tmdl_file.write(f"\tannotation PBI_ResultType = Table\n")
            tmdl_file.write(f"\tannotation PBI_NavigationStepName = Navigation\n\n")
            logger.info(f"Excel TMDL Gen - write_table_model_files_excel - Finished writing table: {table_key}")

    logger.info(f"Excel TMDL Gen - write_table_model_files_excel - All table TMDL files written.")


def ensure_unique_column_name(tables_dir, table_name, new_column_name):
    tmdl_file_path = os.path.join(tables_dir, f"{table_name}.tmdl") 
    
    if os.path.exists(tmdl_file_path):
        with open(tmdl_file_path, 'r', encoding='utf-8') as tmdl_file:
            content = tmdl_file.read()
            
            stripped_name = new_column_name.strip("'")
            quoted_new_column_name = f"'{stripped_name}'" if re.search(r"[^a-zA-Z0-9_]", stripped_name) or " " in stripped_name else stripped_name

            existing_columns = re.findall(r"column\s+((?:'.*?')|(?:[^'\s]+))", content)
            
            normalized_existing_columns = [col.strip("'") for col in existing_columns]
            
            if stripped_name in normalized_existing_columns:
                suffix = 1
                updated_column_name_base = stripped_name
                current_test_name = f"{updated_column_name_base}_{suffix}"
                while current_test_name in normalized_existing_columns:
                    suffix += 1
                    current_test_name = f"{updated_column_name_base}_{suffix}"
                
                final_updated_name = f"'{current_test_name}'" if re.search(r"[^a-zA-Z0-9_]", current_test_name) or " " in current_test_name else current_test_name
                logger.info(f"Column name '{new_column_name}' already exists in '{table_name}'. Renamed to '{final_updated_name}'")
                return final_updated_name
            else:
                return quoted_new_column_name # Return the (potentially newly) quoted original name
    else:
        # If file doesn't exist, just quote the new column name if needed
        stripped_name = new_column_name.strip("'")
        return f"'{stripped_name}'" if re.search(r"[^a-zA-Z0-9_]", stripped_name) or " " in stripped_name else stripped_name
def update_tmdl_files_from_json_excel(tables_dir, json_data):
    aggregation = None
    for item in json_data:
        table_name_unquoted = item['table_name'] # This is likely unquoted from TWB
        caption = item['caption'] # This should be the desired DAX object name, potentially already quoted
        formula = item['formula']
        data_type_twb = item['data_type']
        role = item['role']
        col_type = item['type']
        
        # Determine data_type and format_string for TMDL
        if data_type_twb == "integer": data_type_tmdl, format_string = "int64", "0"
        elif data_type_twb in ("double", "real"): data_type_tmdl, format_string = "double", "0.00" # TMDL uses double
        elif data_type_twb in ("date", "datetime"): data_type_tmdl, format_string = "dateTime", "General Date"
        elif data_type_twb == "string": data_type_tmdl, format_string = "string", "none"
        else: data_type_tmdl, format_string = "string", "none" # Default

        if role == 'measure' and col_type == 'quantitative':
            aggregation = 'Sum' # Or based on formula inspection; for measures, this is less direct
        elif role == 'dimension' and col_type == 'nominal':
            aggregation = 'None'
        else: # Default for calculated columns if not specified
            aggregation = 'Sum' if data_type_tmdl in ["int64", "double"] else 'None'
        
        # Ensure the caption (DAX object name) is correctly quoted for TMDL
        # caption from json_data might already be quoted, or might need quoting.
        # ensure_unique_column_name should handle returning a valid, quoted, unique name.
        # Pass the base name (unquoted caption) to ensure_unique_column_name.
        base_caption_name = caption.strip("'")
        tmdl_dax_object_name = ensure_unique_column_name(tables_dir, table_name_unquoted, base_caption_name)

        tmdl_file_path = os.path.join(tables_dir, f"{table_name_unquoted}.tmdl") # File name is unquoted
        
        if os.path.exists(tmdl_file_path):
            with open(tmdl_file_path, 'r', encoding='utf-8') as tmdl_file:
                lines = tmdl_file.readlines()

            partition_index = next((i for i, line in enumerate(lines) if line.strip().startswith("partition")), None)
            
            insert_lines = []
            if role == 'measure':
                insert_lines.append(f"\tmeasure {tmdl_dax_object_name} = {formula}\n")
                insert_lines.append(f"\t\tdataType: {data_type_tmdl}\n") # Measures can have dataType
                if format_string and format_string != "none":
                    insert_lines.append(f"\t\tformatString: {format_string}\n")
                insert_lines.append(f"\t\tlineageTag: {str(uuid.uuid4())}\n")
                # Measures might have displayFolder, description, etc.
            else: # Calculated Column
                insert_lines.append(f"\tcolumn {tmdl_dax_object_name} = {formula}\n")
                insert_lines.append(f"\t\tdataType: {data_type_tmdl}\n")
                if format_string and format_string != "none":
                     insert_lines.append(f"\t\tformatString: {format_string}\n")
                insert_lines.append(f"\t\tlineageTag: {str(uuid.uuid4())}\n")
                insert_lines.append(f"\t\tsummarizeBy: {aggregation}\n")
                insert_lines.append(f"\t\tisDataTypeInferred\n") # Typically true for calculated columns
            
            insert_lines.append(f"\t\tannotation SummarizationSetBy = Automatic\n")
            # Add PBI_FormatHint if applicable
            if format_string and ("$" in format_string or "%" in format_string):
                 hint_type = "isCurrency" if "$" in format_string else "isPercentage"
                 insert_lines.append(f'\t\tannotation PBI_FormatHint = {{"{hint_type}":true}}\n')
            insert_lines.append("\n")


            if partition_index is not None:
                updated_lines = lines[:partition_index] + insert_lines + lines[partition_index:]
            else: # Append if no partition found (less ideal, but a fallback)
                updated_lines = lines + insert_lines
                logger.warning(f"Partition definition not found in {tmdl_file_path}. Appending DAX calculation.")

            with open(tmdl_file_path, 'w', encoding='utf-8') as tmdl_file:
                tmdl_file.writelines(updated_lines)
            logger.info(f"Updated {tmdl_file_path} with DAX object: {tmdl_dax_object_name}")
        else:
            logger.warning(f"Table file {table_name_unquoted}.tmdl not found in {tables_dir}. Skipping update for this table.")


def create_parameters_model_excel(tables_dir,datasource):
    parameters_table_name_unquoted = datasource.get('name') # e.g., "Parameters"
    parameters_table_name_quoted = f"'{parameters_table_name_unquoted}'" if re.search(r"[^a-zA-Z0-9_]", parameters_table_name_unquoted) or " " in parameters_table_name_unquoted else parameters_table_name_unquoted
    
    lineage_tag = str(uuid.uuid4())
    columns_elements = datasource.findall(".//column[@name]") # Corrected from 'columns'
    tmdl_file_path = os.path.join(tables_dir, f"{parameters_table_name_unquoted}.tmdl") # File name unquoted

    logger.info(f"Creating Parameters model: {tmdl_file_path}")
    with open(tmdl_file_path, 'w', encoding='utf-8') as models_tmdl:
        models_tmdl.write(f"table {parameters_table_name_quoted}\n") # Table name quoted
        models_tmdl.write(f"\tlineageTag: {lineage_tag}\n\n")
       
        for column_element in columns_elements: # Iterate through found column elements
            column_name_raw = column_element.get('name').strip('[]')
            # Parameter names are typically simple, but quote if necessary
            parameter_measure_name_quoted = f"'{column_name_raw}'" if re.search(r"[^a-zA-Z0-9_]", column_name_raw) or " " in column_name_raw else column_name_raw
            
            calculation_element = column_element.find('calculation')
            if calculation_element is not None and 'formula' in calculation_element.attrib:
                formula = calculation_element.get('formula')
                models_tmdl.write(f"\tmeasure {parameter_measure_name_quoted} = {formula}\n")
                models_tmdl.write(f"\t\tlineageTag: {str(uuid.uuid4())}\n")
                # Parameters often have specific format strings or data types in TWB,
                # which should be reflected here if possible (e.g., whole number, decimal, date)
                # For now, using a generic PBI_FormatHint.
                # A dataType: and formatString: could be added if known.
                # Example: models_tmdl.write(f"\t\tdataType: double\n")
                # Example: models_tmdl.write(f"\t\tformatString: 0.00\n")
                models_tmdl.write(f'\t\tannotation PBI_FormatHint = {{"isGeneralNumber":true}}\n\n') 
            else:
                logger.warning(f"Parameter '{column_name_raw}' in '{parameters_table_name_unquoted}' is missing calculation/formula.")
 
        # Partition for Parameters table (usually an empty table or a single row for context)
        models_tmdl.write(f"\tpartition {parameters_table_name_quoted} = m\n") # Partition name quoted
        models_tmdl.write(f"\t\tmode: import\n")
        models_tmdl.write(f"\t\tsource =\n")
        models_tmdl.write(f"\t\t\tlet\n")
        # M-query for an empty table or a table with one row, as parameters don't load data
        models_tmdl.write('\t\t\t\tSource = #table(type table[], {{}})\n') # Creates an empty table
        models_tmdl.write(f"\t\t\tin\n")
        models_tmdl.write('\t\t\t\t\tSource\n\n')
        models_tmdl.write(f"\tannotation PBI_NavigationStepName = Navigation\n") # Or remove if not applicable
        models_tmdl.write(f"\tannotation PBI_ResultType = Table\n")
    logger.info(f"Finished creating Parameters model: {tmdl_file_path}")

def extract_table_and_column_definitions_for_excel(
    twb_root: ET.Element,
    unique_table_lineage_tags: dict, 
    create_date_tables: bool = False, # This parameter seems unused in the provided snippet
) -> tuple[dict, dict]: # The second dict for variant_relationships seems unused too
    log_prefix = f" - excel Table/Col Extractor - " 
    table_columns_map = {}
    # variant_relationships_map_for_date_tables is not populated in this version of the function
    variant_relationships_map_for_date_tables = {} 

    logger.info(f"{log_prefix}Starting extraction from TWB metadata-records.")

    metadata_records = twb_root.findall(".//metadata-record[@class='column']")
    if not metadata_records:
        logger.warning(f"{log_prefix}No metadata-records with class 'column' found in TWB.")
        return table_columns_map, variant_relationships_map_for_date_tables # Return empty

    for metadata_record_node in metadata_records:
        twb_table_name_raw = metadata_record_node.findtext("parent-name")
        
        twb_column_name_raw = metadata_record_node.findtext("remote-name")
        if not twb_column_name_raw: # Fallback to local-name
            twb_column_name_raw = metadata_record_node.findtext("local-name")
        
        # twb_aggregation_hint = metadata_record_node.findtext("aggregation", "None") 
        twb_local_type = metadata_record_node.findtext("local-type", "string") 

        if not twb_table_name_raw or not twb_column_name_raw:
            logger.warning(f"{log_prefix}Skipping a metadata-record due to missing parent-name or column-name.")
            continue

        tmdl_table_name = twb_table_name_raw.strip("[]") 

        if tmdl_table_name not in unique_table_lineage_tags:
            unique_table_lineage_tags[tmdl_table_name] = str(uuid.uuid4())
        
        table_columns_map.setdefault(tmdl_table_name, [])

        column_lineage_tag_guid = str(uuid.uuid4())

        # --- TMDL Data Type and Property Mapping ---
        tmdl_dataType = "string"    
        tmdl_formatString = "none"  
        tmdl_summarizeBy = "None"   
        underlying_data_type_hint = None

        if twb_local_type == "integer":
            tmdl_dataType = "int64"
            tmdl_formatString = "0" 
            tmdl_summarizeBy = "Sum"
        elif twb_local_type == "real": 
            tmdl_dataType = "double" 
            tmdl_formatString = "0.00" 
            tmdl_summarizeBy = "Sum"
        elif twb_local_type == "string":
            tmdl_dataType = "string"
            tmdl_formatString = "none" 
            tmdl_summarizeBy = "Count"
        elif twb_local_type == "date":
            tmdl_dataType = "dateTime" 
            tmdl_formatString = "Long Date" 
            tmdl_summarizeBy = "None"
            underlying_data_type_hint = "Date"
        elif twb_local_type == "datetime":
            tmdl_dataType = "dateTime"
            tmdl_formatString = "General Date" 
            tmdl_summarizeBy = "None"
            underlying_data_type_hint = "DateTime"
        elif twb_local_type == "boolean":
            tmdl_dataType = "boolean"
            tmdl_formatString = "none" 
            tmdl_summarizeBy = "Count"
        else: 
            logger.warning(f"{log_prefix}Unknown TWB local-type '{twb_local_type}' for column '{twb_column_name_raw}' in table '{tmdl_table_name}'. Defaulting to string.")
            tmdl_dataType = "string"
            tmdl_formatString = "none"
            tmdl_summarizeBy = "Count"
        
        column_name_cleaned_for_source = twb_column_name_raw.strip("[]")
        tmdl_column_definition_name = f"'{column_name_cleaned_for_source}'" if re.search(r"[^a-zA-Z0-9_]", column_name_cleaned_for_source) or " " in column_name_cleaned_for_source else column_name_cleaned_for_source
        tmdl_source_column_name = tmdl_column_definition_name # For Excel, sourceColumn is usually the same as the (quoted) definition name

        column_definition_data = {
            "name": tmdl_column_definition_name,    
            "dataType": tmdl_dataType,
            "formatString": str(tmdl_formatString), 
            "lineageTag": column_lineage_tag_guid,
            "summarizeBy": tmdl_summarizeBy,
            "sourceColumn": tmdl_source_column_name, 
            "isDataTypeInferred": True, # Common for Excel sources
            # "annotation": "SummarizationSetBy = Automatic", # Will be handled by "annotations" list
        }
        
        # Use "annotations" list for better structure
        column_definition_data["annotations"] = [{"name": "SummarizationSetBy", "value": "Automatic"}]
        if underlying_data_type_hint:
            column_definition_data["annotations"].append({"name": "UnderlyingDateTimeDataType", "value": underlying_data_type_hint})
            # Add PBI_FormatHint for date/datetime columns if needed for variation display
            column_definition_data["annotations"].append({"name": "PBI_FormatHint", "value": '{"isDateTimeCustom":true}'})


        # The create_date_tables logic was part of extract_table_columns_excel before,
        # if it's still needed for this specific "for_excel" variant, it should be here.
        # However, the current function signature and usage pattern suggest it might be simpler.
        # If LocalDateTables are created, their relationships would be added to a shared list.

        table_columns_map[tmdl_table_name].append(column_definition_data)

    logger.info(f"{log_prefix}Finished extracting definitions for {len(table_columns_map)} table(s).")
    return table_columns_map, variant_relationships_map_for_date_tables # variant_relationships is empty here
