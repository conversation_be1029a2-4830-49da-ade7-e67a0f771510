"""test db

Revision ID: e62ac6129e99
Revises: 
Create Date: 2025-04-01 11:43:08.421726

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e62ac6129e99'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('server_details',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('server_name', sa.String(length=100), nullable=False),
    sa.Column('server_url', sa.String(length=100), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='serverstatus'), nullable=True),
    sa.Column('server_type', sa.Enum('ONPREMISE', 'CLOUD', name='servertype'), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('updated_by', sa.Integer(), nullable=False),
    sa.Column('last_modified', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('cloud_server',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('server_id', sa.UUID(), nullable=False),
    sa.Column('pat_name', sa.String(length=100), nullable=True),
    sa.Column('pat_secret', sa.String(length=100), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('updated_by', sa.Integer(), nullable=False),
    sa.Column('last_modified', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['server_id'], ['server_details.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('on_premise_server',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('server_id', sa.UUID(), nullable=False),
    sa.Column('server_auth_type', sa.Enum('CREDENTIALS', 'PAT', name='serverauthtype'), nullable=False),
    sa.Column('username', sa.String(length=100), nullable=True),
    sa.Column('password', sa.String(length=100), nullable=True),
    sa.Column('pat_name', sa.String(length=100), nullable=True),
    sa.Column('pat_secret', sa.String(length=100), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('updated_by', sa.Integer(), nullable=False),
    sa.Column('last_modified', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['server_id'], ['server_details.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('on_premise_server')
    op.drop_table('cloud_server')
    op.drop_table('server_details')
    # ### end Alembic commands ###
