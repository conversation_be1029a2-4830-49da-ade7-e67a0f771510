import os
import uuid
import shutil
import zipfile
from .datasources_handler import process_tableau_datasources

def store_and_prepare_semantic_model_env(twb_file, pbi_env_file):

    base_storage_dir = './storage'
    unique_id = str(uuid.uuid4().hex[:20])
    unique_folder_path = os.path.join(base_storage_dir, 'semantic_model_test', unique_id)
    os.makedirs(unique_folder_path, exist_ok=True)

    twb_folder_path = os.path.join(unique_folder_path, 'twb_files')
    os.makedirs(twb_folder_path, exist_ok=True)

    pbi_env_folder_path = os.path.join(unique_folder_path, 'pbi_env_files')
    os.makedirs(pbi_env_folder_path, exist_ok=True)

    twb_file_path = os.path.join(twb_folder_path, twb_file.filename)
    pbi_env_file_path = os.path.join(pbi_env_folder_path, pbi_env_file.filename)
    pbi_extract_dir = os.path.join(pbi_env_folder_path, os.path.splitext(pbi_env_file.filename)[0])

    with open(twb_file_path, "wb") as twb_buffer:
        shutil.copyfileobj(twb_file.file, twb_buffer)

    with open(pbi_env_file_path, "wb") as pbi_buffer:
        shutil.copyfileobj(pbi_env_file.file, pbi_buffer)

    with zipfile.ZipFile(pbi_env_file_path, 'r') as powerbi_zip:
        powerbi_zip.extractall(pbi_extract_dir)

    powerbi_folder_path = [f.path for f in os.scandir(pbi_extract_dir) if f.is_dir()]
    powerbi_folder_path = powerbi_folder_path[0] if len(powerbi_folder_path) == 1 else pbi_extract_dir 
    return twb_file_path, powerbi_folder_path, unique_id
