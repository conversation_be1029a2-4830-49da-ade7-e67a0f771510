import re
from typing import List, Dict, Optional, Union
from app.core import logger
from app.core.enums import (
    GeneralKeys as GS,
    Datasource as DS
)

from app.core.regex_enums import Regex as RE

def calculate_fields(root) -> List[Dict[str, str]]:
    """
    Extracts calculated fields from the XML root element.

    Args:
        root: XML root element.

    Returns:
        A list of calculations extracted from the XML.
    """
    datasources_element = root.find(DS.DSS.value)
    if datasources_element is None:
        logger.warning("Datasources element not found in XML root.")
        return []

    calculations_list = []
    for datasource in datasources_element.findall(GS.Datasource.value):
        calculations_list.extend(extract_calculations_from_datasource(datasource))

    if not calculations_list:
        logger.info("No calculation fields were found in any datasource.")
        return []

    return process_calculations_file(calculations_list)

def extract_calculations_from_datasource(datasource) -> List[Dict[str, str]]:
    """
    Helper function to extract calculations from a single datasource.

    Args:
        datasource: XML element representing a datasource.

    Returns:
        List of extracted calculation dictionaries.
    """
    calculations = []
    for column in datasource.findall(DS.COLUMN.value):
        formula_element = column.find(DS.CALC.value)
        if formula_element is not None and DS.FORMULA.value in formula_element.attrib:
            calculations.append({
                DS.NAME.value: column.get(DS.NAME.value, GS.UNKNOWN.value),
                DS.CAPTION.value: column.get(DS.CAPTION.value, column.get(DS.NAME.value, GS.UNKNOWN.value)),
                DS.FORMULA.value: formula_element.get(DS.FORMULA.value, "")
            })
    return calculations

def resolve_dependencies(calculations: List[Dict[str, str]]) -> List[Dict[str, str]]:
    """
    Resolves formulas by replacing internal name references with their respective captions.

    Args:
        calculations: List of calculations containing formula strings.

    Returns:
        Updated calculations with resolved formulas.
    """
    if not calculations:
        logger.info("resolve_dependencies: No calculations provided.")
        return []

    name_to_caption = name_to_caption_map(calculations)
    pattern = RE.BETWEEN_SQUARE_BRACKETS.value
    def resolve_formula(formula: str) -> str:
        if not formula:
            logger.warning("resolve_formula: Encountered empty formula string.")
            return "[Unresolved: Empty Formula]"

        for match in re.findall(pattern, formula):
            match = match.strip("[]")
            caption = name_to_caption.get(match)
            if caption:
                formula = formula.replace(match, f"[{caption}]")
        return formula

    return [
        {
            DS.NAME.value: calc[DS.NAME.value],
            DS.CAPTION.value: calc.get(DS.CAPTION.value, calc[DS.NAME.value]),
            DS.FORMULA.value: resolve_formula(calc.get(DS.FORMULA.value))
        }
        for calc in calculations
    ]


def name_to_caption_map(items: List[Dict[str, str]]) -> Dict[str, str]:
    """
    Utility to build a name-to-caption mapping from a list of dicts.
    """
    return {
        item.get(DS.NAME.value, "").strip("[]"): item.get(DS.CAPTION.value, "").strip()
        for item in items
        if item.get(DS.NAME.value) and item.get(DS.CAPTION.value)
    }


def remove_parameters(calculations: List[Dict[str, str]]) -> List[Dict[str, str]]:
    """
    Removes '[Parameters].' references from each formula in the list.

    Args:
        calculations: List of calculation dictionaries.

    Returns:
        Modified list with cleaned formulas.
    """
    for calc in calculations:
        formula = calc.get(DS.FORMULA.value)
        if isinstance(formula, str):
            cleaned_formula = re.sub(r'\[Parameters\]\.', '', formula)
            calc[DS.FORMULA.value] = cleaned_formula
            logger.debug(f"Removed '[Parameters].' from formula: {formula} -> {cleaned_formula}")
    return calculations


def replace_names_with_captions(formula: str, datasource_json: Dict) -> str:
    """
    Replaces field names in a formula with corresponding captions using datasource JSON.

    Args:
        formula: Formula string containing field names in square brackets.
        datasource_json: JSON structure containing metadata.

    Returns:
        Formula string with names replaced by captions.
    """
    if not isinstance(formula, str):
        logger.warning(f"replace_names_with_captions: Invalid formula type: {type(formula)}")
        return formula

    try:
        fields_in_formula = re.findall(RE.BETWEEN_SQUARE_BRACKETS.value, formula)
    except re.error as regex_error:
        logger.error(f"Regex error in replace_names_with_captions: {regex_error}")
        return formula

    name_to_caption = name_to_caption_map_from_json(datasource_json)

    for field in fields_in_formula:
        name = field.strip("[]")
        caption = name_to_caption.get(name)
        if caption:
            formula = formula.replace(f"[{name}]", f"[{caption}]")

    return formula


def name_to_caption_map_from_json(datasource_json: Dict) -> Dict[str, str]:
    """
    Helper to build a name-to-caption map from datasource JSON.
    """
    name_to_caption = {}
    ds_data = datasource_json.get(GS.Datasources.value, {})

    for table in ds_data.get("Tables and Columns", []):
        for column in table.get("Columns and Data Types", []):
            name = column.get(DS.NAME.value, "").strip("[]")
            caption = column.get(DS.CAPTION.value, "").strip()
            if name and caption:
                name_to_caption[name] = caption

    for calc in ds_data.get("Calculations", []):
        name = calc.get(DS.NAME.value, "").strip("[]")
        caption = calc.get(DS.CAPTION.value, "").strip()
        if name and caption:
            name_to_caption[name] = caption

    return name_to_caption


def process_calculations_file(calculations: List[Dict[str, str]], datasource_json: Optional[Dict] = None) -> List[Dict[str, str]]:
    """
    Processes calculation formulas: resolves dependencies, removes parameters, and optionally replaces names with captions.

    Args:
        calculations: List of raw calculations.
        datasource_json: Optional JSON for name-to-caption replacement.

    Returns:
        List of processed calculations.
    """
    logger.info("Processing calculation formulas.")
    resolved = resolve_dependencies(calculations)
    cleaned = remove_parameters(resolved)

    if not datasource_json:
        return cleaned

    updated = []
    for calc in cleaned:
        formula = calc.get(DS.FORMULA.value)
        if not formula:
            logger.warning(f"process_calculations_file: Empty formula for calculation {calc.get(DS.NAME.value, GS.UNKNOWN.value)}")
            updated.append(calc)
            continue
        try:
            calc[DS.FORMULA.value] = replace_names_with_captions(formula, datasource_json)
        except re.error as err:
            logger.error(f"Regex error in replace_names_with_captions: {err}")
        updated.append(calc)

    return updated


def get_final_formula(formula: str, calculations_json: List[Dict[str, str]]) -> str:
    """
    Recursively resolves formula references to get a fully expanded formula string.

    Args:
        formula: The input formula string.
        calculations_json: List of available calculations.

    Returns:
        The final resolved formula string.
    """
    if not isinstance(formula, str):
        logger.warning("get_final_formula: Formula is not a valid string.")
        return "[Invalid Formula]"

    name_to_formula = {
        calc[DS.NAME.value]: calc[DS.FORMULA.value] for calc in calculations_json
    }

    pattern = RE.BETWEEN_SQUARE_BRACKETS.value

    def resolve(formula_str: str) -> str:
        matches = re.findall(pattern, formula_str)
        for match in matches:
            subformula = name_to_formula.get(match)
            if subformula:
                formula_str = formula_str.replace(match, resolve(subformula))
        return formula_str

    try:
        return resolve(formula)
    except Exception as e:
        logger.error(f"get_final_formula: Error resolving formula '{formula}': {e}")
        return "[Error Resolving Formula]"
