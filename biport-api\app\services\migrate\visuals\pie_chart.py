import uuid, json
from ..core import get_background_config, get_from_list, get_queryref, get_calc_filter_column, get_select_json, get_title_config, get_border_config
from app.core import piechart_json
from app.core import logger
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    generate_projections_data,
    extract_multiple_encodings_data
)
from app.core.enums import (
    ChartType, PowerBITemplateKeys, TableauXMLTags,
    PowerBIReportKeys, PowerBIChartTypes, VisualRequest
)


def get_piechart_report(pane_encodings, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style):
    try:
        overall_piechart_result = []
        tables_list, select_list = [], []

        select_queryref_dict = {}
        pie_category = pane_encodings.get('color',{}).get('@column')
        pie_y = pane_encodings.get('text',{}).get('@column')

        y_filter_value, y_col_value, y_table_name = get_calc_filter_column(pie_y, table_column_data, datasource_column_list)
        category_filter_value, category_col_value, category_table_name = get_calc_filter_column(pie_category, table_column_data, datasource_column_list)

        if y_table_name not in tables_list: tables_list.append(y_table_name)
        if category_table_name not in tables_list: tables_list.append(category_table_name)

        y_queryref = get_queryref(y_filter_value, y_col_value, y_table_name)
        category_queryref = get_queryref(category_filter_value, category_col_value, category_table_name)
        if y_queryref not in select_queryref_dict: select_queryref_dict[y_queryref] = [y_col_value, y_filter_value, y_table_name, y_queryref]
        if category_queryref not in select_queryref_dict: select_queryref_dict[category_queryref] = [category_col_value, category_filter_value, category_table_name, category_queryref]

        from_list = get_from_list(tables_list)

        for data in select_queryref_dict.values():
            select_list.append(get_select_json(data[0], data[1], data[2], data[3]))

        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style = style)

        piechart_json_data = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "category_queryref" : category_queryref,
            "y_queryref" : y_queryref,
            "from_list" : json.dumps(from_list),
            "select_list" : json.dumps(select_list),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
            
        }
        overall_piechart_result.append({"config":piechart_json_data, "template": piechart_json})
        return overall_piechart_result
    except Exception as e:
        logger.error(f"---Error in generating pie chart visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating pie chart visual for {worksheet_name} - {str(e)}")


def process_pie_chart_report(request: VisualRequest):
    pie_chart_result = {}

    panes = request.panes
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data
    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )

    unique_text_data = remove_duplicate_fields(encodings[TableauXMLTags.TEXT.value])
    unique_color_data = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])

    piechart_field_mapping = {
        PowerBIReportKeys.CATEGORY.value: unique_color_data,
        PowerBIReportKeys.Y.value: unique_text_data,
    }


    projections_data = generate_projections_data(table_column_data, calculations_related_data, piechart_field_mapping)

    pie_chart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.PIE.value
    pie_chart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    pie_chart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data

    return pie_chart_result
