import json
import os
from .migration_service import MigrationService
from .process_files import process_files_for_migration
from app.core import logger
from app.services import (
     decode_secure_jwt,
     decode_base
)
from app.schemas.migrate import MigarationInput, SingleReportMigrationInput
from app.core import (
    <PERSON><PERSON><PERSON>ponse,
    LOCAL_DOWNLOAD_PATH,
    S3_INPUT_PATH,
    LOCAL_WORKBOOKS_DOWNLOAD_PATH,
    S3_WORKBOOKS_PATH,
    HTTP_STATUS_OK,
    LOCAL_S3_FILES_DOWNLOAD_PATH,
    WOR<PERSON><PERSON>OKS_PATH,
    MIGRATE_OUTPUT_DIR,
    MIGRATE_S3_ZIP_PATH,
    POWER_BI_STRUCTURE
)
from app.core.config import S3Config
from app.core.exceptions import *
from pathlib import Path
from typing import Optional, List
import shutil
from starlette.concurrency import run_in_threadpool
from app.models_old.reports import ReportDetailsManager
from app.models_old.upload_file_report_details import UploadFilesReportManager
from app.models.report_details import ReportDetailManager
from app.models.users import User
from app.core.constants import MIGRATE_OUTPUTS_PATH, R<PERSON><PERSON>T_PATH


def parse_authorization_token(
        authorization: str,
        email: str
):
    """
    Parses the authorization token and returns
    the decoded email and JWT payload.

    Parameters
    ----------
    authorization : str
        Authorization header in the format "Bearer <token>".
    email : str
        User's email used for decoding the token.

    Returns
    -------
    A tuple containing the decoded email and the token payload.
    """
    try:
        token = authorization.split(" ")[1]
        decoded_email = decode_base(email)
        payload = decode_secure_jwt(token, decoded_email)
        return decoded_email, payload
    except Exception as e:
        logger.error(f"Error parsing authorization token: {e}")
        raise ValueError("Invalid authorization token")


class MigrateProcessor:
    """ All migration operations processed here"""

    @staticmethod
    async def migrate_single_report(report_id, user: User) -> ServiceResponse:
        """
        Migration of a single report from Tableau to Power BI using the new report-based approach.

        Parameters:
        - report_id: UUID of the report to migrate
        - user: Authenticated user object

        Returns:
        - ServiceResponse with migration result and download link
        """
        logger.info(f"[Migration Processor] Starting single report migration for report_id: {report_id}, user: {user.email}")

        try:
            # Get report details
            report_detail = ReportDetailManager.get_report_by_id(report_id)
            if not report_detail:
                raise HTTPException(status_code=404, detail=f"Report {report_id} not found")

            report_name = report_detail.name
            s3_report_id = report_detail.report_id
            organization_name = user.organization.name

            logger.info(f"[Migration Processor] Report details - name: {report_name}, s3_report_id: {s3_report_id}")

            # Set up S3 configuration
            s3config = S3Config()

            # Construct S3 paths using new constants
            input_path = REPORT_PATH.format(
                organization_name=organization_name,
                s3_report_id=s3_report_id
            )

            # Try both .twb and .twbx extensions
            input_path_twb = f"{input_path}/{report_name}.twb"
            input_path_twbx = f"{input_path}/{report_name}.twbx"

            output_path = MIGRATE_OUTPUTS_PATH.format(
                organization_name=organization_name,
                s3_report_id=s3_report_id
            )

            logger.info(f"[Migration Processor] S3 paths - input: {input_path}, output: {output_path}")

            # Set up local directories
            local_download_path = Path(LOCAL_WORKBOOKS_DOWNLOAD_PATH.format(workbook_id=str(report_id)))
            local_download_path.mkdir(parents=True, exist_ok=True)

            # Download TWB files from S3 - try both .twb and .twbx
            logger.info(f"[Migration Processor] Downloading TWB files from S3")
            twb_files = []

            # Try .twb first
            try:
                logger.info(f"[Migration Processor] Trying to download: {input_path_twb}")
                twb_files = await s3config.download_twb_file_from_s3(input_path_twb, str(local_download_path))
            except Exception as e:
                logger.info(f"[Migration Processor] .twb file not found, trying .twbx: {str(e)}")

            # If .twb not found, try .twbx
            if not twb_files:
                try:
                    logger.info(f"[Migration Processor] Trying to download: {input_path_twbx}")
                    twb_files = await s3config.download_twb_file_from_s3(input_path_twbx, str(local_download_path))
                except Exception as e:
                    logger.error(f"[Migration Processor] .twbx file also not found: {str(e)}")

            if not twb_files:
                logger.error(f"[Migration Processor] No TWB/TWBX files found for report_id: {report_id}")
                ReportDetailManager.mark_migrated(str(report_id), "FAILURE", "No TWB/TWBX files found")
                raise HTTPException(status_code=404, detail="No TWB/TWBX files found for this report")

            # Set up Power BI structure path
            power_bi_structure = POWER_BI_STRUCTURE.format(workbook_name=report_name)

            # Create migration input
            migrationinput = SingleReportMigrationInput(
                s3_input_path=input_path,
                local_download_path=str(local_download_path),
                twb_files=twb_files,
                powerbi_structure=power_bi_structure
            )

            # Execute migration
            logger.info(f"[Migration Processor] Executing migration service")
            custom_zip_name = f"{report_name}_migrated.zip"
            result = MigrationService.execute(
                MigrationService().tableau_to_powerbi,
                migrationinput,
                custom_zip_name=custom_zip_name
            )

            # Mark as migrated on success
            logger.info(f"[Migration Processor] Migration successful, updating database status")
            ReportDetailManager.mark_migrated(str(report_id), "SUCCESS", "Migration completed successfully")

            # Clean up local files
            try:
                shutil.rmtree(local_download_path)
                logger.info(f"[Migration Processor] Cleaned up local directory: {local_download_path}")
            except Exception as e:
                logger.warning(f"[Migration Processor] Failed to clean up local files: {e}")

            # Prepare response data
            report_path = ReportDetailManager.get_report_hierarchy_path(report_detail.project_id, report_name)

            # Extract download URL from result
            download_url = ""
            if result and result.data:
                if isinstance(result.data, list) and len(result.data) > 0:
                    download_url = result.data[0].get("download_url", "")
                elif isinstance(result.data, dict):
                    download_url = result.data.get("download_url", "")

            response_data = {
                "report_id": str(s3_report_id),
                "report_name": report_name,
                "download_url": download_url,
                "report_path": report_path,
                "migrated_status": '{"status": "SUCCESS", "message": "Migration completed successfully"}'
            }

            logger.info(f"[Migration Processor] Single report migration completed successfully for report_id: {report_id}")
            return ServiceResponse(data=response_data, status_code=HTTP_STATUS_OK, error=None)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"[Migration Processor] Migration failed for report_id: {report_id}, error: {str(e)}", exc_info=True)
            # Mark as failed
            try:
                ReportDetailManager.mark_migrated(str(report_id), "FAILURE", f"Migration failed: {str(e)}")
            except Exception as mark_err:
                logger.warning(f"[Migration Processor] Failed to mark report as failed: {str(mark_err)}")

            raise HTTPException(status_code=500, detail=f"Migration failed: {str(e)}")
    @staticmethod
    async def tableau_to_powerbi_legacy(
         authorization: str,
         email: str,
         process_id: str,
    ) -> ServiceResponse:
        """ Migration of tableau files to powerBI """

        s3config = S3Config()
        logger.debug(f"/tableau-to-powerbi initiated for process_id: {process_id}, authorization: {authorization}, email: {email}")
        decoded_email, payload = parse_authorization_token(
             authorization,
             email
        )
        organization_name = payload.get('organizationName')
        user_email = payload.get('email')
        local_download_path = LOCAL_DOWNLOAD_PATH.format(
             organization_name=organization_name,
             user_email=user_email,
             process_id=process_id
        )
        os.makedirs(local_download_path,exist_ok=True)
        s3_input_path = S3_INPUT_PATH.format(
             organization_name = organization_name, 
             user_email = user_email,
             process_id = process_id
        )
        twb_files = await s3config.get_twb_files(
             s3_input_path,
             local_download_path
        )
        migrationinput = MigarationInput(
            decoded_email = decoded_email,
            payload = payload,
            organization_name = organization_name,
            user_email = user_email,
            s3_input_path = s3_input_path,
            local_download_path = local_download_path,
            twb_files_count = len(twb_files),
            twb_files = twb_files,
            process_id = process_id
        )
        return MigrationService.execute(MigrationService().tableau_to_powerbi,migrationinput)


    @staticmethod
    async def tableau_to_powerbi(
        workbooks: Optional[List[dict]] = None,
        s3_paths: Optional[List[str]] = None,
        is_upload: bool = False,
    ) -> ServiceResponse:
        """ Migration of tableau files to powerBI """
        s3config = S3Config()
        migration_results = []
        if is_upload:
            for s3_item in s3_paths:
                s3_path = s3_item.s3_path
                workbook_name = s3_item.workbook_name
                report = await run_in_threadpool(UploadFilesReportManager.get_by_s3_path, s3_path)
                if not report:
                    raise HTTPException(status_code=404, detail=f" S3 path '{s3_path}' not found in UploadFilesReportManager")
                custom_zip_name = f"{workbook_name}_pbi.zip"
                if report.is_migrated:
                    parent_folder = os.path.dirname(s3_path)
                    object_key = f"{parent_folder}/{MIGRATE_OUTPUT_DIR}/{custom_zip_name}"
                    download_link = await s3config.generate_presigned_url(object_key = object_key)
                    migration_results.append({
                        "s3_path": s3_path,
                        "status": "Success",
                        "data": {"file": custom_zip_name, "download_url": download_link}
                    })
                else:
                    workbook_id = Path(s3_path).stem
                    local_download_path = Path(LOCAL_S3_FILES_DOWNLOAD_PATH.format(workbook_id=s3_path))
                    local_download_path.mkdir(parents=True, exist_ok=True)
                    twb_files = await s3config.download_twb_file_from_s3(s3_path, str(local_download_path))
                    power_bi_structure = POWER_BI_STRUCTURE.format(workbook_name=workbook_name)
                    migrationinput = MigarationInput(
                        s3_input_path=s3_path,
                        local_download_path=str(local_download_path),
                        twb_files_count=len(twb_files),
                        twb_files=twb_files,
                        process_id=workbook_id,
                        powerbi_structure=power_bi_structure
                    )
                    result = await MigrationService.execute(MigrationService().tableau_to_powerbi, migrationinput, custom_zip_name=custom_zip_name)
                    migration_results.append({"s3_path": s3_path, "status": "Success", "data": result.data})
                    await run_in_threadpool(UploadFilesReportManager.mark_migrated, s3_path)
                    try:
                        shutil.rmtree(local_download_path)
                    except Exception as e:
                        print(f"Failed to delete local files at {local_download_path}: {e}")
        else:
            for workbook in workbooks:
                workbook_id = workbook.workbook_id
                workbook_name = workbook.workbook_name
                report = await run_in_threadpool(ReportDetailsManager.get_workbook_id, workbook_id)
                if not report: raise HTTPException(status_code=404, detail=f"Workbook ID '{workbook_id}' not found in report_details")
                custom_zip_name = f"{workbook_name}_pbi_{workbook_id[:8]}.zip"
                if report.is_migrated:
                    object_key = MIGRATE_S3_ZIP_PATH.format(WORKBOOKS_PATH = WORKBOOKS_PATH, 
                                                            MIGRATE_OUTPUT_DIR = MIGRATE_OUTPUT_DIR,
                                                            workbook_id = workbook_id)
                    object_key = os.path.join(os.path.dirname(object_key), custom_zip_name)
                    download_link = await s3config.generate_presigned_url(object_key = object_key)
                    migration_results.append({
                        "workbook_id": workbook_id,
                        "status": "Success",
                        "data": {"file": custom_zip_name, "download_url": download_link}
                    })
                else:
                    local_download_path = Path(LOCAL_WORKBOOKS_DOWNLOAD_PATH.format(workbook_id=workbook_id))
                    local_download_path.mkdir(parents=True, exist_ok=True)
                    s3_workbook_path = S3_WORKBOOKS_PATH.format(workbook_id=workbook_id)
                    power_bi_structure = POWER_BI_STRUCTURE.format(workbook_name=workbook_name)
                    twb_files = await s3config.download_twb_file_from_s3(
                        s3_workbook_path,
                        str(local_download_path)
                    )
                    migrationinput = MigarationInput(
                        s3_input_path=s3_workbook_path,
                        local_download_path=str(local_download_path),
                        twb_files_count=len(twb_files),
                        twb_files=twb_files,
                        process_id=workbook_id,
                        powerbi_structure=power_bi_structure
                    )
                    result = await MigrationService.execute(MigrationService().tableau_to_powerbi, migrationinput, custom_zip_name=custom_zip_name)
                    migration_results.append({"workbook_id": workbook_id, "status": "Success", "data": result.data})
                    await run_in_threadpool(ReportDetailsManager.mark_migrated, workbook_id)
        return ServiceResponse(data=migration_results, status_code=HTTP_STATUS_OK, error=None)