import uuid
from sqlalchemy import Column, DateTime, Enum, String, <PERSON>ole<PERSON>, Integer, ForeignKey, func, text, or_
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Query, joinedload, aliased
from app.core.session import scoped_context
from app.models.users import User
from app.core.session import Base, logger
from app.models.base import AuditMixin
from app.models.project_details import ProjectDetail

class ReportDetail(Base, AuditMixin):
    __tablename__ = "report_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    report_id = Column(UUID(as_uuid=True), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.project_details.id"), nullable=False)

    is_analyzed = Column(Boolean, server_default=text("false"))
    analyzed_status = Column(String, nullable=True)
    is_converted = Column(Boolean, server_default=text("false"))
    converted_status = Column(String, nullable=True)
    is_migrated = Column(Boolean, server_default=text("false"))
    migrated_status = Column(String, nullable=True)
    unit_tested = Column(Boolean, server_default=text("false"))
    uat_tested = Column(Boolean, server_default=text("false"))
    deployed = Column(Boolean, server_default=text("false"))
    is_scoped = Column(Boolean, server_default=text("false"))
    semantic_type = Column(String, nullable=True)
    has_semantic_model = Column(Boolean, server_default=text("false"))
    view_count = Column(Integer, server_default=text("0"))
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=True)
    is_deleted = Column(Boolean, server_default=text("false"))

    project = relationship("ProjectDetail", back_populates="reports")

    @staticmethod
    def get_reports_by_user_role(session, user, role_name: str) -> Query:
        query = session.query(ReportDetail).join(ProjectDetail, ReportDetail.project_id == ProjectDetail.id).options(joinedload(ReportDetail.project))

        Creator = aliased(User)

        if role_name == "Admin":
            pass
        elif role_name == "Manager":
            subordinate_ids = session.query(User.id).filter(User.manager_id == user.id).all()
            subordinate_ids = [sid[0] for sid in subordinate_ids]
            query = query.filter(
                or_(
                    ProjectDetail.assigned_to == user.id,
                    ProjectDetail.assigned_to.in_(subordinate_ids)
                )
            )
        elif role_name == "Developer":
            query = query.filter(ProjectDetail.assigned_to == user.id)

        query = query.join(Creator, ProjectDetail.creator).filter(
            Creator.organization_id == user.organization_id
        )

        return query.distinct()

class ReportDetailManager:
    @staticmethod
    def add_report(id, name, report_id, project_id, created_by=None, updated_by=None):
        from app.models.report_details import ReportDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            report = ReportDetail(
                id=id,
                name=name,
                report_id=report_id,
                project_id=project_id,
                created_by=created_by,
                updated_by=updated_by
            )
            session.add(report)
            session.commit()
            session.refresh(report)
            return report

    @staticmethod
    def soft_delete_by_project_ids(project_ids):
        if not project_ids:
            return
        from app.models.report_details import ReportDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            session.query(ReportDetail).filter(ReportDetail.project_id.in_(project_ids)).update({"is_deleted": True}, synchronize_session=False)
            session.commit()

    @staticmethod
    def get_report_by_id(report_id: str) -> ReportDetail:
        """
        Retrieve a ReportDetails record by workbook ID.

        Parameters
        ----------
            The ID of the workbook to retrieve.

        Returns
        -------
            The ReportDetails instance if found, otherwise None.
        """
        try:
            with scoped_context() as session:
                return session.query(ReportDetail).filter(ReportDetail.report_id == report_id).first()
        except Exception as e:
            logger.exception(f"{report_id} ID not found in database : {e}")
            return None
        

      
    @staticmethod
    def get_report_hierarchy_path(project_id: UUID, report_name: str) -> str:
        """
        Recursively builds the project path from root to leaf using parent_id chain,
        and appends the report name at the end.
        Example output: "RootFolder/SubFolder/ProjectName/ReportName"
        """
        try:
            with scoped_context() as session:
                path_parts = []
                current_project = session.query(ProjectDetail).filter(ProjectDetail.id == project_id).first()
                while current_project:
                    path_parts.insert(0, current_project.name)
                    if not current_project.parent_id:
                        break
                    current_project = session.query(ProjectDetail).filter(ProjectDetail.id == current_project.parent_id).first()

                # Append report name at the end
                if report_name:
                    path_parts.append(report_name.strip())

                return "/".join(path_parts)
        except Exception as e:
            logger.exception(f"Failed to build project path for project_id {project_id}: {e}")
            return ""


    @staticmethod
    def mark_analyzed(report_id: str, status: str = "SUCCESS", message: str = ""):
        """
        Marks a report as analyzed and sets the analyzed_status field as JSON string.

        Parameters:
        - report_id (UUID): ID of the report (report_id field)
        - status (str): One of ["SUCCESS", "FAILURE", "ERROR"]
        - message (str): Optional message (especially for error cases)
        """
        try:
            with scoped_context() as session:
                db_report = session.query(ReportDetail).filter(ReportDetail.report_id == report_id).first()
                if db_report:
                    db_report.is_analyzed = (status == "SUCCESS")
                    # Store JSON string format as requested
                    db_report.analyzed_status = f'{{"status": "{status.upper()}", "message": "{message}"}}'
                    session.commit()
                    logger.info(f"Report {report_id} marked as analyzed with status: {db_report.analyzed_status}")
                else:
                    logger.warning(f"Report {report_id} not found for marking as analyzed")
        except Exception as e:
            logger.exception(f"Failed to update analyzed status for report {report_id}: {e}")

       
    @staticmethod  
    def mark_converted(report_id: str, status: str = "SUCCESS", message: str = ""):
        """
        Marks a report as converted and sets the analyzed_status field as JSON string.

        Parameters:
        - report_id (UUID): ID of the report (report_id field)
        - status (str): One of ["SUCCESS", "FAILURE", "ERROR"]
        - message (str): Optional message (especially for error cases)
        """
        try:
            with scoped_context() as session:
                db_report = session.query(ReportDetail).filter(ReportDetail.report_id == report_id).first()
                if db_report:
                    db_report.is_converted = (status == "SUCCESS")
                    # Store JSON string format as requested
                    db_report.converted_status = f'{{"status": "{status.upper()}", "message": "{message}"}}'
                    session.commit()
                    logger.info(f"Report {report_id} marked as converted with status: {db_report.converted_status}")
                else:
                    logger.warning(f"Report {report_id} not found for marking as converted")
        except Exception as e:
            logger.exception(f"Failed to update analyzed status for report {report_id}: {e}")


    @staticmethod   
    def mark_migrated(report_id: str, status: str = "SUCCESS", message: str = ""):
        """
        Marks a report as migrated and sets the migrated_status field as JSON string.

        Parameters:
        - report_id (UUID): ID of the report (report_id field)
        - status (str): One of ["SUCCESS", "FAILURE", "ERROR"]
        - message (str): Optional message (especially for error cases)

        """
        try:
            with scoped_context() as session:
                db_report = session.query(ReportDetail).filter(ReportDetail.report_id == report_id).first()
                if db_report:
                    db_report.is_migrated = (status == "SUCCESS")
                    # Store JSON string format as requested
                    db_report.migrated_status = f'{{"status": "{status.upper()}", "message": "{message}"}}'
                    session.commit()
                    logger.info(f"Report {report_id} marked as migrated with status: {db_report.migrated_status}")
                else:
                    logger.warning(f"Report {report_id} not found for marking as migrated")
        except Exception as e:
            logger.exception(f"Failed to update migrated status for report {report_id}: {e}")