apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dev-biportv3-ingress
  namespace: dev-biport
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: prod-alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=1200
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-south-1:211125524251:certificate/7f606916-7952-4eb2-a47d-504a63b33168  # Replace with your ACM certificate ARN
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-2016-08  # Use stronger security policy if needed
spec:
  rules:
    - host: devbiportv3.sparity.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: dev-biport-web
                port:
                  number: 80
          - path: /app_api
            pathType: Prefix
            backend:
              service:
                name: dev-biport-api
                port:
                  number: 9090
