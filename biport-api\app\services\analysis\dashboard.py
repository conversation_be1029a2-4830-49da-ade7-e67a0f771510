import json
import re
from pathlib import Path
from lxml import etree

from app.core import logger
from app.core.enums import GeneralKeys as GS, Dashboard as DB, Datasource as DS
from app.core.regex_enums import Regex as RE
from app.core.enums import ComplexityLevel

def clean_column_name(column_name):
    """Clean column name by removing square brackets and dots."""
    logger.debug(f"Cleaning column name: {column_name}")
    return re.sub(RE.BETWEEN_SQUARE_BRACKETS_FOLLOWED_BY_DOT.value, RE.EMPTY.value, column_name)


def extract_styles(styles):
    """Extract style attributes from XML nodes."""
    return {style.get(GS.ATTR.value): style.get(GS.VALUE.value) for style in styles}


def extract_dependency_columns(dependency):
    """Extract columns and their calculations from dependency."""
    columns = []
    for column in dependency.findall(DB.COLUMN.value):
        column_data = dict(column.attrib)
        calc = column.find(GS.CALC.value)
        if calc is not None:
            column_data[GS.CALC.value] = dict(calc.attrib)
        columns.append(column_data)
    return columns


def extract_zones(dashboard):
    """Extract zones from dashboard."""
    zones = []
    processed = set()

    for zone in dashboard.findall(DB.ZONE.value):
        zone_type = zone.get(GS.TYPE2.value)
        if zone_type and GS.LAYOUT.value in zone_type:
            continue

        zone_data = dict(zone.attrib)
        if GS.PARAM.value in zone_data:
            zone_data[GS.PARAM.value] = clean_column_name(zone_data[GS.PARAM.value])

        zone_styles = zone.find(DB.ZONE_STYLE.value)
        if zone_styles is not None:
            zone_data[GS.STYLE.value] = extract_styles(zone_styles.findall('.//format'))

        view = zone.find(DB.VIEW.value)
        if view is not None:
            view_data = dict(view.attrib)
            encodings = zone.findall(DB.ENCODINGS.value)
            view_data[GS.ENCODINGS.value] = [
                {k: clean_column_name(v) if k == 'column' else v for k, v in encoding.attrib.items()}
                for encoding in encodings
            ]
            zone_data[GS.VIEW.value] = view_data

        serialized = json.dumps(zone_data, sort_keys=True)
        if serialized in processed:
            continue

        processed.add(serialized)
        zones.append(zone_data)

    return zones


def extract_dashboard_details(root):
    """Extracts all dashboard details from the XML root."""
    logger.info("Extracting dashboard details.")
    dashboards = []

    for dashboard in root.findall(DB.DASHBOARD.value):
        name = dashboard.get(DB.NAME.value) or GS.DEFAULT_DASHBOARD_NAME.value
        logger.debug(f"Processing dashboard: {name}")

        dashboard_data = {
            GS.NAME.value: name,
            GS.Datasource.value: [dict(ds.attrib) for ds in dashboard.findall(DS.DS.value)],
            GS.DS_DEPENDENTIES.value: [],
            GS.ZONES.value: [],
            GS.STYLES.value: [extract_styles(dashboard.findall(DB.STYLE_FORMAT.value))]
        }

        for dep in dashboard.findall(DB.DS_DEPENDENCIES.value):
            dep_data = dict(dep.attrib)
            dep_data[GS.COLUMNS.value] = extract_dependency_columns(dep)
            dashboard_data[GS.DS_DEPENDENTIES.value].append(dep_data)

        dashboard_data[GS.ZONES.value] = extract_zones(dashboard)
        dashboards.append(dashboard_data)

    logger.info("Dashboard details extraction completed.")
    return {"Dashboards": dashboards}


def extract_semantic_model(root, output_file_path, source_file=None):
    """Extract dashboard JSON structure from XML root and save it."""
    if source_file:
        logger.info(f"Processing parsed XML root from: {source_file}")

    try:
        data = extract_dashboard_details(root)
        with open(output_file_path, 'w') as f:
            json.dump(data, f, indent=4)
        logger.info(f"Successfully wrote extracted dashboard to {output_file_path}")

    except (IOError, etree.XMLSyntaxError) as e:
        logger.error(f"Failed writing dashboard data to {output_file_path}: {e}")


def parse_single_dashboard(file_path, output_path):
    """Parse a single dashboard file and write its JSON output."""
    try:
        tree = etree.parse(str(file_path))
        root = tree.getroot()
        output_file = output_path / GS.DASHBOARD_JSON.value
        extract_semantic_model(root, str(output_file), source_file=str(file_path))
    except etree.XMLSyntaxError as e:
        logger.error(f"XML parsing error in {file_path}: {e}")
    except IOError as e:
        logger.error(f"I/O error for {file_path}: {e}")


def process_dashboards(input_dir, output_dir, file_extension=GS.SUPPORTED_FILE_EXTENSION.value):
    """Main function to process dashboards in a directory."""
    logger.info("Starting dashboard processing.")
    input_path, output_path = Path(input_dir), Path(output_dir)

    if not input_path.exists():
        logger.error(f"Input directory '{input_dir}' does not exist.")
        return {"error": f"Directory '{input_dir}' does not exist."}
    if not output_path.exists():
        logger.error(f"Output directory '{output_dir}' does not exist.")
        return {"error": f"Directory '{output_dir}' does not exist."}

    for file in input_path.glob(f'*{file_extension}'):
        dashboard_folder = output_path / file.stem
        dashboard_folder.mkdir(parents=True, exist_ok=True)
        parse_single_dashboard(file, dashboard_folder)

    logger.info("Dashboard processing completed.")


def get_dashboard_worksheet_data(dashboard_data: dict, file_name: str) -> dict:
    """Map worksheet names to dashboards."""
    output_result = {file_name: {}}
    for dashboard in dashboard_data.get("Dashboards", []):
        dash_name = dashboard.get(GS.NAME.value, GS.DEFAULT_DASHBOARD_NAME.value)
        for zone in dashboard.get(GS.ZONES.value, []):
            if GS.TYPE2.value in zone:
                continue
            ws_name = zone.get(GS.NAME.value)
            if ws_name:
                output_result[file_name][ws_name] = dash_name
    return output_result


def determine_report_complexity(
    visuals_count, calculations_count, datasources_count, manual_migration_count
):
    """Evaluate report complexity based on defined thresholds."""
    if visuals_count <= 4 and manual_migration_count == 0 and datasources_count == 1 and calculations_count <= 10:
        return ComplexityLevel.SIMPLE.value

    medium = sum([
        4 < visuals_count <= 6,
        0 <= manual_migration_count <= 2,
        1 < datasources_count <= 3,
        10 < calculations_count < 20
    ])

    complex_ = sum([
        visuals_count > 6,
        manual_migration_count > 2,
        datasources_count > 3,
        calculations_count > 20
    ])

    if complex_ >= 2:
        return ComplexityLevel.COMPLEX.value
    elif medium >= 2:
        return ComplexityLevel.MEDIUM.value

    return ComplexityLevel.HIGHLY_COMPLEX.value
