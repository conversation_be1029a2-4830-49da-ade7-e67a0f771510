"""creating upload_file_report_details

Revision ID: a7532629829a
Revises: 83a079b0e077
Create Date: 2025-06-11 11:42:57.477973

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


revision: str = 'a7532629829a'
down_revision: Union[str, None] = '83a079b0e077'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'upload_files_report_details', 
        sa.Column('id', sa.String(length=36), primary_key=True,
                  default=sa.text("gen_random_uuid()") if op.get_context().dialect.name == 'postgresql' else None), 
        sa.Column('report_name', sa.String(), nullable=False),
        sa.Column('is_analyzed', sa.Bo<PERSON>an(), server_default=sa.text('false'), nullable=False),
        sa.Column('is_converted', sa.<PERSON>(), server_default=sa.text('false'), nullable=False),
        sa.Column('is_migrated', sa.<PERSON>(), server_default=sa.text('false'), nullable=False),
        sa.Column('server_id', sa.UUID(), sa.ForeignKey("server_details.id"), nullable=True),
        sa.Column('upload_file_path', sa.String(), nullable=False)
    )


def downgrade() -> None:
    op.drop_table('upload_files_report_details')
